Этап Роадмапа / Функциональный Блок Бэкенда
Этап Роадмапа / Функциональный Блок Бэкенда.1
Ключевой Тестируемый Функционал Бэкенда
Основные Методики и Примеры Тест-Кейсов для Бэкенда (API и Логика)
Пользовательские истории
Приемочные критерии
Сквозные примеры использования
Статус выполнения
Ответственный
Дата последнего обновления
Комментарий заказчика
Этап 1: Аутентификация и Управление Пользователями
Этап 1: Аутентификация и Управление Пользователями
API Регистрации Пользователей
POST /register: Успешная регистрация с валидными уникальными данными (201 Created).

POST /register: Попытка регистрации с существующим email/логином (409 Conflict).

POST /register: Попытка регистрации с невалидными данными (некорректный email, короткий пароль, пустые поля) (400 Bad Request с описанием ошибок).
1. Как новый пользователь, я хочу зарегистрироваться в системе, чтобы получить доступ к VPN-сервису.
2. Как пользователь, я хочу создать учетную запись с уникальным email и надежным паролем, чтобы защитить свои данные.
1. Система должна позволять регистрацию новых пользователей с уникальными email.
2. Система должна проверять валидность email и силу пароля.
3. Система должна выдавать понятные сообщения об ошибках при невалидных данных.
4. Система должна предотвращать регистрацию с уже существующим email.
5. Система должна создавать запись пользователя и возвращать идентификатор пользователя при успешной регистрации.
Успешный сценарий:
1. Пользователь Ivan отправляет POST-запрос на /register с данными: email: <EMAIL>, password: securePass123, name: Ivan Ivanov
2. Система проверяет уникальность email и валидность данных
3. Система создает пользователя в базе данных
4. Система возвращает код 201 Created с ID пользователя

Неуспешный сценарий:
1. Пользователь отправляет запрос с некорректным email
2. Система проверяет данные и определяет их невалидность
3. Система возвращает код 400 Bad Request с описанием ошибки












API Двухуровневой Аутентификации (Account + VPN Keys)
POST /auth/register: Регистрация устройства с device_id, получение account_id и JWT для веб-кабинета (201 Created).

POST /auth/activate: Активация нового устройства по activation_code (200 OK, возврат VPN ключей).

GET /auth/activation-code: Генерация activation_code для переноса между устройствами (200 OK).
1. Как зарегистрированный пользователь, я хочу войти в систему, чтобы управлять своим профилем и VPN-подключениями.
2. Как пользователь, я хочу получить токен доступа, чтобы использовать API системы.
3. Как пользователь, я хочу обновлять свои токены доступа, чтобы оставаться авторизованным.
1. Система должна аутентифицировать пользователя по email и паролю.
2. Система должна выдавать пару JWT токенов (access и refresh) при успешной авторизации.
3. Access-токен должен иметь ограниченное время жизни (60 минут).
4. Refresh-токен должен иметь более длительное время жизни (30 дней).
5. Система должна проверять валидность токенов при запросах к API.
6. Система должна позволять обновлять истекший access-токен с помощью действительного refresh-токена.
Успешный сценарий:
1. Пользователь Ivan отправляет POST-запрос на /login с данными: email: <EMAIL>, password: securePass123
2. Система проверяет учетные данные и аутентифицирует пользователя
3. Система генерирует JWT токены (access и refresh)
4. Система возвращает код 200 OK с токенами

Сценарий с истекшим токеном:
1. Ivan делает запрос с истекшим access-токеном
2. Система возвращает код 401 с сообщением "Токен истек"
3. Клиент отправляет запрос на /refresh-token с refresh-токеном
4. Система проверяет refresh-токен и выдает новую пару токенов












API Управления Профилем Пользователя
GET /profile: Получение данных профиля авторизованным пользователем (200 OK).

PUT /profile: Изменение данных профиля авторизованным пользователем (200 OK или 204 No Content).

GET /profile: Попытка доступа без авторизации (401 Unauthorized).
1. Как авторизованный пользователь, я хочу просматривать свои персональные данные, чтобы видеть, какая информация хранится в системе.
2. Как авторизованный пользователь, я хочу изменять свои персональные данные, чтобы они были актуальными.
1. Система должна предоставлять доступ к данным профиля только авторизованным пользователям.
2. Система должна возвращать все необходимые данные профиля пользователя по запросу GET /profile.
3. Система должна позволять обновлять данные профиля только их владельцу.
4. Система должна проверять валидность данных при обновлении профиля.
5. Система должна возвращать соответствующие коды ответа и сообщения об ошибках при невалидных данных.
Успешный сценарий:
1. Ivan входит в систему и получает токен доступа
2. Ivan отправляет GET-запрос на /profile с токеном авторизации
3. Система возвращает код 200 OK и данные профиля
4. Ivan отправляет PUT-запрос на /profile с обновленными данными
5. Система обновляет профиль и возвращает код 200 OK

Неуспешный сценарий:
1. Неавторизованный пользователь пытается получить данные профиля
2. Система возвращает код 401 Unauthorized с сообщением о необходимости авторизации












API Идентификации и Управления Устройствами (базовое)
POST /devices: Регистрация нового устройства для пользователя (201 Created).

GET /devices: Получение списка устройств пользователя (200 OK).

DELETE /devices/{deviceId}: Удаление устройства пользователя (204 No Content).

Проверка уникальности идентификатора устройства на уровне бэкенда.
1. Как пользователь, я хочу добавлять свои устройства в систему, чтобы использовать VPN.
2. Как пользователь, я хочу видеть список своих устройств, чтобы знать, какие устройства имеют доступ к моему VPN.
3. Как пользователь, я хочу удалять устройства из системы, чтобы отозвать доступ к VPN.
1. Система должна позволять регистрировать новые устройства для авторизованных пользователей.
2. Система должна хранить уникальный идентификатор, имя и тип устройства.
3. Система должна проверять уникальность идентификатора устройства.
4. Система должна предоставлять список всех устройств пользователя.
5. Система должна позволять удалять устройства владельцем.
Успешный сценарий:
1. Ivan регистрирует новое устройство:
   POST /devices с данными: deviceName: iPhone 15, downloadId: ABCD1234-EFGH-5678-IJKL-90MNOPQRSTUV, deviceType: iOS
2. Система сохраняет устройство и возвращает код 201 Created
3. Ivan запрашивает список своих устройств:
   GET /devices
4. Система возвращает код 200 OK со списком устройств
5. Ivan удаляет устройство:
   DELETE /devices/ABCD1234-EFGH-5678-IJKL-90MNOPQRSTUV
6. Система удаляет устройство и возвращает код 204 No Content

Неуспешный сценарий:
1. Ivan пытается зарегистрировать устройство с ID, который уже существует в системе
2. Система возвращает код 409 Conflict с сообщением о дублировании идентификатора








Этап 2: Интеграция с SingBox и Ядро VPN
Этап 2: Интеграция с SingBox и Ядро VPN
API Управления Конфигурациями SingBox
Бэкенд-логика: Корректное формирование JSON-конфигурации для SingBox на основе данных пользователя и сервера.

Бэкенд-логика: Добавление/удаление пользователей (их ключей доступа) в конфигурацию SingBox через API SingBox (проверка через мокированный SingBox API или логи бэкенда).
1. Как администратор системы, я хочу, чтобы бэкенд автоматически генерировал и обновлял конфигурацию SingBox.
2. Как администратор, я хочу, чтобы система автоматически управляла доступом пользователей к VPN.
1. Система должна автоматически генерировать валидные JSON-конфигурации для SingBox.
2. Система должна добавлять новых пользователей в конфигурацию SingBox при активации подписки.
3. Система должна удалять пользователей из конфигурации SingBox при истечении подписки.
4. Система должна использовать API SingBox для обновления конфигурации.
5. Система должна обеспечивать автоматическое применение конфигурации на SingBox-серверах.
Сценарий добавления пользователя:
1. Ivan приобретает подписку и система активирует её
2. Система формирует конфигурацию с пользовательскими данными:
   - Генерирует UUID для пользователя
   - Добавляет конфигурацию серверов и протоколов
3. Система вызывает API SingBox для обновления конфигурации

Сценарий удаления пользователя:
1. Подписка Ivan истекает или он блокируется администратором
2. Система обнаруживает изменение статуса подписки
3. Система удаляет пользователя из конфигурации SingBox
4. Система вызывает API SingBox для обновления конфигурации












API Генерации VPN-Конфигураций (VMESS/Trojan)
GET /vpn/config?protocol=vmess: Успешное получение валидной конфигурации VMESS для авторизованного пользователя (200 OK).

GET /vpn/config?protocol=trojan: Успешное получение валидной конфигурации Trojan (200 OK).

POST /vpn/connection/status: Уведомление о подключении/отключении клиента (200 OK).

Проверка, что конфигурация содержит корректные серверные данные, ID пользователя, ключи.
1. Как подписчик, я хочу получить конфигурацию VMESS для своего устройства, чтобы настроить VPN-клиент.
2. Как подписчик, я хочу получить конфигурацию Trojan для своего устройства, чтобы настроить VPN-клиент.
1. Система должна генерировать валидные конфигурации VMESS для авторизованных пользователей с активной подпиской.
2. Система должна генерировать валидные конфигурации Trojan для авторизованных пользователей с активной подпиской.
3. Конфигурации должны содержать все необходимые параметры: адрес сервера, порт, ID пользователя, ключи и другие параметры протокола.
4. Система должна возвращать конфигурации в формате, совместимом с популярными VPN-клиентами.
Успешный сценарий VMESS:
1. Ivan входит в систему и получает токен
2. Ivan отправляет GET-запрос на /vpn/config?protocol=vmess
3. Система проверяет наличие активной подписки
4. Система генерирует конфигурацию VMESS и возвращает код 200 OK

Успешный сценарий Trojan:
1. Ivan входит в систему и получает токен
2. Ivan отправляет GET-запрос на /vpn/config?protocol=trojan
3. Система проверяет наличие активной подписки
4. Система генерирует конфигурацию Trojan и возвращает код 200 OK

Пример ответа для VMESS:
{
  "vmess": {
	"v": "2",
	"ps": "VPN-Server-1",
	"add": "vpn1.example.com",
	"port": "443",
	"id": "a3482e88-686a-4a58-8126-99c9df64b7bf",
	"aid": "0",
	"net": "ws",
	"type": "none",
	"host": "vpn1.example.com",
	"path": "/websocket",
	"tls": "tls"
  }
}












API Управления Доступом к VPN-Серверам
GET /vpn/config: Попытка получения конфигурации без активной подписки (403 Forbidden).

Логика отзыва доступа: При окончании подписки/блокировке пользователя, бэкенд должен обновить конфигурацию SingBox, удалив пользователя (проверка через мок SingBox API).
1. Как пользователь, я хочу получать доступ к VPN-серверам только при наличии активной подписки.
2. Как администратор, я хочу, чтобы система автоматически отзывала доступ к VPN при истечении подписки или блокировке пользователя.
1. Система должна проверять наличие активной подписки при запросе конфигурации VPN.
2. Система должна отказывать в доступе к конфигурации VPN пользователям без активной подписки.
3. Система должна автоматически обновлять конфигурацию SingBox при истечении подписки или блокировке пользователя.
4. Система должна гарантировать, что пользователь без активной подписки не сможет использовать VPN даже с ранее полученной конфигурацией.
Сценарий с истекшей подпиской:
1. Подписка Ivan истекает
2. Ivan пытается получить конфигурацию VPN:
   GET /vpn/config?protocol=vmess
3. Система проверяет статус подписки и обнаруживает, что она неактивна
4. Система возвращает код 403 Forbidden с сообщением "Для доступа к VPN требуется активная подписка"

Сценарий с автоматическим отзывом доступа:
1. Подписка Ivan истекает
2. Система запускает процесс обновления конфигурации SingBox
3. Система удаляет конфигурацию пользователя из SingBox
4. Система применяет обновленную конфигурацию на серверах
5. При попытке подключения Ivan к VPN с ранее полученной конфигурацией, соединение не устанавливается








Этап 3: Управление Устройствами и Активация Ключей (расширенное)
Этап 3: Управление Устройствами и Активация Ключей (расширенное)
API Многоустройственного Доступа
POST /devices: Попытка добавить устройство сверх лимита подписки (403 Forbidden).

Проверка бэкенд-логики: корректный учет количества активных устройств пользователя.
1. Как премиум-подписчик, я хочу использовать VPN на нескольких устройствах в соответствии с моим тарифным планом.
2. Как пользователь, я хочу знать ограничения моего тарифного плана по количеству одновременно подключенных устройств.
1. Система должна поддерживать разные тарифные планы с различными лимитами на количество устройств.
2. Система должна отслеживать количество активных устройств пользователя.
3. Система должна проверять лимит устройств при добавлении нового устройства.
4. Система должна предоставлять информацию о лимите устройств и текущем количестве используемых устройств.
5. Система должна отказывать в добавлении устройства, если достигнут лимит.
Успешный сценарий:
1. Ivan имеет премиум-подписку с лимитом в 3 устройства
2. Ivan уже имеет 2 активных устройства и добавляет третье:
   POST /devices с данными нового устройства
3. Система проверяет лимит и видит, что он не превышен
4. Система успешно добавляет устройство и возвращает код 201 Created
5. Ivan запрашивает информацию о своей подписке:
   GET /subscriptions/me
6. Система возвращает информацию о подписке, включая лимит устройств (3) и текущее количество (3)

Неуспешный сценарий:
1. Ivan имеет стандартную подписку с лимитом в 3 устройства
2. Ivan уже имеет 3 активных устройства и пытается добавить четвертое:
   POST /devices с данными нового устройства
3. Система проверяет лимит и обнаруживает, что он превышен
4. Система возвращает код 403 Forbidden с сообщением "Превышен лимит устройств" и информацией о текущем лимите












API Генерации QR-кодов/Текстовых Ключей
GET /vpn/config/qr: Получение данных (например, URL конфигурации) для генерации QR-кода (200 OK).

GET /vpn/config/text: Получение текстовой конфигурации/ключа (200 OK).

Проверка валидности данных, возвращаемых для QR/текста.
1. Как пользователь, я хочу получить QR-код с VPN-конфигурацией для быстрой настройки на мобильном устройстве.
2. Как пользователь, я хочу получить текстовый ключ или URL для настройки VPN на десктопном устройстве.
1. Система должна генерировать данные для QR-кода, содержащие всю необходимую информацию для подключения.
2. Система должна генерировать текстовые конфигурации или URL для различных протоколов (VMESS/Trojan).
3. Система должна предоставлять данные в формате, совместимом с популярными VPN-клиентами.
4. Система должна проверять наличие активной подписки при запросе QR-кода или текстовой конфигурации.
Успешный сценарий получения QR-кода:
1. Ivan входит в систему и получает токен
2. Ivan отправляет GET-запрос:
   GET /vpn/config/qr?protocol=vmess&deviceId=ABCD1234-EFGH-5678-IJKL-90MNOPQRSTUV
3. Система проверяет наличие активной подписки
4. Система генерирует данные для QR-кода и возвращает код 200 OK с данными

Успешный сценарий получения текстовой конфигурации:
1. Ivan входит в систему и получает токен
2. Ivan отправляет GET-запрос:
   GET /vpn/config/text?protocol=trojan&deviceId=WXYZ9876-ABCD-5432-EFGH-10IJKLMNOPQR
3. Система проверяет наличие активной подписки
4. Система генерирует текстовую конфигурацию и возвращает код 200 OK с данными

Примеры ответов:
- Для QR-кода: {"qrData": "vmess://eyJ2IjoiMiIsInBzIjoiVlBOLVNlcnZlci0xIiwiYWRkIjoiLi4uIn0="}
- Для текста: {"textConfig": "trojan://<EMAIL>:443?security=tls&type=ws&path=/websocket#VPN-Server-1"}








Этап 4: Мониторинг и Балансировка Нагрузки
Этап 4: Мониторинг и Балансировка Нагрузки
API Получения Списка и Состояния VPN-Серверов
GET /servers: Получение списка доступных VPN-серверов с их статусом и пингом (200 OK).

Проверка, что бэкенд корректно агрегирует данные о пинге (полученные от механизма пингования).
1. Как пользователь, я хочу видеть список доступных VPN-серверов, чтобы выбрать наиболее подходящий.
2. Как пользователь, я хочу знать статус и пинг каждого сервера, чтобы оценить скорость соединения.
1. Система должна предоставлять список всех доступных VPN-серверов.
2. Система должна отслеживать и предоставлять информацию о статусе каждого сервера (онлайн/офлайн).
3. Система должна предоставлять информацию о пинге до каждого сервера.
4. Система должна предоставлять информацию о загрузке каждого сервера.
5. Система должна регулярно обновлять данные о статусе и пинге серверов.
Успешный сценарий:
1. Ivan входит в систему и получает токен
2. Ivan отправляет GET-запрос:
   GET /servers
3. Система возвращает код 200 OK со списком серверов:

```json
{
  "servers": [
	{
  	"id": "vpn1",
  	"name": "Германия (Франкфурт)",
  	"host": "vpn1.example.com",
  	"ping": 45,
  	"status": "online",
  	"load": 65
	},
	{
  	"id": "vpn2",
  	"name": "Нидерланды (Амстердам)",
  	"host": "vpn2.example.com",
  	"ping": 38,
  	"status": "online",
  	"load": 80
	},
	{
  	"id": "vpn3",
  	"name": "США (Нью-Йорк)",
  	"host": "vpn3.example.com",
  	"ping": 120,
  	"status": "online",
  	"load": 30
	}
  ]
}
```

Сценарий с недоступным сервером:
1. Сервер vpn2.example.com становится недоступен
2. Система обнаруживает это через механизм пингования
3. Ivan отправляет GET-запрос:
   GET /servers
4. Система возвращает обновленный список серверов, где vpn2 имеет статус "offline" и пинг null












Бэкенд-логика Тестирования HTTPS-пинга
HTTPS-пинг вместо ICMP: Проверка, что внутренний механизм бэкенда регулярно и корректно пингует VPN-серверы через HTTPS-запросы к /health endpoint.

Имитация недоступности одного из VPN-серверов: проверка, что бэкенд это обнаруживает и обновляет статус сервера.

Автоматическое переключение пользователей на доступные узлы в той же локации.
1. Как администратор, я хочу, чтобы система автоматически мониторила доступность VPN-серверов.
2. Как пользователь, я хочу всегда видеть актуальную информацию о статусе серверов.
1. Система должна регулярно (раз в минуту) проверять доступность и измерять пинг всех VPN-серверов.
2. Система должна использовать HTTPS-запросы для проверки доступности серверов.
3. Система должна обновлять статус сервера на "offline", если он не отвечает на несколько последовательных проверок.
4. Система должна вести логи мониторинга серверов.
5. Система должна иметь настраиваемые параметры мониторинга (частота проверок, тайм-аут).
Процесс мониторинга:
1. Система запускает фоновый процесс мониторинга
2. Процесс отправляет HTTPS-запросы на все серверы раз в минуту
3. Процесс измеряет время ответа и сохраняет его в базе данных
4. Если сервер не отвечает в течение тайм-аута (5 секунд), процесс помечает это
5. Если сервер не отвечает на 3 последовательных запроса, его статус меняется на "offline"

Сценарий обнаружения недоступного сервера:
1. Сервер vpn2.example.com перестает отвечать на HTTPS-запросы
2. Механизм мониторинга фиксирует 3 последовательных неудачных попытки
3. Система обновляет статус сервера на "offline" в базе данных
4. Система записывает событие в лог
5. При следующем запросе /servers, пользователи видят обновленный статус сервера












API Выбора Оптимального Сервера
GET /servers/optimal?location_id={id}&auto_select={true/false}: Получение рекомендуемого сервера на основе алгоритма (пинг, нагрузка) с учетом локации (200 OK).

GET /servers/locations: Получение списка доступных локаций (200 OK).

Проверка, что алгоритм выбора на бэкенде корректно отрабатывает разные сценарии (все серверы доступны, часть недоступна, фильтрация по локации).

Параметры:
- location_id: ID конкретной локации для выбора сервера
- auto_select=true: система сама выберет лучший сервер из любой локации
- без параметров: возвращает лучший из указанной локации
1. Как пользователь, я хочу автоматически получать информацию о наиболее оптимальном сервере для моего местоположения.
2. Как пользователь, я хочу, чтобы система учитывала как пинг, так и нагрузку сервера при выборе оптимального сервера.
1. Система должна предоставлять API для получения оптимального сервера.
2. Система должна учитывать пинг до сервера при выборе оптимального сервера.
3. Система должна учитывать текущую нагрузку на сервер при выборе оптимального сервера.
4. Система должна исключать недоступные серверы из рассмотрения.
5. Алгоритм выбора должен быть настраиваемым и позволять задавать веса для разных факторов.
Успешный сценарий:
1. Ivan входит в систему и получает токен
2. Ivan отправляет GET-запрос:
   GET /servers/optimal
3. Система запускает алгоритм выбора оптимального сервера:
   a. Фильтрует все серверы со статусом "online"
   b. Рассчитывает рейтинг для каждого сервера на основе пинга (60%) и нагрузки (40%)
   c. Выбирает сервер с наилучшим рейтингом
4. Система возвращает код 200 OK с данными оптимального сервера:
```json
{
  "server": {
	"id": "vpn2",
	"name": "Нидерланды (Амстердам)",
	"host": "vpn2.example.com",
	"ping": 38,
	"status": "online",
	"load": 80,
	"rating": 0.85
  }
}
```

Сценарий с частично недоступными серверами:
1. Сервер vpn2.example.com недоступен и имеет статус "offline"
2. Ivan отправляет GET-запрос /servers/optimal
3. Система исключает vpn2 из рассмотрения и выбирает наилучший из оставшихся серверов (vpn1)








Этап 5: Подписки и Интеграция с Платежной Системой
Этап 5: Подписки и Интеграция с Платежной Системой
API Управления Подписками
POST /subscriptions: Инициация покупки подписки (200 OK с redirect URL или данными для оплаты).

GET /subscriptions/me: Получение информации о текущей подписке пользователя (200 OK).

Проверка бэкенд-логики: изменение статуса подписки пользователя, предоставление/ограничение доступа к VPN.
1. Как пользователь, я хочу приобретать подписку на VPN-сервис, чтобы получить доступ к функциям.
2. Как пользователь, я хочу видеть информацию о своей текущей подписке.
3. Как пользователь, я хочу иметь возможность выбирать из разных тарифных планов.
1. Система должна предоставлять API для инициации покупки подписки.
2. Система должна предоставлять API для получения информации о текущей подписке пользователя.
3. Система должна предоставлять API для получения списка доступных тарифных планов.
4. Система должна корректно обрабатывать переходы между разными статусами подписки (активна, истекла, отменена).
5. Система должна автоматически предоставлять/ограничивать доступ к VPN в зависимости от статуса подписки.
Сценарий покупки подписки:
1. Ivan входит в систему и получает токен
2. Ivan запрашивает список доступных тарифных планов:
   GET /subscription-plans
3. Система возвращает список тарифных планов
4. Ivan инициирует покупку подписки:
   POST /subscriptions
   Body: {
 	"planId": "premium-monthly",
 	"paymentMethod": "card"
   }
5. Система возвращает код 200 OK и URL для перехода на страницу оплаты:
   {"redirectUrl": "https://payment.example.com/checkout/12345", "orderId": "order-6789"}

Сценарий просмотра информации о подписке:
1. Ivan имеет активную подписку
2. Ivan запрашивает информацию о своей подписке:
   GET /subscriptions/me
3. Система возвращает код 200 OK и информацию о подписке:
   {
 	"subscriptionId": "sub-12345",
 	"planId": "premium-monthly",
 	"status": "active",
 	"startDate": "2025-05-21T12:34:56Z",
 	"endDate": "2025-06-21T12:34:56Z",
 	"features": {
   	"maxDevices": 3,
   	"maxTraffic": null,
   	"servers": ["all"]
 	}
   }












API Обработки Webhook'ов от Платежной Системы
POST /payments/webhook: Успешная обработка webhook'а об успешной оплате (200 OK, активация/продление подписки).

POST /payments/webhook: Корректная обработка webhook'а о неуспешной оплате (200 OK, без изменения подписки).

POST /payments/webhook: Обработка невалидного/поддельного webhook'а (400 Bad Request или игнорирование с логированием).

Проверка идемпотентности обработки webhook'ов.
1. Как администратор, я хочу, чтобы система автоматически обрабатывала уведомления от платежной системы.
2. Как пользователь, я хочу, чтобы моя подписка активировалась сразу после оплаты.
1. Система должна предоставлять API для получения и обработки webhook'ов от платежной системы.
2. Система должна корректно обрабатывать уведомления о успешной оплате и активировать подписку.
3. Система должна корректно обрабатывать уведомления о неуспешной оплате без изменения статуса подписки.
4. Система должна проверять подлинность полученных webhook'ов.
5. Система должна обеспечивать идемпотентность обработки webhook'ов (несколько одинаковых уведомлений не должны приводить к дублированию действий).
Сценарий успешной оплаты:
1. Ivan завершает процесс оплаты на странице платежной системы
2. Платежная система отправляет webhook о успешной оплате:
   POST /payments/webhook
   Body: {
 	"event": "payment.success",
 	"orderId": "order-6789",
 	"paymentId": "pay-ABCDEF",
 	"amount": 9.99,
 	"currency": "EUR",
 	"timestamp": "2025-05-21T12:34:56Z"
   }
   Headers: {
 	"X-Signature": "valid-signature-hash"
   }
3. Система проверяет подпись в заголовке X-Signature
4. Система проверяет, что заказ order-6789 существует и не был оплачен ранее
5. Система активирует подписку для пользователя и сохраняет детали платежа
6. Система возвращает код 200 OK

Сценарий неуспешной оплаты:
1. Платежная система отправляет webhook о неуспешной оплате:
   POST /payments/webhook
   Body: {
 	"event": "payment.failed",
 	"orderId": "order-6789",
 	"errorCode": "insufficient_funds",
 	"timestamp": "2025-05-21T12:36:12Z"
   }
2. Система проверяет подпись и существование заказа
3. Система фиксирует ошибку платежа, но не изменяет статус подписки
4. Система возвращает код 200 OK












API Управления Промокодами
POST /promocodes/apply: Успешное применение валидного промокода (200 OK, применение скидки/бонуса к подписке).

POST /promocodes/apply: Попытка применения невалидного/просроченного/использованного промокода (400 Bad Request или 404 Not Found).
1. Как пользователь, я хочу применять промокоды для получения скидок на подписку.
2. Как администратор, я хочу создавать и управлять промокодами для маркетинговых кампаний.
1. Система должна предоставлять API для применения промокодов.
2. Система должна проверять существование и валидность промокода.
3. Система должна проверять дату истечения промокода.
4. Система должна проверять количество использований промокода.
5. Система должна применять скидку к стоимости подписки при валидном промокоде.
6. Система должна предоставлять API для администрирования промокодов (создание, изменение, удаление).
Сценарий успешного применения промокода:
1. Ivan входит в систему и получает токен
2. Ivan отправляет запрос на применение промокода:
   POST /promocodes/apply
   Body: {
 	"code": "WELCOME20"
   }
3. Система проверяет существование промокода
4. Система проверяет срок действия промокода
5. Система проверяет, что лимит использований не превышен
6. Система применяет промокод к аккаунту пользователя
7. Система возвращает код 200 OK и детали скидки:
   {
 	"promocode": "WELCOME20",
 	"discount": {
   	"type": "percentage",
   	"value": 20
 	},
 	"appliedTo": "next_purchase"
   }

Сценарий невалидного промокода:
1. Ivan отправляет запрос на применение несуществующего промокода:
   POST /promocodes/apply
   Body: {
 	"code": "INVALID50"
   }
2. Система не находит промокод в базе данных
3. Система возвращает код 404 Not Found с сообщением:
   {
 	"error": "Промокод не найден или истек срок его действия"
   }








Этап 6: Статистика, Аналитика и Уведомления
Этап 6: Статистика, Аналитика и Уведомления
API Сбора и Предоставления Статистики Трафика
Бэкенд-логика: Корректный сбор/агрегация данных об использованном трафике от SingBox (если SingBox API это позволяет и это реализуется).

GET /statistics/traffic: Получение статистики использования трафика для авторизованного пользователя (200 OK).

Проверка обновления/сброса статистики согласно условиям подписки.
1. Как пользователь, я хочу видеть статистику использования трафика, чтобы контролировать свои расходы.
2. Как администратор, я хочу собирать статистику использования трафика пользователями для аналитики.
1. Система должна собирать информацию об использованном трафике от SingBox.
2. Система должна агрегировать данные о трафике по разным периодам (день, неделя, месяц).
3. Система должна предоставлять API для получения статистики использования трафика.
4. Система должна обеспечивать защиту персональных данных при сборе статистики.
5. Система должна обновлять статистику трафика регулярно (например, раз в час).
Сценарий получения статистики трафика:
1. Ivan входит в систему и получает токен
2. Ivan отправляет запрос на получение статистики:
   GET /statistics/traffic
3. Система проверяет авторизацию и права доступа
4. Система извлекает и агрегирует данные о трафике пользователя
5. Система возвращает код 200 OK и данные о трафике:
   {
 	"totalTraffic": {
   	"download": 1245678900, // в байтах
   	"upload": 123456789,
   	"total": 1369135689
 	},
 	"lastWeek": {
   	"download": 345678900,
   	"upload": 34567890,
   	"total": 380246790
 	},
 	"byDay": [
   	{
     	"date": "2025-05-14",
     	"download": 123456789,
     	"upload": 12345678
   	},
   	// ...и т.д. по дням
 	]
   }

Сценарий сбора статистики трафика:
1. Система запускает фоновый процесс сбора статистики
2. Процесс отправляет запросы к SingBox API для получения данных о трафике по пользователям
3. Процесс агрегирует данные и сохраняет в базе данных
4. Процесс очищает устаревшие данные согласно настроенной политике хранения данных












API Управления Push-Уведомлениями (FCM)
POST /notifications/register_fcm_token: Регистрация FCM-токена устройства (200 OK).

Бэкенд-логика: Отправка уведомлений по триггерам (окончание подписки, новости) через FCM API (проверка через мок FCM API или логи бэкенда).
1. Как пользователь, я хочу получать уведомления о важных событиях (окончание подписки, обновления сервиса).
2. Как администратор, я хочу отправлять уведомления пользователям о важных событиях и маркетинговых акциях.
1. Система должна предоставлять API для регистрации FCM-токенов устройств.
2. Система должна хранить связь между пользователем, устройством и FCM-токеном.
3. Система должна уметь отправлять Push-уведомления через FCM API по различным триггерам.
4. Система должна обеспечивать базовую настройку типов уведомлений пользователем.
5. Система должна предоставлять административный API для отправки массовых уведомлений.
Сценарий регистрации FCM-токена:
1. Ivan входит в систему через мобильное приложение и получает токен
2. Приложение получает FCM-токен от Firebase и отправляет его на сервер:
   POST /notifications/register_fcm_token
   Body: {
 	"deviceId": "ABCD1234-EFGH-5678-IJKL-90MNOPQRSTUV",
 	"fcmToken": "fMzU1NTU5MDQ6MTA4Mzg4NTExMzQ4OTI4MzM4Nw=="
   }
3. Система проверяет авторизацию и права доступа
4. Система сохраняет связь между пользователем, устройством и FCM-токеном
5. Система возвращает код 200 OK

Сценарий отправки уведомления об окончании подписки:
1. Система обнаруживает, что подписка Ivan истекает через 3 дня
2. Система находит все FCM-токены, связанные с устройствами Ivan
3. Система формирует и отправляет уведомление через FCM API:
   ```
   {
 	"to": "fMzU1NTU5MDQ6MTA4Mzg4NTExMzQ4OTI4MzM4Nw==",
 	"notification": {
   	"title": "Истекает подписка VPN",
   	"body": "Ваша подписка истекает через 3 дня. Продлите, чтобы сохранить доступ к VPN."
 	},
 	"data": {
   	"type": "subscription_expiring",
   	"subscriptionId": "sub-12345",
   	"expiryDate": "2025-06-21T12:34:56Z"
 	}
   }
   ```
4. Приложение на устройстве Ivan получает и отображает уведомление








Этап 7: Безопасность и Финализация (бэкенд-аспекты)
Этап 7: Безопасность и Финализация (бэкенд-аспекты)
API для поддержки Killswitch на клиенте
GET /session/status: Быстрая проверка валидности текущей сессии/токена/подписки (200 OK или 401/403).

Бэкенд-логика: Принудительный отзыв сессии/токена пользователя (например, через API администратора), проверка, что последующие запросы с этим токеном не проходят.
1. Как пользователь, я хочу быть уверенным в безопасности моего соединения, и чтобы при проблемах с VPN интернет-соединение автоматически блокировалось.
2. Как администратор, я хочу иметь возможность принудительно отзывать сессии пользователей в случае необходимости.
1. Система должна предоставлять API для быстрой проверки статуса сессии/токена/подписки.
2. Система должна предоставлять административный API для принудительного отзыва сессий/токенов пользователей.
3. Система должна обеспечивать низкую задержку (<100мс) при проверке статуса сессии.
4. Система должна предоставлять информацию о причине отзыва сессии/токена при ответе на запрос проверки статуса.
Сценарий проверки статуса сессии:
1. Клиентское приложение Ivan периодически проверяет статус сессии:
   GET /session/status
   Headers: Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
2. Система проверяет валидность токена и активность подписки
3. Система возвращает код 200 OK и информацию о статусе сессии:
   {
 	"status": "active",
 	"subscriptionActive": true,
 	"lastChecked": "2025-05-21T14:30:00Z"
   }

Сценарий принудительного отзыва токена администратором:
1. Администратор обнаруживает подозрительную активность на аккаунте Ivan
2. Администратор отправляет запрос на отзыв всех токенов пользователя:
   POST /admin/users/{userId}/revoke-tokens
   Body: {
 	"reason": "suspicious_activity",
 	"notifyUser": true
   }
3. Система отзывает все токены пользователя и заносит их в черный список
4. При следующей проверке статуса клиентское приложение получает ответ:
   {
 	"status": "revoked",
 	"reason": "suspicious_activity",
 	"code": "token_revoked"
   }
5. Клиентское приложение активирует killswitch, блокируя весь интернет-трафик и перенаправляя пользователя на страницу входа








Этап 8: Административная панель и управление системой
Этап 8: Административная панель и управление системой
API для управления тарифными планами
GET/POST /admin/api/subscription-plans/: CRUD операции с тарифными планами (200 OK / 201 Created).

PUT/DELETE /admin/api/subscription-plans/{id}/: Обновление/удаление планов (200 OK / 204 No Content).

POST /admin/api/subscription-plans/{id}/toggle-active/: Активация/деактивация планов (200 OK).

Проверка корректности настройки лимитов (устройства, трафик, скорость) в тарифных планах.
1. Как администратор, я хочу создавать и настраивать тарифные планы, чтобы предлагать пользователям разные варианты подписок.
2. Как администратор, я хочу управлять параметрами планов (стоимость, лимиты устройств, доступные локации).
1. Система должна предоставлять API для создания, редактирования и удаления тарифных планов.
2. Система должна поддерживать настройку различных параметров: стоимость, количество устройств, лимит трафика, доступные локации.
3. Система должна позволять активировать/деактивировать тарифные планы.
4. Система должна проверять корректность параметров при создании/обновлении планов.
Сценарий создания тарифного плана:
1. Администратор создает новый план:
   POST /admin/api/subscription-plans/
   Body: {
 	"name": "Premium Monthly",
 	"price": 9.99,
 	"currency": "EUR",
 	"duration_days": 30,
 	"max_devices": 5,
 	"traffic_limit": null,
 	"speed_limit": null,
 	"available_locations": ["all"],
 	"is_active": true
   }
2. Система валидирует данные и создает план
3. Система возвращает код 201 Created с ID плана

Сценарий деактивации плана:
1. Администратор деактивирует план:
   POST /admin/api/subscription-plans/premium-monthly/toggle-active/
2. Система меняет статус плана на неактивный
3. План перестает быть доступным для новых подписок












API для управления локациями и VPN узлами
GET/POST /admin/api/locations/: Справочник локаций (страна, город, описание) (200 OK / 201 Created).

GET/POST /admin/api/nodes/: Управление VPN узлами (IP, домен, порт, статус) (200 OK / 201 Created).

POST /admin/api/nodes/{id}/toggle-active/: Активация/деактивация узлов (200 OK).

POST /admin/api/nodes/{id}/restart/: Перезапуск узла через API (200 OK).
1. Как администратор, я хочу управлять списком локаций и VPN узлов.
2. Как администратор, я хочу добавлять новые серверы и управлять их статусом.
3. Как администратор, я хочу иметь возможность перезапускать узлы при необходимости.
1. Система должна предоставлять справочник локаций с возможностью добавления новых.
2. Система должна позволять добавлять, редактировать и удалять VPN узлы.
3. Система должна предоставлять возможность управления статусом узлов.
4. Система должна предоставлять API для перезапуска узлов.
Сценарий добавления нового узла:
1. Администратор добавляет новый VPN узел:
   POST /admin/api/nodes/
   Body: {
 	"name": "Germany Frankfurt 2",
 	"domain": "vpn-de2.example.com",
 	"ip_address": "***************",
 	"port": 443,
 	"location_id": "germany-frankfurt",
 	"is_active": true,
 	"auth_token": "node-secret-token"
   }
2. Система создает запись узла в базе данных
3. Система начинает мониторинг нового узла
4. Узел становится доступным для пользователей












Django Admin интерфейс
Настройка Django Admin для управления всеми моделями системы.

Кастомизация административного интерфейса для удобства работы.

Интеграция кастомных действий (отправка push-уведомлений, перезапуск узлов).

Фильтры и поиск по основным полям.
1. Как администратор, я хочу иметь удобный веб-интерфейс для управления всей системой.
2. Как сотрудник техподдержки, я хочу быстро находить пользователей и управлять их подписками.
1. Система должна предоставлять полнофункциональную административную панель.
2. Административная панель должна включать все основные модели: пользователи, подписки, узлы, локации, тикеты.
3. Панель должна предоставлять удобные фильтры и поиск.
4. Панель должна включать кастомные действия для массовых операций.
Использование Django Admin:
1. Администратор заходит в панель по адресу /admin/
2. Видит все разделы: Accounts, Devices, Subscriptions, VPN Nodes, Support Tickets
3. Может искать пользователей по account_id или device_id
4. Может массово отправлять уведомления выбранным пользователям
5. Может перезапускать VPN узлы через действия в админке




























































1. Как администратор, я хочу настраивать тексты уведомлений для разных событий.
2. Как маркетолог, я хочу отправлять массовые уведомления пользователям.
1. Система должна предоставлять систему шаблонов уведомлений.
2. Система должна поддерживать различные типы событий для уведомлений.
3. Система должна позволять массовую отправку уведомлений.
4. Система должна автоматически отправлять уведомления по триггерам.
Сценарий настройки уведомлений:
1. Администратор создает шаблон уведомления об истечении подписки:
   POST /admin/api/notification-templates/
   Body: {
 	"event_type": "subscription_expiring",
 	"title": "Подписка истекает",
 	"message": "Ваша подписка истекает через {days_left} дней. Продлите для сохранения доступа.",
 	"is_active": true
   }
2. Система автоматически отправляет уведомления за 3 дня до истечения
3. Администратор отправляет массовое уведомление:
   POST /admin/api/notifications/broadcast/
   Body: {
 	"title": "Новые серверы!",
 	"message": "Добавили серверы в США и Японии",
 	"target_users": "active_subscribers"
   }








Этап 9:  автоматизация деплоя
Этап 9:  автоматизация деплоя 
API для манаджмента VPN узлов + базовые скрипты
POST /admin/api/nodes/deploy/: Автоматическое развертывание нового VPN узла на чистой VPS (201 Created).

Проверка базового скрипта deploy-node.sh: установка Docker, SingBox, настройка SSL.

POST /internal/node-register/: Автоматическая регистрация узла на главном сервере после деплоя (200 OK).

Проверка автоматического обновления SSL сертификатов (Let's Encrypt).
1. Как DevOps, я хочу разворачивать новые VPN узлы одним скриптом на чистой VPS.
2. Как администратор, я хочу, чтобы новые узлы автоматически появлялись в системе после деплоя.
1. Система должна предоставлять готовые скрипты для деплоя VPN узлов.
2. Скрипт должен работать на чистой Ubuntu/Debian VPS.
3. Скрипт должен автоматически устанавливать Docker, SingBox и настраивать SSL.
4. Узел должен автоматически регистрироваться на главном сервере.
5. Система должна предоставлять подробную документацию.
Сценарий деплоя нового узла:
1. DevOps арендует новую VPS в Нидерландах
2. Подключается по SSH и скачивает скрипт:
   wget https://vpn.example.com/scripts/deploy-node.sh
3. Запускает скрипт с параметрами:
   chmod +x deploy-node.sh
   ./deploy-node.sh vpn-nl2.example.com api.vpn.com node-token-12345
4. Скрипт автоматически:
   - Устанавливает Docker и docker-compose
   - Создает конфигурацию SingBox
   - Настраивает SSL через Let's Encrypt
   - Запускает VPN сервисы
   - Регистрирует узел на главном сервере
5. Через 5 минут узел появляется в Django Admin и становится доступным пользователям












Автоматическое переключение пользователей при сбоях
Celery задача: Проверка автоматического переключения пользователей при обнаружении сбоя узла.

POST /admin/api/nodes/{id}/force-failover/: Принудительное переключение всех пользователей с упавшего узла (200 OK).

POST /notifications/failover/: Отправка push-уведомлений пользователям о переключении на новый сервер (200 OK).

Проверка логики возврата нагрузки на восстановленный узел.
1. Как пользователь, я хочу, чтобы при сбое VPN-сервера мое подключение автоматически переключалось на другой сервер.
2. Как администратор, я хочу получать уведомления о сбоях серверов и автоматическом переключении.
1. Система должна обнаруживать сбои VPN-узлов в течение 3 минут.
2. Система должна автоматически переключать пользователей на альтернативные узлы.
3. Система должна отправлять уведомления администраторам о сбоях.
4. Система должна отправлять push-уведомления пользователям о переключении.
5. Система должна возвращать нагрузку на восстановленные узлы.
Сценарий автоматического переключения:
1. VPN-узел vpn-de1.example.com перестает отвечать на HTTPS-пинги
2. Через 3 неудачных попытки система помечает узел как 'offline'
3. Автоматически запускается Celery задача handle_node_failure
4. Система находит 150 пользователей, подключенных к упавшему узлу
5. Находит альтернативные узлы в той же локации (Германия): vpn-de2.example.com
6. Переключает пользователей на новые узлы
7. Отправляет push-уведомления: 'Подключение переключено на другой сервер'
8. Отправляет email администраторам о сбое узла
9. Когда vpn-de1.example.com восстанавливается, часть нагрузки постепенно возвращается обратно








Этап 10: Killswitch и дополнительная безопасность
Этап 10: Killswitch и дополнительная безопасность
API поддержки Killswitch и быстрой проверки сессий
GET /session/status: Максимально быстрая (менее 100мс) проверка статуса сессии/подписки (200 OK или 403 Forbidden).

POST /admin/api/users/{account_id}/revoke-tokens/: Принудительный отзыв всех токенов пользователя (200 OK).

GET /vpn/connection/verify: Проверка активности VPN-подключения для Killswitch (200 OK с статусом).

Проверка отзыва доступа: после отзыва токенов проверка, что последующие запросы не проходят.
1. Как пользователь, я хочу, чтобы при отключении VPN мой интернет-трафик автоматически блокировался (killswitch).
2. Как администратор, я хочу мочь принудительно отзывать сессии пользователей при подозрительной активности.
3. Как пользователь, я хочу быть уверенным в безопасности моего соединения.
1. Система должна обеспечивать очень быструю (менее 100мс) проверку статуса сессии.
2. Система должна позволять клиентским приложениям проверять статус VPN-подключения.
3. Система должна обеспечивать мгновенный отзыв доступа при отзыве токенов.
4. Система должна предоставлять информацию о причине отзыва доступа.
5. API должно быть оптимизировано для частых запросов от клиентов.
Сценарий работы Killswitch:
1. Приложение Ivan каждые 10 секунд проверяет статус:
   GET /session/status
   Ответ: {"status": "active", "vpn_connected": true, "subscription_active": true}
2. VPN-соединение неожиданно обрывается
3. При следующей проверке:
   GET /vpn/connection/verify
   Ответ: {"vpn_active": false, "last_seen": "2025-05-22T14:25:00Z"}
4. Приложение обнаруживает отключение VPN
5. Активирует killswitch: блокирует все сетевые соединения
6. Показывает пользователю уведомление: "VPN отключен, интернет заблокирован для безопасности"

Сценарий отзыва токенов:
1. Администратор обнаруживает подозрительную активность на аккаунте Ivan
2. Отзывает все токены:
   POST /admin/api/users/acc_123456789/revoke-tokens/
   Body: {"reason": "suspicious_activity", "notify_user": true}
3. При следующей проверке приложение получает:
   GET /session/status
   Ответ 403: {"status": "revoked", "reason": "suspicious_activity"}
4. Приложение активирует killswitch и перенаправляет на страницу входа










