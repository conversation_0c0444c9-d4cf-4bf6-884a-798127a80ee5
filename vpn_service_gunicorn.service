[Unit]
Description=VPN Service API Gunicorn daemon
Documentation=https://docs.gunicorn.org/
After=network.target
Wants=network.target

[Service]
Type=forking
User=root
Group=root
RuntimeDirectory=gunicorn
WorkingDirectory=/root/matrix/vpn_service
Environment="PATH=/usr/local/bin:/usr/bin:/bin"
Environment="DJANGO_SETTINGS_MODULE=vpn_service.settings"
ExecStart=/usr/local/bin/gunicorn --config /root/matrix/vpn_service/gunicorn.conf.py vpn_service.wsgi:application --daemon
ExecReload=/bin/kill -s HUP $MAINPID
KillMode=mixed
TimeoutStopSec=5
PrivateTmp=true
Restart=on-failure
RestartSec=5

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=vpn-service-api

# Security settings
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/root/matrix/vpn_service
ReadWritePaths=/var/log/gunicorn
ReadWritePaths=/var/run/gunicorn

[Install]
WantedBy=multi-user.target
