# Nginx SSL configuration for VPN Service API
# This serves as a temporary solution for HTTPS testing

server {
    listen 8443 ssl http2;
    server_name api.ductuspro.ru;
    
    # SSL Configuration - using self-signed certificates for testing
    ssl_certificate /tmp/api.ductuspro.ru.crt;
    ssl_certificate_key /tmp/api.ductuspro.ru.key;
    
    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # Security Headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-Frame-Options DENY always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # Proxy to Django VPN Service
    location / {
        proxy_pass http://127.0.0.1:8090;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto https;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        proxy_buffering off;
        proxy_request_buffering off;
        proxy_http_version 1.1;
        proxy_set_header Connection "";
    }
    
    # Logging
    access_log /var/log/nginx/vpn_api_ssl_access.log combined;
    error_log /var/log/nginx/vpn_api_ssl_error.log warn;
}

# HTTP to HTTPS redirect
server {
    listen 8080;
    server_name api.ductuspro.ru;
    return 301 https://$server_name:8443$request_uri;
}
