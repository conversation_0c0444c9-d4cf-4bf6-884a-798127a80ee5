#!/usr/bin/env python
"""
Скрипт для инициализации начальных данных VPN-сервиса.

PURPOSE:
  - Создает обязательный тариф по умолчанию для анонимных пользователей
  - Обеспечивает корректную работу системы регистрации
  - Является идемпотентным (безопасен для многократного запуска)

USAGE:
  python setup_initial_data.py

REQUIREMENTS:
  - Django проект должен быть настроен
  - База данных должна быть создана и мигрирована
  - Переменные окружения должны быть настроены
"""

import os
import sys
import django
from decimal import Decimal

# Настройка Django окружения
if __name__ == "__main__":
    # Добавляем путь к проекту
    project_path = os.path.join(os.path.dirname(__file__), 'vpn_service')
    sys.path.insert(0, project_path)
    
    # Настраиваем Django
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'vpn_service.settings')
    django.setup()

    # Импортируем модели после настройки Django
    from subscriptions.models import SubscriptionPlan

    def create_default_free_plan():
        """
        Создает тариф по умолчанию для анонимных пользователей.
        
        PURPOSE:
          - Обеспечивает наличие обязательного тарифа для анонимных пользователей
          - Создает план с минимальными лимитами для тестирования
          - Устанавливает флаг is_free_default=True
        
        RETURNS:
          - bool: True если план был создан, False если уже существует
        """
        try:
            # Проверяем, существует ли уже тариф по умолчанию
            existing_plan = SubscriptionPlan.objects.filter(is_free_default=True).first()
            
            if existing_plan:
                print(f"✅ Тариф по умолчанию уже существует: '{existing_plan.name}'")
                print(f"   ID: {existing_plan.id}")
                print(f"   Цена: {existing_plan.price} {existing_plan.currency}")
                print(f"   Длительность: {existing_plan.duration_days} дней")
                print(f"   Лимит трафика: {existing_plan.traffic_limit_gb} ГБ")
                print(f"   Максимум устройств: {existing_plan.max_devices}")
                print(f"   Активен: {existing_plan.is_active}")
                return False
            
            # Создаем новый тариф по умолчанию
            default_plan = SubscriptionPlan.objects.create(
                name="Free Default Plan",
                description="Бесплатный тариф по умолчанию для анонимных пользователей. Предоставляет базовый доступ к VPN-сервису для тестирования.",
                price=Decimal('0.00'),
                currency='USD',
                duration_days=30,  # 30 дней
                traffic_limit_gb=1,  # 1 ГБ трафика
                max_devices=1,  # 1 устройство
                is_active=True,
                is_trial=False,
                is_free_default=True
            )
            
            print("🎉 Успешно создан тариф по умолчанию!")
            print(f"   Название: {default_plan.name}")
            print(f"   ID: {default_plan.id}")
            print(f"   Цена: {default_plan.price} {default_plan.currency}")
            print(f"   Длительность: {default_plan.duration_days} дней")
            print(f"   Лимит трафика: {default_plan.traffic_limit_gb} ГБ")
            print(f"   Максимум устройств: {default_plan.max_devices}")
            print(f"   Активен: {default_plan.is_active}")
            print(f"   Тариф по умолчанию: {default_plan.is_free_default}")
            
            return True
            
        except Exception as e:
            print(f"❌ Ошибка при создании тарифа по умолчанию: {e}")
            return False

    def verify_database_connection():
        """
        Проверяет подключение к базе данных.
        
        RETURNS:
          - bool: True если подключение успешно
        """
        try:
            from django.db import connection
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
            print("✅ Подключение к базе данных успешно")
            return True
        except Exception as e:
            print(f"❌ Ошибка подключения к базе данных: {e}")
            return False

    def main():
        """
        Основная функция скрипта.
        
        PURPOSE:
          - Выполняет все проверки и инициализацию данных
          - Обеспечивает корректную обработку ошибок
          - Выводит понятные сообщения о результатах
        """
        print("🚀 Запуск инициализации начальных данных VPN-сервиса...")
        print("=" * 60)
        
        # Проверяем подключение к базе данных
        if not verify_database_connection():
            print("❌ Не удалось подключиться к базе данных. Проверьте настройки.")
            sys.exit(1)
        
        # Создаем тариф по умолчанию
        print("\n📋 Проверка и создание тарифа по умолчанию...")
        plan_created = create_default_free_plan()
        
        # Выводим итоговую информацию
        print("\n" + "=" * 60)
        if plan_created:
            print("✅ Инициализация завершена успешно! Создан новый тариф по умолчанию.")
        else:
            print("✅ Инициализация завершена! Тариф по умолчанию уже существует.")
        
        print("\n📝 Следующие шаги:")
        print("   1. Запустите Django сервер: python manage.py runserver")
        print("   2. Проверьте админ-панель: http://localhost:8000/admin/")
        print("   3. Протестируйте регистрацию анонимных пользователей")
        
        print("\n🔧 Для тестирования API используйте:")
        print("   python e2e_test_flow.py")

    # Запускаем основную функцию
    main()
