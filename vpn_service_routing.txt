# VPN Service API routing rules - to be added to frontend http-https-in
# Add these lines after the hiddifypanel rules

# VPN Service API ACL rules
acl is_vpn_api path_beg /api/
acl is_vpn_health path_beg /health/
acl is_vpn_docs path_beg /api/docs/
acl is_vpn_redoc path_beg /api/redoc/
acl is_vpn_schema path_beg /api/schema/

# Route VPN API requests to vpn_service_api backend
use_backend vpn_service_api if is_vpn_api
use_backend vpn_service_api if is_vpn_health
use_backend vpn_service_api if is_vpn_docs
use_backend vpn_service_api if is_vpn_redoc
use_backend vpn_service_api if is_vpn_schema
