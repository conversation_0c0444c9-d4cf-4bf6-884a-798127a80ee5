# HAProxy Configuration for VPN Service API with SSL
# This configuration should be added to the existing HAProxy setup

# Backend for VPN Service API
backend vpn_service_api
    mode http
    balance roundrobin
    option httpchk GET /health/
    http-check expect status 200

    # Django server on port 8090 (standard VPN Service port)
    server vpn_api 127.0.0.1:8090 check inter 30s fall 3 rise 2
    
    # Headers for Django behind proxy
    http-request set-header X-Forwarded-Proto https
    http-request set-header X-Forwarded-For %[src]
    http-request set-header X-Real-IP %[src]

# Frontend configuration for HTTPS (add to existing frontend)
# This should be added to the existing HTTPS frontend in HAProxy

# Example frontend configuration:
frontend https_frontend
    bind *:443 ssl crt /path/to/ductuspro.ru.pem
    mode http
    
    # Existing rules...
    
    # VPN Service API routing
    acl is_vpn_api hdr(host) -i api.ductuspro.ru
    acl is_vpn_api_path path_beg /api/
    acl is_vpn_health path_beg /health/
    acl is_vpn_docs path_beg /api/docs/
    acl is_vpn_redoc path_beg /api/redoc/
    acl is_vpn_schema path_beg /api/schema/
    
    # Route VPN API requests
    use_backend vpn_service_api if is_vpn_api
    use_backend vpn_service_api if is_vpn_api_path
    use_backend vpn_service_api if is_vpn_health
    use_backend vpn_service_api if is_vpn_docs
    use_backend vpn_service_api if is_vpn_redoc
    use_backend vpn_service_api if is_vpn_schema
    
    # Rate limiting for API endpoints
    stick-table type ip size 100k expire 30s store http_req_rate(10s)
    http-request track-sc0 src
    http-request deny if { sc_http_req_rate(0) gt 20 }

# HTTP to HTTPS redirect (add to existing HTTP frontend)
frontend http_frontend
    bind *:80
    mode http
    
    # Redirect all HTTP to HTTPS
    redirect scheme https code 301

# Global settings (add to existing global section)
global
    # SSL settings
    ssl-default-bind-ciphers ECDHE+AESGCM:ECDHE+CHACHA20:RSA+AESGCM:RSA+AES:!aNULL:!MD5:!DSS
    ssl-default-bind-options ssl-min-ver TLSv1.2 no-tls-tickets
    
    # Security
    tune.ssl.default-dh-param 2048
