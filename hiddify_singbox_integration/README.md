# Интеграция Hiddify Manager с SingBox - Trojan Protocol

## 📋 Обзор

Данная директория содержит конфигурации и документацию для интеграции VPN-сервиса на базе Django/DRF с Hiddify Manager, специально адаптированные для протокола Trojan.

## 📁 Структура файлов

### Конфигурационные файлы

1. **`06_undef_uk_trojan.json`** - Оригинальная конфигурация стороннего сервера
   - Сервер: `undef-uk-1.undef.network:443`
   - Протокол: Trojan over WebSocket
   - Режим: TUN (полный VPN)

2. **`ductuspro_trojan_tun_config.json`** - Адаптированная конфигурация для нашего сервера
   - Сервер: `ductuspro.ru:443`
   - Протокол: Trojan over WebSocket
   - Режим: TUN (полный VPN)
   - Пароль: Реальный UUID пользователя из Hiddify

3. **`ductuspro_trojan_dynamic_template.json`** - Шаблон с переменными
   - Поддерживает динамическую подстановку параметров
   - Переменные: `{{SERVER}}`, `{{USER_PASSWORD}}`, `{{WS_PATH}}`

## 🔧 Технические детали

### Параметры подключения

| Параметр | Значение | Описание |
|----------|----------|----------|
| **Сервер** | `ductuspro.ru` | Основной домен |
| **Порт** | `443` | HTTPS порт с SSL |
| **Протокол** | `Trojan` | Trojan over WebSocket |
| **TLS** | `Включен` | С SNI и ALPN |
| **WebSocket путь** | `/Cgm6B1DqLOKIFrY19tjCyr3egnx` | Реальный путь из Hiddify |
| **Пароль** | `UUID пользователя` | Динамический для каждого пользователя |

### Режимы работы

#### TUN режим (Полный VPN)
- **Интерфейс:** `tun0`
- **IP адрес:** `**********/30`
- **MTU:** `9000`
- **Маршрутизация:** Автоматическая
- **DNS:** Через прокси (*******)

#### Mixed режим (Прокси)
- **Интерфейс:** `127.0.0.1:2080`
- **Протоколы:** HTTP/SOCKS
- **DNS:** Локальный + Google DNS

## 🚀 API Endpoints

### Получение Trojan конфигурации

```http
GET /api/vpn/trojan/?mode=tun
Authorization: Bearer <JWT_TOKEN>
```

**Параметры:**
- `mode` - Режим подключения (`tun` или `mixed`)
- `listen_port` - Порт для mixed режима (по умолчанию: 2080)

**Пример ответа:**
```json
{
  "success": true,
  "config_type": "trojan",
  "mode": "tun",
  "config": {
    "log": {...},
    "dns": {...},
    "inbounds": [...],
    "outbounds": [...],
    "route": {...}
  },
  "connection_info": {
    "protocol": "trojan",
    "server": "ductuspro.ru",
    "server_port": 443,
    "transport": "websocket",
    "ws_path": "/Cgm6B1DqLOKIFrY19tjCyr3egnx",
    "tls_enabled": true
  },
  "subscription_info": {...}
}
```

## 🔐 Безопасность

### TLS конфигурация
- **Версия:** TLS 1.2+
- **SNI:** `ductuspro.ru`
- **ALPN:** `h2`, `http/1.1`
- **Проверка сертификата:** Включена

### WebSocket маскировка
- **Путь:** Уникальный для каждого протокола
- **Заголовки:** `Host: ductuspro.ru`
- **Маскировка:** Под обычный HTTPS трафик

## 📊 Мониторинг и логирование

### Логи подключений
- Уровень: `info`
- Временные метки: Включены
- Ротация: Автоматическая

### Метрики трафика
- Загрузка/выгрузка в байтах
- Синхронизация с Hiddify Manager
- Лимиты по тарифным планам

## 🛠️ Интеграция с Django

### Сервис класс
```python
from vpn.trojan_service import TrojanConfigService

# TUN режим
config = TrojanConfigService.generate_trojan_tun_config(
    password=user_uuid,
    user_uuid=user_id
)

# Mixed режим
config = TrojanConfigService.generate_trojan_mixed_config(
    password=user_uuid,
    listen_port=2080,
    user_uuid=user_id
)
```

### URL маршруты
```python
# vpn/urls.py
path('trojan/', views.get_trojan_config, name='trojan_config'),
```

## 🔄 Совместимость

### Поддерживаемые клиенты
- **SingBox** - Основной клиент
- **V2Ray** - Частичная поддержка
- **Clash** - Через конвертацию

### Операционные системы
- **Android** - SingBox app
- **iOS** - SingBox app
- **Windows** - SingBox GUI
- **macOS** - SingBox GUI
- **Linux** - SingBox CLI

## 📝 Примечания

1. **Производительность:** TUN режим обеспечивает лучшую производительность для полного VPN
2. **Совместимость:** Mixed режим лучше подходит для приложений с прокси поддержкой
3. **Безопасность:** Все подключения используют TLS 1.2+ с проверкой сертификатов
4. **Масштабируемость:** Каждый пользователь получает уникальный UUID пароль

## 🔗 Связанные файлы

- `/vpn_service/vpn/trojan_service.py` - Сервис для генерации конфигураций
- `/vpn_service/vpn/views.py` - API endpoint для Trojan
- `/vpn_service/vpn/urls.py` - URL маршруты
- `/vpn_service/trojan_ws_working_config.json` - Рабочая конфигурация для тестов
