# Система активации подписок через промокоды

## 📋 Обзор

Система промокодов обеспечивает механизм активации платных подписок VPN-сервиса через уникальные коды. Это основной способ монетизации на данном этапе развития проекта.

## 🏗 Архитектура

### Компоненты системы

1. **Модель PromoCode** - хранение промокодов и их состояния
2. **PromoActivationService** - бизнес-логика активации
3. **API эндпоинт** - `POST /api/promo/activate/`
4. **<PERSON><PERSON><PERSON> Admin** - управление промокодами

### Схема базы данных

```sql
CREATE TABLE promo_codes (
    id UUID PRIMARY KEY,
    code VARCHAR(16) UNIQUE NOT NULL,
    plan_id UUID REFERENCES subscription_plans(id),
    is_activated BOOLEAN DEFAULT FALSE,
    activated_at TIMESTAMP NULL,
    activated_by_id UUID REFERENCES user_accounts(id) NULL,
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP NOT NULL
);
```

## 🔄 Процесс активации

### Схема работы

```mermaid
sequenceDiagram
    participant Client as Клиент
    participant API as API Server
    participant Service as PromoService
    participant DB as Database
    participant Hiddify as Hiddify Manager

    Client->>API: POST /api/promo/activate/
    Note over Client,API: {promo_code, device_id}
    
    API->>Service: activate_promo_code()
    
    Service->>DB: Валидация промокода
    DB-->>Service: PromoCode объект
    
    Service->>Service: Идентификация пользователя
    Note over Service: JWT токен или device_id
    
    alt Новый анонимный пользователь
        Service->>DB: Создание UserAccount
        Service->>DB: Создание UserDevice
        Service->>Hiddify: create_hiddify_user()
        Hiddify-->>Service: hiddify_user_uuid
        Service->>DB: Создание HiddifyLink
    end
    
    Service->>DB: Деактивация старых подписок
    Service->>DB: Создание новой подписки
    Service->>Hiddify: update_hiddify_user()
    Service->>DB: Активация промокода
    
    Service-->>API: Результат активации
    API-->>Client: Ответ с подпиской и токенами
```

### Этапы активации

1. **Валидация промокода**
   - Проверка существования
   - Проверка срока действия
   - Проверка активации

2. **Идентификация пользователя**
   - JWT токен → существующий пользователь
   - device_id → поиск по устройству
   - Новое устройство → создание анонимного пользователя

3. **Создание подписки**
   - Деактивация старых подписок
   - Создание новой подписки на основе тарифа из промокода

4. **Обновление VPN доступа**
   - Синхронизация лимитов с Hiddify Manager
   - Активация пользователя в VPN системе

5. **Финализация**
   - Пометка промокода как активированного
   - Формирование ответа с токенами (для новых пользователей)

## 🔌 API Документация

### POST /api/promo/activate/

Активирует промокод и создает подписку для пользователя.

#### Запрос

```json
{
  "promo_code": "PREMIUM2024",
  "device_id": "device_12345"
}
```

**Параметры:**
- `promo_code` (string, required) - Промокод для активации
- `device_id` (string, required) - Уникальный идентификатор устройства

**Заголовки:**
- `Authorization: Bearer <token>` (optional) - JWT токен для аутентифицированных пользователей

#### Ответы

**200 OK - Успешная активация**
```json
{
  "success": true,
  "message": "Subscription activated successfully!",
  "subscription": {
    "plan_name": "Premium",
    "end_date": "2024-07-01T12:00:00Z",
    "traffic_limit_gb": 100,
    "duration_days": 30,
    "is_active": true
  },
  "tokens": {
    "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
  }
}
```

**Поле `tokens` присутствует только для новых анонимных пользователей.**

**400 Bad Request - Ошибка валидации**
```json
{
  "error": "Некорректные данные запроса",
  "code": "VALIDATION_ERROR",
  "details": {
    "promo_code": ["Это поле обязательно."]
  }
}
```

**404 Not Found - Промокод не найден**
```json
{
  "error": "Промокод не найден или недействителен",
  "code": "INVALID_PROMO_CODE"
}
```

**409 Conflict - Ошибка обновления VPN**
```json
{
  "error": "Не удалось обновить VPN доступ",
  "code": "HIDDIFY_UPDATE_FAILED"
}
```

**500 Internal Server Error - Внутренняя ошибка**
```json
{
  "error": "Внутренняя ошибка сервера",
  "code": "INTERNAL_ERROR"
}
```

## 🎯 Сценарии использования

### 1. Новый анонимный пользователь

```bash
curl -X POST http://localhost:8090/api/promo/activate/ \
  -H "Content-Type: application/json" \
  -d '{
    "promo_code": "PREMIUM2024",
    "device_id": "new_device_123"
  }'
```

**Результат:**
- Создается анонимный пользователь
- Создается устройство
- Создается связь с Hiddify
- Активируется подписка
- Возвращаются JWT токены

### 2. Аутентифицированный пользователь

```bash
curl -X POST http://localhost:8090/api/promo/activate/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..." \
  -d '{
    "promo_code": "PREMIUM2024",
    "device_id": "existing_device_123"
  }'
```

**Результат:**
- Используется существующий пользователь
- Активируется подписка
- Токены не возвращаются

### 3. Deeplink из мобильного приложения

Мобильное приложение обрабатывает deeplink:
```
myvpnapp://activate-promo?code=PREMIUM2024
```

Извлекает код и вызывает API с текущим device_id.

## 🛠 Управление промокодами

### Django Admin

Промокоды управляются через Django Admin интерфейс:

1. **Создание промокодов**
   - Указание уникального кода
   - Выбор тарифного плана
   - Установка срока действия (опционально)

2. **Мониторинг использования**
   - Просмотр активированных кодов
   - Информация о пользователях
   - Статистика по тарифам

3. **Фильтрация и поиск**
   - По статусу активации
   - По тарифному плану
   - По дате создания/активации

### Создание тестовых данных

```python
# Создание тарифного плана
plan = SubscriptionPlan.objects.create(
    name='Premium',
    price=15.00,
    duration_days=30,
    traffic_limit_gb=100,
    is_active=True
)

# Создание промокода
promo = PromoCode.objects.create(
    code='PREMIUM2024',
    plan=plan,
    expires_at=timezone.now() + timedelta(days=60)
)
```

## 🔒 Безопасность

### Валидация промокодов

- Проверка существования в базе данных
- Проверка срока действия
- Проверка статуса активации
- Проверка активности тарифного плана

### Защита от злоупотреблений

- Уникальность промокодов
- Одноразовое использование
- Ограничение по времени
- Логирование всех попыток активации

### Транзакционность

Все операции активации выполняются в рамках database transaction:
- При ошибке все изменения откатываются
- Гарантируется консистентность данных
- Предотвращается частичная активация

## 📊 Мониторинг и логирование

### Логи активации

```
INFO: Successfully activated promo code PREMIUM2024 for user 12345
WARNING: Invalid promo code attempted: INVALID2024
ERROR: Failed to update Hiddify user uuid-123: API timeout
```

### Метрики

- Количество активированных промокодов
- Популярные тарифные планы
- Конверсия промокодов
- Ошибки интеграции с Hiddify

## 🧪 Тестирование

### Запуск тестов

```bash
cd vpn_service
python manage.py test promo
```

### Покрытие тестами

- ✅ Модель PromoCode
- ✅ Сервис активации
- ✅ API эндпоинт
- ✅ Интеграция с Hiddify
- ✅ Обработка ошибок

## 🚀 Развертывание

### Миграции

```bash
python manage.py makemigrations promo
python manage.py migrate
```

### Настройка URL

Добавлено в `vpn_service/urls.py`:
```python
path('api/promo/', include('promo.urls')),
```

### Зависимости

Система использует существующие компоненты:
- `accounts` - модели пользователей и устройств
- `subscriptions` - модели тарифов и подписок
- `vpn.services` - интеграция с Hiddify Manager
