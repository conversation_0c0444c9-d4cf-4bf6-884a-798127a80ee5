# Реализация системы анонимных пользователей

## Обзор

Система анонимных пользователей позволяет пользователям получать VPN-доступ без предварительной регистрации по email/паролю. Анонимные пользователи идентифицируются по уникальному `device_id` и могут в дальнейшем "апгрейднуть" свой аккаунт до полноценного зарегистрированного.

## Изменения в модели данных

### UserAccount
- **Добавлено поле**: `is_anonymous = models.BooleanField(default=False)`
- **Изменено поле**: `email = models.EmailField(null=True, blank=True)` (теперь необязательное)
- **Добавлен constraint**: Уникальность email только когда он не NULL
- **Изменен USERNAME_FIELD**: Теперь используется `username` вместо `email`
- **Добавлен кастомный менеджер**: `UserAccountManager` для корректного создания анонимных пользователей

### Миграции
Применены миграции:
- `0004_add_anonymous_user_support` - добавление поля `is_anonymous` и constraint
- `0005_add_anonymous_user_support` - обновление менеджера модели

## Новые API эндпоинты

### POST /api/auth/device/init/
**Инициализация устройства (создание анонимного пользователя)**

#### Request Body:
```json
{
  "device_id": "unique-device-id-123",
  "device_name": "iPhone 13",
  "device_type": "ios"
}
```

#### Response (201 Created - новый пользователь):
```json
{
  "success": true,
  "user": {
    "id": "uuid",
    "is_anonymous": true
  },
  "subscription": {
    "plan_name": "Trial",
    "end_date": "2025-06-14T10:23:21.500677+00:00",
    "traffic_limit_gb": 10
  },
  "tokens": {
    "access": "jwt_access_token",
    "refresh": "jwt_refresh_token"
  }
}
```

#### Response (200 OK - существующий анонимный пользователь):
Аналогичная структура, но статус 200.

#### Response (409 Conflict - device_id привязан к зарегистрированному пользователю):
```json
{
  "error": "Это устройство уже привязано к зарегистрированному аккаунту. Пожалуйста, войдите в систему."
}
```

### POST /api/auth/convert-anonymous/
**Конвертация анонимного аккаунта в зарегистрированный**

#### Headers:
```
Authorization: Bearer <anonymous_access_token>
```

#### Request Body:
```json
{
  "email": "<EMAIL>",
  "password": "securepassword123",
  "password_confirm": "securepassword123"
}
```

#### Response (200 OK):
```json
{
  "success": true,
  "message": "Account upgraded successfully.",
  "user": {
    "id": "uuid",
    "email": "<EMAIL>"
  },
  "tokens": {
    "access": "new_jwt_access_token",
    "refresh": "new_jwt_refresh_token"
  }
}
```

#### Response (403 Forbidden - пользователь не анонимный):
```json
{
  "error": "Этот аккаунт уже зарегистрирован"
}
```

## Логика работы

### Инициализация устройства
1. Проверяется существование устройства с данным `device_id`
2. Если устройство существует:
   - Если привязано к анонимному пользователю → возвращаются токены (200 OK)
   - Если привязано к зарегистрированному пользователю → ошибка 409 Conflict
3. Если устройство новое:
   - Создается анонимный пользователь (`is_anonymous=True`, `email=NULL`)
   - Создается запись устройства
   - Создается пользователь в Hiddify Manager
   - Создается пробная подписка
   - Возвращаются JWT токены (201 Created)

### Конвертация аккаунта
1. Проверяется, что пользователь анонимный (`is_anonymous=True`)
2. Валидируются email и пароль
3. Проверяется уникальность email
4. Обновляется запись пользователя:
   - `email` устанавливается
   - `password` хэшируется и сохраняется
   - `is_anonymous` устанавливается в `False`
   - `username` обновляется на email
5. Генерируются новые JWT токены
6. Все VPN-данные и подписки сохраняются

## Безопасность

- Анонимные пользователи создаются с `set_unusable_password()`
- JWT токены содержат `device_id` и `hiddify_uuid` в claims
- Email валидируется на уникальность при конвертации
- Пароли проходят стандартную валидацию Django

## Интеграция с Hiddify

- Анонимные пользователи создаются в Hiddify с именем `anon_{user_id[:8]}`
- Метаданные включают флаг `is_anonymous: true`
- VPN-доступ работает идентично зарегистрированным пользователям
- При конвертации Hiddify-данные сохраняются без изменений

## Тестирование

Все эндпоинты протестированы на:
- ✅ Создание нового анонимного пользователя
- ✅ Повторная инициализация существующего анонимного устройства
- ✅ Попытка инициализации устройства, привязанного к зарегистрированному пользователю
- ✅ Успешная конвертация анонимного аккаунта
- ✅ Попытка конвертации уже зарегистрированного аккаунта
- ✅ Валидация email на уникальность
- ✅ Валидация паролей
- ✅ Интеграция с Hiddify Manager

## Совместимость

Система полностью совместима с существующими эндпоинтами:
- `/api/auth/register/` - для прямой регистрации
- `/api/auth/login/` - для входа зарегистрированных пользователей
- `/api/auth/activation-code/` и `/api/auth/activate/` - для двухуровневой аутентификации

Анонимные пользователи могут использовать все VPN-функции наравне с зарегистрированными.
