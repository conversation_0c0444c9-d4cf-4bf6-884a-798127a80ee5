# Система персонализированных SingBox конфигураций

## Обзор

Система персонализированных SingBox конфигураций обеспечивает автоматическую генерацию уникальных VPN конфигураций для каждого пользователя VPN сервиса. Каждая конфигурация работает как "SIM-карта пользователя" - персонализированная настройка, которую пользователь может импортировать в SingBox клиент для подключения к VPN.

## Архитектура

### Основные компоненты

1. **PersonalizedSingBoxService** - основной сервис для генерации конфигураций
2. **Базовый шаблон** - структура SingBox конфигурации с поддержкой множественных протоколов
3. **Интеграция с Hiddify Manager** - создание пользователей и получение параметров
4. **Система локаций** - параметры серверов для различных географических точек

### Поддерживаемые протоколы и транспорты

- **Trojan**: WebSocket, gRPC
- **VMess**: WebSocket, gRPC, HTTP Upgrade
- **Транспорты**: WebSocket, gRPC, HTTP Upgrade
- **TLS**: Обязательно для всех подключений

## Использование

### 1. Создание нового пользователя с конфигурацией

```python
from vpn_service.vpn.trojan_service import PersonalizedSingBoxService

# Параметры локации
location_params = {
    'server': '***********',
    'server_port': 443,
    'tls_server_name': '***********.sslip.io',
    'trojan_ws_path': '/Cgm6B1DqLOKIFrY19tjCyr3egnx',
    'vmess_ws_path': '/39m0pgSOrY19tjCyr3egnx',
    'vmess_httpupgrade_path': '/39m0pgSOdKbicJLIaR',
    'trojan_grpc_service': 'Cgm6B1DqLOKIFOh7gdS9',
    'vmess_grpc_service': '39m0pgSOOh7gdS9'
}

# Создание пользователя и генерация конфигурации
result = PersonalizedSingBoxService.create_user_and_generate_config(
    user_name="user_001",
    usage_limit_gb=50,
    package_days=30,
    location_params=location_params,
    comment_data={'plan': 'premium', 'source': 'web_registration'}
)

if result['success']:
    hiddify_uuid = result['hiddify_user_uuid']
    singbox_config = result['singbox_config']
    # Сохранить конфигурацию или отправить пользователю
else:
    error = result['error']
    # Обработать ошибку
```

### 2. Генерация конфигурации для существующего пользователя

```python
# Для уже созданного пользователя в Hiddify
result = PersonalizedSingBoxService.get_config_for_existing_user(
    hiddify_user_uuid="15c175d8-703c-456a-ac82-91041f8af845",
    location_params=location_params,
    user_name="user_001"
)

if result['success']:
    singbox_config = result['singbox_config']
    user_info = result.get('user_info', {})
```

### 3. Прямая генерация конфигурации

```python
# Только генерация конфигурации без создания пользователя
config = PersonalizedSingBoxService.generate_personalized_config(
    hiddify_user_uuid="15c175d8-703c-456a-ac82-91041f8af845",
    location_params=location_params,
    user_name="user_001"
)
```

## Структура конфигурации

### Базовый шаблон

Базовый шаблон включает:

- **DNS настройки**: Cloudflare, Google DNS с поддержкой DoH/DoT
- **TUN интерфейс**: Полный VPN режим с автоматической маршрутизацией
- **Selector outbound**: Автоматический выбор лучшего протокола
- **Множественные outbound'ы**: 5 вариантов подключения для надежности
- **Правила маршрутизации**: Обход локального трафика

### Персонализация

Для каждого пользователя:

1. **UUID подстановка**: Уникальный UUID пользователя используется как password/uuid
2. **Серверные параметры**: Настройки сервера из выбранной локации
3. **Транспортные пути**: Уникальные WebSocket пути и gRPC сервисы
4. **TLS настройки**: Корректные server_name для каждой локации

## Интеграция с Django VPN Service

### В views.py

```python
from vpn_service.vpn.trojan_service import PersonalizedSingBoxService

def get_vpn_config(request):
    # Получить пользователя и локацию
    user_account = request.user.useraccount
    location = get_user_location(user_account)
    
    # Получить Hiddify UUID
    hiddify_link = HiddifyLink.objects.get(user=user_account)
    
    # Генерировать конфигурацию
    result = PersonalizedSingBoxService.get_config_for_existing_user(
        hiddify_user_uuid=str(hiddify_link.hiddify_user_uuid),
        location_params=location.hiddify_params,
        user_name=user_account.username
    )
    
    if result['success']:
        return JsonResponse({
            'config': result['singbox_config'],
            'format': 'singbox_json'
        })
    else:
        return JsonResponse({'error': result['error']}, status=500)
```

### В моделях Location

```python
# Пример hiddify_params для модели Location
location = Location.objects.create(
    name="Netherlands - Amsterdam",
    country_code="NL",
    city="Amsterdam",
    flag_emoji="🇳🇱",
    hiddify_params={
        'server': 'nl.vpnserver.com',
        'server_port': 443,
        'tls_server_name': 'nl.vpnserver.com.sslip.io',
        'trojan_ws_path': '/trojan_ws_path_nl',
        'vmess_ws_path': '/vmess_ws_path_nl',
        'vmess_httpupgrade_path': '/vmess_http_path_nl',
        'trojan_grpc_service': 'trojan_grpc_nl',
        'vmess_grpc_service': 'vmess_grpc_nl'
    }
)
```

## Тестирование

### Запуск тестов

```bash
# Основные тесты функциональности
python test_personalized_singbox.py

# Тесты интеграции с Hiddify (требует настроенный API)
python test_personalized_singbox.py --integration
```

### Проверка конфигурации

1. **JSON валидность**: Конфигурация должна быть валидным JSON
2. **UUID подстановка**: Проверить, что UUID пользователя корректно подставлен
3. **Серверные параметры**: Убедиться, что параметры локации применены
4. **Структура outbound'ов**: 5 протокольных + 3 системных outbound'а

## Безопасность

### Принципы

1. **Уникальные UUID**: Каждый пользователь имеет уникальный идентификатор
2. **TLS обязательно**: Все подключения используют TLS шифрование
3. **Изоляция пользователей**: Конфигурации не пересекаются между пользователями
4. **Валидация параметров**: Все входные данные проверяются

### Рекомендации

- Регулярно обновлять транспортные пути
- Мониторить использование UUID для предотвращения дублирования
- Логировать все операции создания конфигураций
- Использовать HTTPS для передачи конфигураций клиентам

## Мониторинг и логирование

### Логи

Система логирует:
- Создание новых пользователей в Hiddify
- Генерацию конфигураций
- Ошибки интеграции с Hiddify Manager
- Использование deprecated методов

### Метрики

Рекомендуется отслеживать:
- Количество созданных конфигураций
- Успешность создания пользователей в Hiddify
- Время генерации конфигураций
- Ошибки валидации параметров локаций

## Миграция с старой системы

### Обратная совместимость

Старые методы `TrojanConfigService` помечены как deprecated, но продолжают работать:

```python
# Старый способ (deprecated)
config = TrojanConfigService.generate_trojan_tun_config(
    server="server.com",
    password="uuid",
    ws_path="/path"
)

# Новый способ
config = PersonalizedSingBoxService.generate_personalized_config(
    hiddify_user_uuid="uuid",
    location_params={'server': 'server.com', 'trojan_ws_path': '/path'}
)
```

### План миграции

1. Обновить все вызовы на новые методы
2. Настроить параметры локаций в базе данных
3. Протестировать генерацию конфигураций
4. Удалить deprecated методы после полной миграции

## Устранение неполадок

### Частые проблемы

1. **Ошибка создания пользователя в Hiddify**
   - Проверить API ключ и URL
   - Убедиться в доступности Hiddify Manager
   - Проверить лимиты API

2. **Неверная структура конфигурации**
   - Проверить параметры локации
   - Убедиться в корректности hiddify_params
   - Проверить UUID пользователя

3. **Проблемы с подключением**
   - Проверить серверные параметры
   - Убедиться в корректности TLS настроек
   - Проверить доступность портов и путей

### Диагностика

```python
# Проверка базового шаблона
template = PersonalizedSingBoxService.load_base_template()
print(f"Template loaded: {len(template['outbounds'])} outbounds")

# Проверка информации о подключении
info = PersonalizedSingBoxService.get_connection_info(location_params)
print(f"Supported protocols: {info['protocols']}")

# Тестовая генерация
config = PersonalizedSingBoxService.generate_personalized_config(
    hiddify_user_uuid="test-uuid",
    location_params=test_params
)
```
