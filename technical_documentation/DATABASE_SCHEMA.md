# 🗄️ Database Schema Documentation

**Последнее обновление:** 2 июня 2025
**СУБД:** PostgreSQL 15 (миграция с SQLite завершена)
**Django Version:** 4.2.7

---

## 📋 Обзор схемы

Система использует 5 основных таблиц для управления пользователями, устройствами, VPN-доступом и подписками.

### Диаграмма связей
```
UserAccount (1) ←→ (N) UserDevice
     ↓ (1)
     ↓
ActiveSubscription (N) → (1) SubscriptionPlan
     ↓ (1)
     ↓
HiddifyLink (1) ← (1) UserAccount
```

---

## 👤 user_accounts

Расширенная модель пользователя Django.

| Поле | Тип | Ограничения | Описание |
|------|-----|-------------|----------|
| **id** | UUID | PRIMARY KEY | Уникальный идентификатор пользователя |
| **email** | VARCHAR(254) | UNIQUE, NOT NULL | Email адрес (используется для входа) |
| **password** | VARCHAR(128) | NOT NULL | Хэшированный пароль |
| **first_name** | VARCHAR(150) | | Имя пользователя |
| **last_name** | VARCHAR(150) | | Фамилия пользователя |
| **phone** | VARCHAR(20) | NULL | Номер телефона |
| **is_email_verified** | BOOLEAN | DEFAULT FALSE | Статус верификации email |
| **last_login_ip** | INET | NULL | IP последнего входа |
| **is_active** | BOOLEAN | DEFAULT TRUE | Активность аккаунта |
| **is_staff** | BOOLEAN | DEFAULT FALSE | Доступ к админ панели |
| **is_superuser** | BOOLEAN | DEFAULT FALSE | Суперпользователь |
| **date_joined** | TIMESTAMP | NOT NULL | Дата регистрации |
| **created_at** | TIMESTAMP | AUTO | Дата создания записи |
| **updated_at** | TIMESTAMP | AUTO | Дата последнего обновления |

**Индексы:**
- `user_accounts_email_unique` (email)
- `user_accounts_is_active` (is_active)

**Пример записи:**
```sql
INSERT INTO user_accounts VALUES (
  '550e8400-e29b-41d4-a716-************',
  '<EMAIL>',
  'pbkdf2_sha256$...',
  '',
  '',
  NULL,
  FALSE,
  NULL,
  TRUE,
  FALSE,
  FALSE,
  '2025-06-01 11:30:51',
  '2025-06-01 11:30:51',
  '2025-06-01 11:30:51'
);
```

---

## 📱 user_devices

Управление устройствами пользователей.

| Поле | Тип | Ограничения | Описание |
|------|-----|-------------|----------|
| **id** | UUID | PRIMARY KEY | Уникальный идентификатор устройства |
| **user_id** | UUID | FOREIGN KEY | Ссылка на пользователя |
| **device_id** | VARCHAR(255) | NOT NULL | Идентификатор устройства от клиента |
| **device_name** | VARCHAR(255) | NOT NULL | Название устройства |
| **device_type** | VARCHAR(50) | NOT NULL | Тип устройства |
| **is_active** | BOOLEAN | DEFAULT TRUE | Активность устройства |
| **last_seen** | TIMESTAMP | NULL | Последняя активность |
| **created_at** | TIMESTAMP | AUTO | Дата добавления |
| **updated_at** | TIMESTAMP | AUTO | Дата обновления |

**Ограничения:**
- `UNIQUE(user_id, device_id)` - Уникальность device_id для пользователя
- `device_type IN ('ios', 'android', 'windows', 'macos', 'linux')`

**Индексы:**
- `user_devices_user_id` (user_id)
- `user_devices_device_id` (device_id)
- `user_devices_is_active` (is_active)

**Пример записи:**
```sql
INSERT INTO user_devices VALUES (
  '660e8400-e29b-41d4-a716-************',
  '550e8400-e29b-41d4-a716-************',
  'my-device-001',
  'iPhone 15 Pro',
  'ios',
  TRUE,
  '2025-06-01 11:35:00',
  '2025-06-01 11:30:51',
  '2025-06-01 11:35:00'
);
```

---

## 🔗 hiddify_links

Связь между Django пользователями и Hiddify Manager.

| Поле | Тип | Ограничения | Описание |
|------|-----|-------------|----------|
| **id** | UUID | PRIMARY KEY | Уникальный идентификатор связи |
| **user_id** | UUID | FOREIGN KEY, UNIQUE | Ссылка на пользователя (1:1) |
| **hiddify_user_uuid** | UUID | UNIQUE, NOT NULL | UUID пользователя в Hiddify |
| **hiddify_username** | VARCHAR(255) | | Имя пользователя в Hiddify |
| **hiddify_comment** | JSON | DEFAULT '{}' | Метаданные для Hiddify |
| **traffic_used_bytes** | BIGINT | DEFAULT 0 | Использованный трафик |
| **traffic_limit_bytes** | BIGINT | DEFAULT 0 | Лимит трафика |
| **last_traffic_sync** | TIMESTAMP | NULL | Последняя синхронизация трафика |
| **is_active_in_hiddify** | BOOLEAN | DEFAULT TRUE | Статус в Hiddify |
| **hiddify_created_at** | TIMESTAMP | NOT NULL | Дата создания в Hiddify |
| **last_config_request** | TIMESTAMP | NULL | Последний запрос конфигурации |
| **created_at** | TIMESTAMP | AUTO | Дата создания записи |
| **updated_at** | TIMESTAMP | AUTO | Дата обновления |

**Индексы:**
- `hiddify_links_user_id_unique` (user_id)
- `hiddify_links_hiddify_user_uuid_unique` (hiddify_user_uuid)
- `hiddify_links_is_active` (is_active_in_hiddify)

**Пример записи:**
```sql
INSERT INTO hiddify_links VALUES (
  '770e8400-e29b-41d4-a716-************',
  '550e8400-e29b-41d4-a716-************',
  '12465e4c-2ad3-4761-8e7e-94efecc05160',
  'user_550e8400',
  '{"account_id": "550e8400-e29b-41d4-a716-************", "device_id": "my-device-001"}',
  0,
  ***********,
  '2025-06-01 11:30:59',
  TRUE,
  '2025-06-01 11:30:51',
  '2025-06-01 11:35:00',
  '2025-06-01 11:30:51',
  '2025-06-01 11:30:59'
);
```

---

## 💳 subscription_plans

Тарифные планы VPN-сервиса.

| Поле | Тип | Ограничения | Описание |
|------|-----|-------------|----------|
| **id** | UUID | PRIMARY KEY | Уникальный идентификатор плана |
| **name** | VARCHAR(100) | UNIQUE, NOT NULL | Название плана |
| **description** | TEXT | | Описание плана |
| **price** | DECIMAL(10,2) | NOT NULL | Цена плана |
| **currency** | VARCHAR(3) | DEFAULT 'USD' | Валюта |
| **duration_days** | INTEGER | NOT NULL | Длительность в днях |
| **traffic_limit_gb** | INTEGER | NOT NULL | Лимит трафика в ГБ |
| **max_devices** | INTEGER | DEFAULT 3 | Максимум устройств |
| **is_active** | BOOLEAN | DEFAULT TRUE | Активность плана |
| **is_trial** | BOOLEAN | DEFAULT FALSE | Пробный план |
| **created_at** | TIMESTAMP | AUTO | Дата создания |

**Индексы:**
- `subscription_plans_name_unique` (name)
- `subscription_plans_is_active` (is_active)
- `subscription_plans_price` (price)

**Предустановленные планы:**
```sql
INSERT INTO subscription_plans VALUES 
('584f1669-be8a-4982-b2b6-19fe69e17377', 'Trial', '7-day trial with 10GB traffic', 0.00, 'USD', 7, 10, 2, TRUE, TRUE, '2025-06-01 10:00:00'),
('684f1669-be8a-4982-b2b6-19fe69e17378', 'Basic', 'Monthly plan with 100GB traffic', 9.99, 'USD', 30, 100, 3, TRUE, FALSE, '2025-06-01 10:00:00'),
('784f1669-be8a-4982-b2b6-19fe69e17379', 'Premium', 'Monthly plan with unlimited traffic', 19.99, 'USD', 30, 1000, 5, TRUE, FALSE, '2025-06-01 10:00:00');
```

---

## 📅 active_subscriptions

Активные подписки пользователей.

| Поле | Тип | Ограничения | Описание |
|------|-----|-------------|----------|
| **id** | UUID | PRIMARY KEY | Уникальный идентификатор подписки |
| **user_id** | UUID | FOREIGN KEY | Ссылка на пользователя |
| **plan_id** | UUID | FOREIGN KEY | Ссылка на план |
| **start_date** | TIMESTAMP | NOT NULL | Дата начала подписки |
| **end_date** | TIMESTAMP | NOT NULL | Дата окончания подписки |
| **is_active** | BOOLEAN | DEFAULT TRUE | Активность подписки |
| **auto_renew** | BOOLEAN | DEFAULT FALSE | Автопродление |
| **payment_method** | VARCHAR(50) | | Способ оплаты |
| **created_at** | TIMESTAMP | AUTO | Дата создания |

**Ограничения:**
- `CHECK (end_date > start_date)` - Корректность дат
- `UNIQUE(user_id, is_active)` WHERE `is_active = TRUE` - Одна активная подписка

**Индексы:**
- `active_subscriptions_user_id_is_active` (user_id, is_active)
- `active_subscriptions_end_date` (end_date)
- `active_subscriptions_plan_id` (plan_id)

**Пример записи:**
```sql
INSERT INTO active_subscriptions VALUES (
  '6b9b03cb-956c-44ec-8dad-a531748c100a',
  '550e8400-e29b-41d4-a716-************',
  '584f1669-be8a-4982-b2b6-19fe69e17377',
  '2025-06-01 11:30:51',
  '2025-06-08 11:30:51',
  TRUE,
  FALSE,
  '',
  '2025-06-01 11:30:51'
);
```

---

## 🔍 Полезные запросы

### Получение пользователя с активной подпиской
```sql
SELECT 
  ua.email,
  sp.name as plan_name,
  asub.end_date,
  hl.hiddify_user_uuid
FROM user_accounts ua
JOIN active_subscriptions asub ON ua.id = asub.user_id
JOIN subscription_plans sp ON asub.plan_id = sp.id
JOIN hiddify_links hl ON ua.id = hl.user_id
WHERE asub.is_active = TRUE
  AND asub.end_date > NOW();
```

### Статистика по планам
```sql
SELECT 
  sp.name,
  COUNT(asub.id) as active_subscriptions,
  SUM(sp.price) as total_revenue
FROM subscription_plans sp
LEFT JOIN active_subscriptions asub ON sp.id = asub.plan_id 
  AND asub.is_active = TRUE
GROUP BY sp.id, sp.name;
```

### Пользователи с истекающими подписками
```sql
SELECT 
  ua.email,
  sp.name,
  asub.end_date,
  EXTRACT(DAY FROM asub.end_date - NOW()) as days_remaining
FROM user_accounts ua
JOIN active_subscriptions asub ON ua.id = asub.user_id
JOIN subscription_plans sp ON asub.plan_id = sp.id
WHERE asub.is_active = TRUE
  AND asub.end_date BETWEEN NOW() AND NOW() + INTERVAL '7 days';
```

### Статистика трафика
```sql
SELECT 
  ua.email,
  hl.traffic_used_bytes / 1024 / 1024 / 1024 as used_gb,
  hl.traffic_limit_bytes / 1024 / 1024 / 1024 as limit_gb,
  ROUND(
    (hl.traffic_used_bytes::float / hl.traffic_limit_bytes::float) * 100, 2
  ) as usage_percent
FROM user_accounts ua
JOIN hiddify_links hl ON ua.id = hl.user_id
WHERE hl.traffic_limit_bytes > 0;
```

---

## 🔧 Миграции

### Статус миграций
```
accounts.0001_initial - ✅ Применена
subscriptions.0001_initial - ✅ Применена
```

### Создание миграций
```bash
# Создание новой миграции
python manage.py makemigrations

# Применение миграций
python manage.py migrate

# Просмотр статуса
python manage.py showmigrations
```

### Откат миграций
```bash
# Откат к предыдущей миграции
python manage.py migrate accounts 0001

# Полный откат
python manage.py migrate accounts zero
```

---

## 🚀 Оптимизация для продакшена

### Рекомендуемые индексы для PostgreSQL
```sql
-- Составные индексы для частых запросов
CREATE INDEX idx_user_active_subscription 
ON active_subscriptions (user_id, is_active, end_date);

CREATE INDEX idx_hiddify_traffic_sync 
ON hiddify_links (last_traffic_sync, is_active_in_hiddify);

-- Частичные индексы
CREATE INDEX idx_active_subscriptions_active 
ON active_subscriptions (user_id, end_date) 
WHERE is_active = TRUE;
```

### Настройки подключения
```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'vpn_service',
        'USER': 'vpn_user',
        'PASSWORD': 'secure_password',
        'HOST': 'localhost',
        'PORT': '5432',
        'OPTIONS': {
            'MAX_CONNS': 20,
            'CONN_MAX_AGE': 600,
        }
    }
}
```

---

## 📊 Мониторинг

### Ключевые метрики
- Количество активных пользователей
- Использование трафика по планам
- Частота запросов конфигураций
- Время отклика запросов к БД

### Запросы для мониторинга
```sql
-- Активные пользователи
SELECT COUNT(*) FROM user_accounts WHERE is_active = TRUE;

-- Активные подписки
SELECT COUNT(*) FROM active_subscriptions 
WHERE is_active = TRUE AND end_date > NOW();

-- Средний размер БД
SELECT pg_size_pretty(pg_database_size('vpn_service'));
```

---

**Документация актуальна на:** 1 июня 2025  
**Версия схемы:** 1.0  
**Поддержка:** Команда разработки VPN Service
