# Руководство по миграции: Система анонимных пользователей

## Обзор изменений

Данная миграция добавляет поддержку анонимных пользователей в VPN-сервис, позволяя пользователям получать доступ без предварительной регистрации.

## Шаги миграции

### 1. Резервное копирование базы данных

```bash
# Создайте резервную копию перед миграцией
pg_dump your_database_name > backup_before_anonymous_users.sql
```

### 2. Применение миграций

```bash
cd /path/to/vpn_service
python manage.py migrate accounts
```

Будут применены следующие миграции:
- `0004_add_anonymous_user_support` - добавление поля `is_anonymous` и constraint
- `0005_add_anonymous_user_support` - обновление менеджера модели

### 3. Проверка миграции

```bash
# Проверьте, что миграции применились успешно
python manage.py showmigrations accounts

# Проверьте структуру таблицы
python manage.py dbshell
\d user_accounts
```

### 4. Проверка работоспособности

```bash
# Запустите тесты системы
python manage.py check

# Запустите сервер
python manage.py runserver 0.0.0.0:8090
```

## Изменения в базе данных

### Таблица `user_accounts`

#### Добавленные поля:
- `is_anonymous` (boolean, default: false) - флаг анонимного пользователя

#### Измененные поля:
- `email` - теперь может быть NULL и не обязательно уникальным

#### Добавленные constraints:
- `unique_email_when_not_null` - обеспечивает уникальность email только когда он не NULL

## Обратная совместимость

### Существующие пользователи
- Все существующие пользователи автоматически получают `is_anonymous = false`
- Их email остается уникальным и обязательным
- Никаких изменений в поведении не происходит

### Существующие API
- Все существующие эндпоинты работают без изменений
- `/api/auth/register/` и `/api/auth/login/` функционируют как прежде
- JWT токены для зарегистрированных пользователей не изменились

## Новые возможности

### Новые эндпоинты:
- `POST /api/auth/device/init/` - инициализация устройства
- `POST /api/auth/convert-anonymous/` - конвертация анонимного аккаунта

### Новые поля в JWT токенах:
- `device_id` - для всех пользователей
- `hiddify_uuid` - UUID пользователя в Hiddify Manager

## Мониторинг после миграции

### Проверьте логи на наличие ошибок:
```bash
tail -f /path/to/logs/django.log
```

### Проверьте работу новых эндпоинтов:
```bash
# Тест инициализации устройства
curl -X POST http://localhost:8090/api/auth/device/init/ \
  -H "Content-Type: application/json" \
  -d '{"device_id": "test-123", "device_name": "Test Device", "device_type": "ios"}'
```

### Мониторинг метрик:
- Количество анонимных пользователей: `SELECT COUNT(*) FROM user_accounts WHERE is_anonymous = true;`
- Количество конвертаций: `SELECT COUNT(*) FROM user_accounts WHERE is_anonymous = false AND email IS NOT NULL;`

## Откат миграции (если необходимо)

⚠️ **ВНИМАНИЕ**: Откат приведет к потере данных анонимных пользователей!

```bash
# Откат к предыдущему состоянию
python manage.py migrate accounts 0003_merge_20250602_0550

# Восстановление из резервной копии
psql your_database_name < backup_before_anonymous_users.sql
```

## Поддержка

При возникновении проблем:

1. Проверьте логи Django и PostgreSQL
2. Убедитесь, что все зависимости установлены
3. Проверьте настройки `HIDDIFY_*` в settings.py
4. Обратитесь к документации в `ANONYMOUS_USERS_IMPLEMENTATION.md`

## Производительность

Миграция добавляет:
- 1 новое поле в таблицу `user_accounts`
- 1 новый constraint
- Минимальное влияние на производительность существующих запросов

Рекомендуется мониторить производительность запросов к таблице `user_accounts` в первые дни после миграции.
