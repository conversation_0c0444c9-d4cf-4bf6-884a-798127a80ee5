# 🔌 API Endpoints Reference - Stage 1 Unified

**Последнее обновление:** 2 июня 2025 (верификация документации)
**Версия API:** 1.0-unified
**Base URL:** `http://127.0.0.1:8080/api/` (Production: Nginx + Gunicorn)

## 🎯 Stage 1 Unified Architecture

**Архитектурное решение:** Единый Legacy API для завершения Этапа 1.

**Двухуровневая аутентификация:**
- **Уровень 1:** Account (email + password) → JWT токен
- **Уровень 2:** Device ID (в JWT claims) → Привязка к устройству

**Основные endpoints для Stage 1:**
- `POST /api/auth/register/` - Регистрация пользователя с устройством
- `GET /api/vpn/config/` - Получение SingBox VPN конфигурации
- `GET /api/auth/activation-code/` - Генерация кода активации
- `POST /api/auth/activate/` - Активация нового устройства

---

## 🚀 Полный цикл Stage 1 (Двухуровневая аутентификация)

### Сценарий 1: Новый пользователь
```bash
# Шаг 1: Регистрация пользователя с устройством
curl -X POST http://127.0.0.1:8080/api/auth/register/ \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "securepass123",
    "password_confirm": "securepass123",
    "device_id": "my-device-001",
    "device_name": "My iPhone",
    "device_type": "ios"
  }'

# Ответ: JWT токены + subscription info

# Шаг 2: Получение VPN конфигурации
curl -X GET "http://127.0.0.1:8080/api/vpn/config/" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# Ответ: SingBox JSON конфигурация
```

### Сценарий 2: Перенос на новое устройство
```bash
# На старом устройстве: генерация кода активации
curl -X GET "http://127.0.0.1:8080/api/auth/activation-code/" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# Ответ: {"activation_code": "A1B2C3D4", "expires_at": "..."}

# На новом устройстве: активация
curl -X POST "http://127.0.0.1:8080/api/auth/activate/" \
  -H "Content-Type: application/json" \
  -d '{
    "activation_code": "A1B2C3D4",
    "device_id": "new-device-002",
    "device_name": "My Android",
    "device_type": "android"
  }'

# Ответ: JWT токены для нового устройства
```

---

## 📋 Общая информация

### Аутентификация
Все защищенные endpoints требуют JWT токен в заголовке:
```
Authorization: Bearer <access_token>
```

### Форматы данных
- **Request:** `application/json`
- **Response:** `application/json`
- **Кодировка:** UTF-8

### Коды ответов
| Код | Описание |
|-----|----------|
| 200 | OK - Успешный запрос |
| 201 | Created - Ресурс создан |
| 400 | Bad Request - Ошибка валидации |
| 401 | Unauthorized - Требуется аутентификация |
| 403 | Forbidden - Доступ запрещен |
| 404 | Not Found - Ресурс не найден |
| 500 | Internal Server Error - Ошибка сервера |

---

## 🔐 Аутентификация

### POST /auth/register/
Регистрация нового пользователя с автоматическим созданием VPN доступа.

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "securepassword123",
  "password_confirm": "securepassword123",
  "device_id": "unique-device-identifier",
  "device_name": "iPhone 15 Pro",
  "device_type": "ios"
}
```

**Response (201):**
```json
{
  "success": true,
  "user": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "email": "<EMAIL>",
    "created_at": "2025-06-01T11:30:51.408327+00:00"
  },
  "subscription": {
    "plan_name": "Trial",
    "end_date": "2025-06-08T11:30:51.711795+00:00",
    "traffic_limit_gb": 10
  },
  "tokens": {
    "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
  }
}
```

**Валидация:**
- `email`: Уникальный email адрес
- `password`: Минимум 8 символов
- `password_confirm`: Должен совпадать с password
- `device_id`: Уникальный идентификатор устройства
- `device_type`: `ios`, `android`, `windows`, `macos`, `linux`

### POST /auth/login/
Аутентификация пользователя.

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "securepassword123",
  "device_id": "unique-device-identifier"
}
```

**Response (200):**
```json
{
  "success": true,
  "user": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "email": "<EMAIL>"
  },
  "tokens": {
    "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
  }
}
```

### POST /auth/token/refresh/
Обновление access токена.

**Request:**
```json
{
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

**Response (200):**
```json
{
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

### GET /auth/profile/
Получение профиля текущего пользователя.

**Headers:**
```
Authorization: Bearer <access_token>
```

**Response (200):**
```json
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "email": "<EMAIL>",
  "phone": null,
  "is_email_verified": false,
  "created_at": "2025-06-01T11:30:51.408327Z",
  "devices_count": 1
}
```

### GET /auth/activation-code/
Генерация кода активации для переноса VPN доступа на новое устройство.

**Headers:**
```
Authorization: Bearer <access_token>
```

**Response (200):**
```json
{
  "activation_code": "A1B2C3D4",
  "expires_at": "2025-06-02T12:30:51.408327Z",
  "message": "Activation code generated successfully"
}
```

**Особенности:**
- Код действителен 24 часа
- Одновременно может быть активен только один код
- Генерация нового кода отменяет предыдущий

### POST /auth/activate/
Активация VPN доступа на новом устройстве с помощью кода активации.

**Request:**
```json
{
  "activation_code": "A1B2C3D4",
  "device_id": "new-device-identifier",
  "device_name": "My Android Phone",
  "device_type": "android"
}
```

**Response (200):**
```json
{
  "success": true,
  "message": "Device activated successfully",
  "user": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "email": "<EMAIL>"
  },
  "tokens": {
    "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
  }
}
```

**Валидация:**
- `activation_code`: 8-символьный код (A-Z, 0-9)
- `device_id`: Уникальный идентификатор нового устройства
- `device_type`: `ios`, `android`, `windows`, `macos`, `linux`

---

## 🔒 VPN Конфигурации

### GET /vpn/config/
Получение SingBox VPN конфигурации (Stage 1 Focus).

**Headers:**
```
Authorization: Bearer <access_token>
```

**Stage 1 Implementation:**
- Возвращает только SingBox конфигурации
- Параметр `type` не требуется (фиксированно SingBox)
- Clash и Subscription конфигурации будут добавлены в Stage 2-3

**Example:**

#### SingBox Configuration
```
GET /vpn/config/
```

**Response (200):**
```json
{
  "success": true,
  "config_type": "singbox",
  "config": {
    "outbounds": [
      {
        "type": "vless",
        "tag": "proxy",
        "server": "ductuspro.ru",
        "server_port": 443,
        "uuid": "12465e4c-2ad3-4761-8e7e-94efecc05160",
        "tls": {
          "enabled": true,
          "server_name": "ductuspro.ru"
        }
      }
    ],
    "inbounds": [
      {
        "type": "tun",
        "tag": "tun-in",
        "interface_name": "tun0",
        "inet4_address": "**********/30",
        "auto_route": true,
        "strict_route": false
      }
    ],
    "route": {
      "rules": [
        {
          "outbound": "proxy"
        }
      ]
    }
  },
  "subscription_info": {
    "plan_name": "Trial",
    "traffic_limit_gb": 10,
    "traffic_used_gb": 0.0,
    "end_date": "2025-06-08T11:30:51.711795+00:00"
  }
}
```

**Note:** Clash и Subscription конфигурации будут добавлены в Stage 2-3:
- **Stage 2:** Поддержка Clash конфигураций (`GET /vpn/config/?type=clash`)
- **Stage 3:** Поддержка Subscription ссылок (`GET /vpn/config/?type=subscription`)

### GET /vpn/stats/
Получение статистики использования трафика.

**Headers:**
```
Authorization: Bearer <access_token>
```

**Response (200):**
```json
{
  "success": true,
  "traffic_stats": {
    "upload_bytes": 1048576,
    "download_bytes": 5242880,
    "total_used_bytes": 6291456,
    "total_used_gb": 0.006,
    "limit_gb": 10,
    "limit_bytes": 10737418240,
    "last_sync": "2025-06-01T11:30:59.357004+00:00"
  },
  "subscription_info": {
    "plan_name": "Trial",
    "end_date": "2025-06-08T11:30:51.711795+00:00",
    "days_remaining": 6
  }
}
```

---

## 💳 Подписки

### GET /subscriptions/plans/
Получение списка доступных тарифных планов.

**Response (200):**
```json
[
  {
    "id": "584f1669-be8a-4982-b2b6-19fe69e17377",
    "name": "Trial",
    "description": "7-day trial with 10GB traffic",
    "price": "0.00",
    "currency": "USD",
    "duration_days": 7,
    "traffic_limit_gb": 10,
    "max_devices": 2,
    "is_trial": true,
    "is_active": true
  },
  {
    "id": "684f1669-be8a-4982-b2b6-19fe69e17378",
    "name": "Basic",
    "description": "Monthly plan with 100GB traffic",
    "price": "9.99",
    "currency": "USD",
    "duration_days": 30,
    "traffic_limit_gb": 100,
    "max_devices": 3,
    "is_trial": false,
    "is_active": true
  }
]
```

### GET /subscriptions/current/
Получение информации о текущей подписке пользователя.

**Headers:**
```
Authorization: Bearer <access_token>
```

**Response (200):**
```json
{
  "id": "6b9b03cb-956c-44ec-8dad-a531748c100a",
  "plan": {
    "id": "584f1669-be8a-4982-b2b6-19fe69e17377",
    "name": "Trial",
    "description": "7-day trial with 10GB traffic",
    "price": "0.00",
    "currency": "USD",
    "duration_days": 7,
    "traffic_limit_gb": 10,
    "max_devices": 2,
    "is_trial": true
  },
  "start_date": "2025-06-01T11:30:51.711784Z",
  "end_date": "2025-06-08T11:30:51.711795Z",
  "is_active": true,
  "auto_renew": false,
  "days_remaining": 6,
  "is_expired": false,
  "created_at": "2025-06-01T11:30:51.712802Z",
  "traffic_info": {
    "traffic_used_gb": 0.006,
    "traffic_limit_gb": 10,
    "last_sync": "2025-06-01T11:30:59.357004+00:00"
  }
}
```

**Response (404) - Нет активной подписки:**
```json
{
  "error": "No active subscription found"
}
```

---

## 📊 Документация API

### GET /docs/
Swagger UI документация (интерактивная).

### GET /redoc/
ReDoc документация (статичная).

### GET /schema/
OpenAPI 3.0 схема в JSON формате.

---

## ⚠️ Обработка ошибок

### Стандартный формат ошибок
```json
{
  "error": "Описание ошибки",
  "details": {
    "field_name": ["Список ошибок валидации"]
  }
}
```

### Примеры ошибок

**400 Bad Request - Валидация:**
```json
{
  "error": "Validation failed",
  "details": {
    "email": ["This field is required."],
    "password": ["Password must be at least 8 characters long."]
  }
}
```

**401 Unauthorized:**
```json
{
  "error": "Authentication credentials were not provided."
}
```

**403 Forbidden:**
```json
{
  "error": "You do not have permission to perform this action."
}
```

**404 Not Found:**
```json
{
  "error": "Resource not found"
}
```

**500 Internal Server Error:**
```json
{
  "error": "Internal server error. Please try again later."
}
```

---

## 🔧 Rate Limiting

| Endpoint | Лимит | Период |
|----------|-------|--------|
| `/auth/register/` | 5 запросов | 1 час |
| `/auth/login/` | 10 запросов | 1 час |
| `/vpn/config/` | 60 запросов | 1 час |
| `/vpn/stats/` | 120 запросов | 1 час |
| Остальные | 1000 запросов | 1 час |

**Заголовки rate limiting:**
```
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 59
X-RateLimit-Reset: 1625097600
```

---

## 📝 Примеры использования

### Полный цикл регистрации и получения VPN (Stage 1 Unified)
```bash
# 1. Регистрация пользователя с устройством
curl -X POST http://127.0.0.1:8080/api/auth/register/ \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "securepass123",
    "password_confirm": "securepass123",
    "device_id": "my-device-001",
    "device_name": "My iPhone",
    "device_type": "ios"
  }'

# 2. Получение SingBox конфигурации (Stage 1: только SingBox)
curl -X GET "http://127.0.0.1:8080/api/vpn/config/" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# 3. Проверка статистики
curl -X GET "http://127.0.0.1:8080/api/vpn/stats/" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### Перенос VPN доступа на новое устройство
```bash
# На старом устройстве: генерация кода активации
curl -X GET "http://127.0.0.1:8080/api/auth/activation-code/" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# На новом устройстве: активация
curl -X POST "http://127.0.0.1:8080/api/auth/activate/" \
  -H "Content-Type: application/json" \
  -d '{
    "activation_code": "A1B2C3D4",
    "device_id": "new-device-002",
    "device_name": "My Android",
    "device_type": "android"
  }'

# Получение конфигурации на новом устройстве
curl -X GET "http://127.0.0.1:8080/api/vpn/config/" \
  -H "Authorization: Bearer NEW_ACCESS_TOKEN"
```

---

## ⚠️ DEPRECATED ENDPOINTS

The following endpoints are deprecated and excluded from Stage 1:
- ~~POST /api/v1/device/register/~~ → Use `POST /api/auth/register/`
- ~~GET /api/v1/device/config/~~ → Use `GET /api/vpn/config/`

**Migration guide:** See STAGE1_FRONTEND_INTEGRATION_GUIDE.md

---

**Документация актуальна на:** 2 июня 2025
**Версия API:** 1.0-unified
**Поддержка:** Техническая команда VPN Service
