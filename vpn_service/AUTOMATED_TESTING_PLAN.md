# 🧪 План автоматизированных тестов VPN-сервиса

## 🎯 Цель
Создать комплексную систему автоматизированных тестов с использованием `pytest-django` и `APIClient` для обеспечения стабильности и надежности системы.

---

## 📁 Структура тестов

```
tests/
├── __init__.py
├── conftest.py                 # Общие фикстуры
├── test_auth.py               # Тесты аутентификации
├── test_devices.py            # Тесты управления устройствами
├── test_subscriptions.py      # Тесты подписок
├── test_vpn.py               # Тесты VPN конфигураций
├── test_promo.py             # Тесты промокодов
├── test_admin.py             # Тесты админ-панели
├── test_security.py          # Тесты безопасности
└── test_integration.py       # Интеграционные тесты
```

---

## 🔧 Базовая настройка

### `tests/conftest.py`
```python
import pytest
from django.test import TestCase
from rest_framework.test import APIClient
from accounts.models import UserAccount, UserDevice
from subscriptions.models import SubscriptionPlan, ActiveSubscription
from unittest.mock import patch

@pytest.fixture
def api_client():
    """API клиент для тестов."""
    return APIClient()

@pytest.fixture
def free_plan():
    """Бесплатный тарифный план."""
    return SubscriptionPlan.objects.create(
        name="Free Test Plan",
        price=0.00,
        duration_days=30,
        traffic_limit_gb=10,
        max_devices=1,
        is_active=True,
        is_free_default=True
    )

@pytest.fixture
def premium_plan():
    """Премиум тарифный план."""
    return SubscriptionPlan.objects.create(
        name="Premium Test Plan",
        price=9.99,
        duration_days=30,
        traffic_limit_gb=100,
        max_devices=5,
        is_active=True
    )

@pytest.fixture
def anonymous_user(free_plan):
    """Анонимный пользователь с устройством."""
    user = UserAccount.objects.create_anonymous_user()
    device = UserDevice.objects.create(
        user=user,
        device_id="test-anonymous-device",
        device_name="Test Anonymous Device"
    )
    return user, device

@pytest.fixture
def registered_user():
    """Зарегистрированный пользователь."""
    return UserAccount.objects.create_user(
        email="<EMAIL>",
        password="testpass123",
        username="<EMAIL>"
    )

@pytest.fixture
def mock_hiddify_service():
    """Мок для HiddifyApiService."""
    with patch('vpn.views.HiddifyApiService') as mock:
        mock_instance = mock.return_value
        mock_instance.create_hiddify_user.return_value = (True, {'uuid': 'test-uuid'})
        mock_instance.get_singbox_config_for_user.return_value = (
            True, 
            {
                'config': {'outbounds': []},
                'traffic_info': {'upload_bytes': 0, 'download_bytes': 1000}
            }
        )
        yield mock_instance
```

---

## 🔐 Тесты аутентификации

### `tests/test_auth.py`
```python
class TestDeviceRegistration:
    """Тесты регистрации устройств."""
    
    def test_register_new_device_success(self, api_client, free_plan):
        """Тест успешной регистрации нового устройства."""
        data = {
            "device_id": "new-device-123",
            "device_name": "Test Device",
            "device_type": "mobile"
        }
        response = api_client.post('/api/devices/register', data)
        
        assert response.status_code == 201
        assert response.data['success'] is True
        assert 'tokens' in response.data
        assert 'user' in response.data
        assert 'subscription' in response.data

    def test_restore_session_with_secret_success(self, api_client, anonymous_user):
        """Тест восстановления сессии по device_secret."""
        user, device = anonymous_user
        data = {
            "device_id": device.device_id,
            "device_secret": str(device.device_secret)
        }
        response = api_client.post('/api/devices/register', data)
        
        assert response.status_code == 200
        assert response.data['success'] is True

    def test_register_device_invalid_data(self, api_client):
        """Тест регистрации с некорректными данными."""
        data = {"device_id": ""}  # Пустой device_id
        response = api_client.post('/api/devices/register', data)
        
        assert response.status_code == 400

class TestUserRegistration:
    """Тесты регистрации пользователей."""
    
    def test_register_new_user_success(self, api_client, free_plan):
        """Тест регистрации нового пользователя."""
        data = {
            "email": "<EMAIL>",
            "password": "securepass123",
            "password_confirm": "securepass123",
            "device_id": "new-user-device"
        }
        response = api_client.post('/api/auth/register', data)
        
        assert response.status_code == 201
        assert response.data['success'] is True

    def test_register_with_existing_email_fails_409(self, api_client, registered_user):
        """Тест регистрации с существующим email."""
        data = {
            "email": registered_user.email,
            "password": "securepass123",
            "password_confirm": "securepass123",
            "device_id": "conflict-device"
        }
        response = api_client.post('/api/auth/register', data)
        
        assert response.status_code == 409

    def test_convert_anonymous_user_success(self, api_client, anonymous_user):
        """Тест конвертации анонимного пользователя."""
        user, device = anonymous_user
        api_client.force_authenticate(user=user)
        
        data = {
            "email": "<EMAIL>",
            "password": "securepass123",
            "password_confirm": "securepass123"
        }
        response = api_client.post('/api/auth/register', data)
        
        assert response.status_code == 201
        user.refresh_from_db()
        assert user.email == "<EMAIL>"
        assert not user.is_anonymous
```

---

## 🌐 Тесты VPN конфигураций

### `tests/test_vpn.py`
```python
class TestVPNConfig:
    """Тесты получения VPN конфигураций."""
    
    def test_get_config_unauthenticated_fails_401(self, api_client):
        """Тест получения конфига без аутентификации."""
        response = api_client.get('/api/vpn/config')
        assert response.status_code == 401

    def test_get_config_no_subscription_fails_403(self, api_client, registered_user):
        """Тест получения конфига без активной подписки."""
        api_client.force_authenticate(user=registered_user)
        response = api_client.get('/api/vpn/config')
        assert response.status_code == 404

    def test_get_config_for_active_user_success(self, api_client, anonymous_user, mock_hiddify_service):
        """Тест успешного получения конфига."""
        user, device = anonymous_user
        api_client.force_authenticate(user=user)
        
        response = api_client.get('/api/vpn/config')
        
        assert response.status_code == 200
        assert 'outbounds' in response.data
        assert len(response.data['outbounds']) > 0

    def test_config_personalization(self, api_client, anonymous_user, mock_hiddify_service):
        """Тест персонализации конфигурации."""
        user, device = anonymous_user
        api_client.force_authenticate(user=user)
        
        response = api_client.get('/api/vpn/config')
        
        # Проверяем, что конфиг персонализирован
        config_str = str(response.data)
        assert 'test-uuid' in config_str  # hiddify_uuid из мока
```

---

## 💳 Тесты подписок и промокодов

### `tests/test_subscriptions.py`
```python
class TestSubscriptionActivation:
    """Тесты активации подписок."""
    
    def test_activate_subscription_success(self, api_client, registered_user, premium_plan):
        """Тест успешной активации подписки."""
        api_client.force_authenticate(user=registered_user)
        
        data = {"plan_id": str(premium_plan.id)}
        response = api_client.post('/api/subscriptions/activate', data)
        
        assert response.status_code == 201
        assert response.data['success'] is True

### `tests/test_promo.py`
```python
class TestPromoCodeActivation:
    """Тесты активации промокодов."""
    
    def test_activate_promo_code_success(self, api_client, registered_user, premium_plan):
        """Тест успешной активации промокода."""
        from promo.models import PromoCode
        
        promo = PromoCode.objects.create(code="TESTCODE", plan=premium_plan)
        api_client.force_authenticate(user=registered_user)
        
        data = {"promo_code": "TESTCODE"}
        response = api_client.post('/api/promo/activate', data)
        
        assert response.status_code == 200
        assert response.data['success'] is True
```

---

## 🔒 Тесты безопасности

### `tests/test_security.py`
```python
class TestSecurityMeasures:
    """Тесты мер безопасности."""
    
    def test_device_conflict_detection(self, api_client, anonymous_user):
        """Тест обнаружения конфликтов устройств."""
        user1, device1 = anonymous_user
        
        # Создаем второго пользователя с тем же device_id
        user2 = UserAccount.objects.create_anonymous_user()
        UserDevice.objects.create(
            user=user2,
            device_id=device1.device_id,  # Тот же device_id
            device_name="Conflict Device"
        )
        
        # Пытаемся активировать код
        from accounts.models import ActivationCode
        code = ActivationCode.objects.create(
            code="TESTCODE",
            user=user1,
            expires_at=timezone.now() + timedelta(minutes=15)
        )
        
        data = {
            "activation_code": "TESTCODE",
            "device_id": device1.device_id,
            "device_name": "Test Device"
        }
        response = api_client.post('/api/auth/activate', data)
        
        assert response.status_code == 409  # Conflict
```

---

## 🚀 Запуск тестов

### Команды для выполнения
```bash
# Все тесты
pytest tests/ -v

# Конкретный модуль
pytest tests/test_auth.py -v

# С покрытием кода
pytest tests/ --cov=. --cov-report=html

# Только быстрые тесты (без внешних API)
pytest tests/ -m "not slow" -v
```

### Настройка CI/CD
```yaml
# .github/workflows/tests.yml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: 3.11
      - name: Install dependencies
        run: pip install -r requirements.txt
      - name: Run tests
        run: pytest tests/ --cov=. --cov-report=xml
```

---

## 📊 Метрики качества

### Целевые показатели
- **Покрытие кода:** > 85%
- **Время выполнения:** < 30 секунд
- **Количество тестов:** > 50
- **Успешность:** 100%

### Отчетность
- Ежедневные отчеты о покрытии
- Уведомления о падающих тестах
- Метрики производительности тестов
