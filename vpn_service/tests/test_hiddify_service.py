"""
Tests for HiddifyApiService.
"""
import pytest
from unittest.mock import Mock, patch
from vpn.services import HiddifyApiService


class TestHiddifyApiService:
    """Test cases for HiddifyApiService."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.service = HiddifyApiService()
    
    @patch('vpn.services.requests.request')
    def test_create_hiddify_user_success(self, mock_request):
        """Test successful user creation."""
        # Mock successful response
        mock_response = Mock()
        mock_response.status_code = 201
        mock_response.json.return_value = {'uuid': 'test-uuid-123'}
        mock_request.return_value = mock_response
        
        # Test the method
        success, result = self.service.create_hiddify_user(
            name="test_user",
            usage_limit_gb=10,
            package_days=30,
            comment_json_string='{"test": "data"}'
        )
        
        # Assertions
        assert success is True
        assert result['uuid'] == 'test-uuid-123'
        mock_request.assert_called_once()
    
    @patch('vpn.services.requests.request')
    def test_create_hiddify_user_failure(self, mock_request):
        """Test failed user creation."""
        # Mock failed response
        mock_response = Mock()
        mock_response.status_code = 400
        mock_response.text = 'Bad Request'
        mock_request.return_value = mock_response
        
        # Test the method
        success, result = self.service.create_hiddify_user(
            name="test_user",
            usage_limit_gb=10,
            package_days=30,
            comment_json_string='{"test": "data"}'
        )
        
        # Assertions
        assert success is False
        assert result['status_code'] == 400
        assert result['error'] == 'Bad Request'
    
    @patch('vpn.services.requests.request')
    def test_get_singbox_config_success(self, mock_request):
        """Test successful SingBox config retrieval."""
        # Mock successful response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.side_effect = Exception("Not JSON")  # Simulate non-JSON response
        mock_response.text = '{"outbounds": []}'
        mock_response.headers = {
            'subscription-userinfo': 'upload=0;download=1000;total=10000000;expire=1234567890'
        }
        mock_request.return_value = mock_response
        
        # Test the method
        success, result = self.service.get_singbox_config_for_user('test-uuid')
        
        # Assertions
        assert success is True
        assert 'config' in result
        assert 'traffic_info' in result
        assert result['traffic_info']['download_bytes'] == 1000
    
    def test_extract_traffic_info(self):
        """Test traffic info extraction from headers."""
        headers = {
            'subscription-userinfo': 'upload=500;download=1500;total=10000000;expire=1234567890'
        }
        
        traffic_info = self.service._extract_traffic_info(headers)
        
        assert traffic_info['upload_bytes'] == 500
        assert traffic_info['download_bytes'] == 1500
        assert traffic_info['total_bytes'] == 10000000
        assert traffic_info['expire_timestamp'] == 1234567890
    
    def test_extract_traffic_info_empty(self):
        """Test traffic info extraction with empty headers."""
        headers = {}
        
        traffic_info = self.service._extract_traffic_info(headers)
        
        assert traffic_info['upload_bytes'] == 0
        assert traffic_info['download_bytes'] == 0
        assert traffic_info['total_bytes'] == 0
        assert traffic_info['expire_timestamp'] is None
    
    @patch('vpn.services.requests.request')
    def test_request_timeout(self, mock_request):
        """Test request timeout handling."""
        from requests.exceptions import Timeout
        
        mock_request.side_effect = Timeout("Request timed out")
        
        success, result = self.service.create_hiddify_user(
            name="test_user",
            usage_limit_gb=10,
            package_days=30,
            comment_json_string='{"test": "data"}'
        )
        
        assert success is False
        assert 'timeout' in result['error'].lower()
    
    @patch('vpn.services.requests.request')
    def test_connection_error(self, mock_request):
        """Test connection error handling."""
        from requests.exceptions import ConnectionError
        
        mock_request.side_effect = ConnectionError("Connection failed")
        
        success, result = self.service.create_hiddify_user(
            name="test_user",
            usage_limit_gb=10,
            package_days=30,
            comment_json_string='{"test": "data"}'
        )
        
        assert success is False
        assert 'connection' in result['error'].lower()
