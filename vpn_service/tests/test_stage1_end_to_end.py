"""
Сквозные тесты для подтверждения готовности Этапа 1.

Эти тесты проверяют полный цикл работы унифицированного Legacy API
и подтверждают выполнение критерия готовности Stage 1:
"Тестовый пользователь может получить конфигурацию через API, 
подключиться к VPN и получить доступ к заблокированным ресурсам"
"""
import json
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from unittest.mock import patch, Mock
from accounts.models import UserAccount, UserDevice, HiddifyLink, ActivationCode
from subscriptions.models import SubscriptionPlan, ActiveSubscription


class Stage1EndToEndTest(TestCase):
    """
    Критические тесты для подтверждения готовности Этапа 1.
    
    Проверяет полный цикл унифицированного Legacy API:
    1. Регистрация пользователя с устройством
    2. Получение VPN конфигурации
    3. Двухуровневая аутентификация
    4. ActivationCode функциональность
    """
    
    def setUp(self):
        """
        Настройка тестового окружения.
        
        PURPOSE:
          - Инициализирует API клиент и тестовые данные
          - Создает базовые объекты для тестирования
        """
        self.client = APIClient()
        self.test_user_data = {
            'email': '<EMAIL>',
            'password': 'securepass123',
            'password_confirm': 'securepass123',
            'device_id': 'stage1-test-device',
            'device_name': 'Stage 1 Test Device',
            'device_type': 'test'
        }
        
        # Создаем тестовый план подписки
        self.trial_plan = SubscriptionPlan.objects.create(
            name='Trial',
            description='Test trial plan',
            price=0.00,
            currency='USD',
            duration_days=7,
            traffic_limit_gb=10,
            max_devices=2,
            is_trial=True,
            is_active=True
        )
    
    @patch('vpn.services.HiddifyApiService')  # ПРАВИЛЬНЫЙ путь для VPN services
    @patch('accounts.views.HiddifyApiService')  # Для регистрации
    def test_stage1_complete_flow(self, mock_hiddify_service_accounts, mock_hiddify_service_vpn):
        """
        Тест полного цикла Этапа 1: регистрация → VPN конфигурация.

        Это критический тест для подтверждения готовности Этапа 1.

        PURPOSE:
          - Проверяет весь жизненный цикл пользователя в Stage 1
          - Валидирует двухуровневую аутентификацию
          - Подтверждает получение рабочей VPN конфигурации

        CONTRACT:
          PRECONDITIONS:
            - Hiddify Manager доступен (mock)
            - Trial план существует
          POSTCONDITIONS:
            - Пользователь зарегистрирован
            - VPN конфигурация получена
            - Критерий готовности Stage 1 подтвержден
        """
        # Mock Hiddify service для регистрации (accounts)
        mock_service_instance_accounts = Mock()
        mock_service_instance_accounts.create_hiddify_user.return_value = (
            True, {'uuid': '550e8400-e29b-41d4-a716-************'}
        )
        mock_hiddify_service_accounts.return_value = mock_service_instance_accounts

        # Mock Hiddify service для VPN конфигурации (vpn)
        mock_service_instance_vpn = Mock()
        mock_service_instance_vpn.get_singbox_config_for_user.return_value = (
            True, {
                'config': {
                    'outbounds': [{
                        'type': 'vless',
                        'tag': 'proxy',
                        'server': 'ductuspro.ru',
                        'server_port': 443,
                        'uuid': '550e8400-e29b-41d4-a716-************'
                    }],
                    'route': {'rules': []},
                    'experimental': {},
                    'dns': {'servers': []},
                    'inbounds': [{'type': 'tun', 'tag': 'tun-in'}]
                },
                'traffic_info': {'upload_bytes': 0, 'download_bytes': 0, 'total_bytes': 0, 'expire_timestamp': None}
            }
        )
        mock_hiddify_service_vpn.return_value = mock_service_instance_vpn
        
        # Шаг 1: Регистрация пользователя (Уровень 1 аутентификации)
        register_response = self.client.post(
            reverse('register'),
            data=self.test_user_data,
            format='json'
        )
        
        self.assertEqual(register_response.status_code, status.HTTP_201_CREATED)
        register_data = register_response.json()
        
        # Проверяем структуру ответа
        self.assertIn('success', register_data)
        self.assertIn('user', register_data)
        self.assertIn('subscription', register_data)
        self.assertIn('tokens', register_data)
        
        # Извлекаем токен для дальнейших запросов
        access_token = register_data['tokens']['access']
        
        # Шаг 2: Получение VPN конфигурации (Уровень 2 аутентификации)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
        
        config_response = self.client.get(reverse('vpn_config'))
        
        self.assertEqual(config_response.status_code, status.HTTP_200_OK)
        config_data = config_response.json()
        
        # Проверяем SingBox конфигурацию
        self.assertIn('config', config_data)
        self.assertIn('outbounds', config_data['config'])
        self.assertGreater(len(config_data['config']['outbounds']), 0)
        
        # Проверяем, что это действительно SingBox конфигурация
        outbound = config_data['config']['outbounds'][0]
        self.assertIn('type', outbound)
        self.assertIn(outbound['type'], ['vless', 'vmess', 'trojan'])
        
        # Шаг 3: Проверка критерия готовности Этапа 1
        self.assertTrue(
            self._validate_stage1_readiness_criteria(register_data, config_data),
            "Stage 1 readiness criteria not met"
        )
        
        print("✅ Stage 1 complete flow: PASSED")
    
    def _validate_stage1_readiness_criteria(self, register_data, config_data):
        """
        Проверяет критерий готовности Этапа 1:
        "Тестовый пользователь может получить конфигурацию через API"
        
        PURPOSE:
          - Валидирует все аспекты критерия готовности
          - Логирует результаты проверки
        
        ARGS:
          - register_data (dict): Данные ответа регистрации
          - config_data (dict): Данные VPN конфигурации
        
        RETURNS:
          - bool: True если все критерии выполнены
        """
        criteria_checks = {
            'user_registered': 'user' in register_data and 'id' in register_data['user'],
            'jwt_tokens_issued': 'tokens' in register_data and 'access' in register_data['tokens'],
            'subscription_created': 'subscription' in register_data,
            'vpn_config_received': 'config' in config_data,
            'singbox_format': 'outbounds' in config_data.get('config', {}),
            'valid_outbound': len(config_data.get('config', {}).get('outbounds', [])) > 0,
            'two_level_auth': self._validate_two_level_auth(register_data)
        }
        
        # Логируем результаты проверки
        print("\n=== STAGE 1 READINESS CRITERIA VALIDATION ===")
        for criterion, passed in criteria_checks.items():
            status_icon = "✅" if passed else "❌"
            print(f"{status_icon} {criterion}: {'PASSED' if passed else 'FAILED'}")
        
        all_passed = all(criteria_checks.values())
        print(f"\n🎯 OVERALL STAGE 1 READINESS: {'✅ READY' if all_passed else '❌ NOT READY'}")
        
        return all_passed
    
    def _validate_two_level_auth(self, register_data):
        """
        Проверяет реализацию двухуровневой аутентификации.
        
        PURPOSE:
          - Валидирует наличие Account уровня (JWT токены)
          - Проверяет Device ID binding в токенах
        """
        if 'tokens' not in register_data:
            return False
        
        # Проверяем наличие access токена
        access_token = register_data['tokens'].get('access')
        if not access_token:
            return False
        
        # В реальном тесте можно декодировать JWT и проверить device_id claim
        # Для упрощения проверяем наличие токена
        return True
    
    @patch('accounts.views.HiddifyApiService')
    def test_two_level_authentication_flow(self, mock_hiddify_service):
        """
        Тест двухуровневой аутентификации с ActivationCode.
        
        PURPOSE:
          - Проверяет полный цикл переноса устройства
          - Валидирует ActivationCode функциональность
          - Подтверждает безопасность переноса
        """
        # Настройка mock с реалистичными данными
        mock_service_instance = Mock()
        mock_service_instance.create_hiddify_user.return_value = (
            True, {'uuid': '550e8400-e29b-41d4-a716-************'}
        )
        # Добавляем mock для get_singbox_config_for_user
        mock_service_instance.get_singbox_config_for_user.return_value = (
            True, {
                'config': {
                    'outbounds': [{'type': 'vless', 'tag': 'proxy'}],
                    'route': {'rules': []},
                    'experimental': {},
                    'dns': {'servers': []},
                    'inbounds': [{'type': 'tun', 'tag': 'tun-in'}]
                },
                'traffic_info': {'upload_bytes': 0, 'download_bytes': 0, 'total_bytes': 0, 'expire_timestamp': None}
            }
        )
        mock_hiddify_service.return_value = mock_service_instance
        
        # Шаг 1: Регистрация первого пользователя
        register_response = self.client.post(
            reverse('register'),
            data=self.test_user_data,
            format='json'
        )
        
        access_token = register_response.json()['tokens']['access']
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
        
        # Шаг 2: Генерация кода активации
        activation_code_response = self.client.get(
            reverse('generate_activation_code')
        )
        
        self.assertEqual(activation_code_response.status_code, status.HTTP_200_OK)
        activation_data = activation_code_response.json()
        activation_code = activation_data['activation_code']
        
        # Проверяем формат кода активации
        self.assertEqual(len(activation_code), 8)
        self.assertTrue(activation_code.isalnum())
        
        # Шаг 3: Активация нового устройства
        new_device_data = {
            'activation_code': activation_code,
            'device_id': 'new-test-device-002',
            'device_name': 'New Test Device',
            'device_type': 'android'
        }
        
        # Убираем авторизацию для имитации нового устройства
        self.client.credentials()
        
        activate_response = self.client.post(
            reverse('activate_device'),
            data=new_device_data,
            format='json'
        )
        
        self.assertEqual(activate_response.status_code, status.HTTP_200_OK)
        activate_data = activate_response.json()
        
        # Проверяем, что новое устройство получило токены
        self.assertIn('tokens', activate_data)
        self.assertIn('access', activate_data['tokens'])
        
        print("✅ Two-level authentication flow: PASSED")
    
    def test_singbox_config_validation(self):
        """
        Тест валидации SingBox конфигурации.
        
        PURPOSE:
          - Проверяет корректность формата SingBox конфигурации
          - Валидирует обязательные поля
          - Подтверждает совместимость с SingBox клиентами
        """
        # Пример валидной SingBox конфигурации
        valid_config = {
            'outbounds': [{
                'type': 'vless',
                'tag': 'proxy',
                'server': 'ductuspro.ru',
                'server_port': 443,
                'uuid': 'test-uuid'
            }],
            'inbounds': [{
                'type': 'tun',
                'tag': 'tun-in'
            }]
        }
        
        # Проверяем валидацию
        self.assertTrue(self._validate_singbox_config(valid_config))
        
        # Проверяем невалидную конфигурацию
        invalid_config = {'invalid': 'config'}
        self.assertFalse(self._validate_singbox_config(invalid_config))
        
        print("✅ SingBox config validation: PASSED")
    
    def _validate_singbox_config(self, config):
        """
        Валидирует SingBox конфигурацию.
        
        PURPOSE:
          - Проверяет наличие обязательных полей
          - Валидирует структуру outbounds
        
        ARGS:
          - config (dict): SingBox конфигурация
        
        RETURNS:
          - bool: True если конфигурация валидна
        """
        required_fields = ['outbounds']
        
        for field in required_fields:
            if field not in config:
                return False
        
        if not isinstance(config['outbounds'], list) or len(config['outbounds']) == 0:
            return False
        
        outbound = config['outbounds'][0]
        if 'type' not in outbound or outbound['type'] not in ['vless', 'vmess', 'trojan']:
            return False
        
        return True
    
    def test_database_consistency(self):
        """
        Тест консистентности базы данных после операций.
        
        PURPOSE:
          - Проверяет корректность создания связанных объектов
          - Валидирует целостность данных
        """
        # Создаем тестового пользователя
        user = UserAccount.objects.create_user(
            username='<EMAIL>',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Создаем устройство
        device = UserDevice.objects.create(
            user=user,
            device_id='test-device',
            device_name='Test Device',
            device_type='test'
        )
        
        # Создаем HiddifyLink
        hiddify_link = HiddifyLink.objects.create(
            user=user,
            device=device,
            hiddify_user_uuid='test-uuid'
        )
        
        # Проверяем связи
        self.assertEqual(device.user, user)
        self.assertEqual(hiddify_link.user, user)
        self.assertEqual(hiddify_link.device, device)
        
        print("✅ Database consistency: PASSED")


class Stage1PerformanceTest(TestCase):
    """
    Тесты производительности для Stage 1.
    """
    
    def setUp(self):
        self.client = APIClient()
    
    @patch('vpn.services.HiddifyApiService')  # Для VPN конфигурации
    @patch('accounts.views.HiddifyApiService')  # Для регистрации
    def test_api_response_times(self, mock_hiddify_service_accounts, mock_hiddify_service_vpn):
        """
        Тест времени отклика API endpoints.
        
        PURPOSE:
          - Проверяет производительность критических endpoints
          - Валидирует соответствие SLA
        """
        import time
        
        # Mock Hiddify service для регистрации (accounts)
        mock_service_instance_accounts = Mock()
        mock_service_instance_accounts.create_hiddify_user.return_value = (
            True, {'uuid': '550e8400-e29b-41d4-a716-************'}
        )
        mock_hiddify_service_accounts.return_value = mock_service_instance_accounts

        # Mock Hiddify service для VPN конфигурации (vpn)
        mock_service_instance_vpn = Mock()
        mock_service_instance_vpn.get_singbox_config_for_user.return_value = (
            True, {
                'config': {
                    'outbounds': [{'type': 'vless', 'tag': 'proxy'}],
                    'route': {'rules': []},
                    'experimental': {},
                    'dns': {'servers': []},
                    'inbounds': [{'type': 'tun', 'tag': 'tun-in'}]
                },
                'traffic_info': {'upload_bytes': 0, 'download_bytes': 0, 'total_bytes': 0, 'expire_timestamp': None}
            }
        )
        mock_hiddify_service_vpn.return_value = mock_service_instance_vpn
        
        # Создаем тестовый план
        SubscriptionPlan.objects.create(
            name='Trial',
            price=0.00,
            duration_days=7,
            traffic_limit_gb=10,
            is_trial=True,
            is_active=True
        )
        
        # Тест регистрации
        start_time = time.time()
        response = self.client.post('/api/auth/register/', {
            'email': '<EMAIL>',
            'password': 'testpass123',
            'password_confirm': 'testpass123',
            'device_id': 'perf-device',
            'device_name': 'Performance Test',
            'device_type': 'test'
        }, format='json')
        registration_time = time.time() - start_time
        
        self.assertEqual(response.status_code, 201)
        self.assertLess(registration_time, 5.0, "Registration took too long")
        
        # Тест получения конфигурации
        access_token = response.json()['tokens']['access']
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
        
        start_time = time.time()
        config_response = self.client.get('/api/vpn/config/')
        config_time = time.time() - start_time
        
        self.assertEqual(config_response.status_code, 200)
        self.assertLess(config_time, 3.0, "Config retrieval took too long")
        
        print(f"✅ Performance test: Registration {registration_time:.2f}s, Config {config_time:.2f}s")
