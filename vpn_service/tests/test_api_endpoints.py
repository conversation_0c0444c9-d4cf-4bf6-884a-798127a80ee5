"""
Tests for API endpoints.
"""
import pytest
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from unittest.mock import patch, Mock
from accounts.models import UserAccount, UserDevice, HiddifyLink
from subscriptions.models import SubscriptionPlan, ActiveSubscription
from django.utils import timezone
from datetime import timedelta
import uuid


class TestAuthenticationEndpoints(TestCase):
    """Test authentication endpoints."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.client = APIClient()
        self.register_url = reverse('register')
        self.login_url = reverse('login')
        
        # Create a test subscription plan
        self.trial_plan = SubscriptionPlan.objects.create(
            name="Trial",
            price=0.00,
            duration_days=7,
            traffic_limit_gb=10,
            is_trial=True,
            is_active=True
        )
    
    @patch('accounts.views.HiddifyApiService')
    def test_user_registration_success(self, mock_hiddify_service):
        """Test successful user registration."""
        # Mock Hiddify service response
        mock_service_instance = Mock()
        mock_service_instance.create_hiddify_user.return_value = (True, {'uuid': str(uuid.uuid4())})
        mock_hiddify_service.return_value = mock_service_instance
        
        data = {
            'email': '<EMAIL>',
            'password': 'testpassword123',
            'password_confirm': 'testpassword123',
            'device_id': 'test-device-123',
            'device_name': 'Test Device',
            'device_type': 'ios'
        }
        
        response = self.client.post(self.register_url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(response.data['success'])
        self.assertIn('tokens', response.data)
        self.assertIn('user', response.data)
        
        # Verify user was created
        user = UserAccount.objects.get(email='<EMAIL>')
        self.assertTrue(user.devices.filter(device_id='test-device-123').exists())
    
    def test_user_registration_invalid_data(self):
        """Test user registration with invalid data."""
        data = {
            'email': 'invalid-email',
            'password': 'short',
            'password_confirm': 'different',
            'device_id': ''
        }
        
        response = self.client.post(self.register_url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    
    def test_user_login_success(self):
        """Test successful user login."""
        # Create a test user
        user = UserAccount.objects.create_user(
            email='<EMAIL>',
            password='testpassword123',
            username='<EMAIL>'
        )
        
        data = {
            'email': '<EMAIL>',
            'password': 'testpassword123',
            'device_id': 'test-device-123'
        }
        
        response = self.client.post(self.login_url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertIn('tokens', response.data)
    
    def test_user_login_invalid_credentials(self):
        """Test user login with invalid credentials."""
        data = {
            'email': '<EMAIL>',
            'password': 'wrongpassword'
        }
        
        response = self.client.post(self.login_url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)


class TestVPNEndpoints(TestCase):
    """Test VPN configuration endpoints."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.client = APIClient()
        self.config_url = reverse('vpn_config')
        self.stats_url = reverse('traffic_stats')
        
        # Create test user and related objects
        self.user = UserAccount.objects.create_user(
            email='<EMAIL>',
            password='testpassword123',
            username='<EMAIL>'
        )
        
        self.device = UserDevice.objects.create(
            user=self.user,
            device_id='test-device-123',
            device_name='Test Device'
        )
        
        self.plan = SubscriptionPlan.objects.create(
            name="Basic",
            price=9.99,
            duration_days=30,
            traffic_limit_gb=100,
            is_active=True
        )
        
        self.subscription = ActiveSubscription.objects.create(
            user=self.user,
            plan=self.plan,
            start_date=timezone.now(),
            end_date=timezone.now() + timedelta(days=30),
            is_active=True
        )
        
        self.hiddify_link = HiddifyLink.objects.create(
            user=self.user,
            device=self.device,
            hiddify_user_uuid=uuid.uuid4(),
            hiddify_comment={},
            traffic_limit_bytes=self.plan.traffic_limit_gb * 1024 * 1024 * 1024,
            is_active_in_hiddify=True,
            hiddify_created_at=timezone.now()
        )
        
        # Authenticate user
        self.client.force_authenticate(user=self.user)
    
    @patch('vpn.views.HiddifyApiService')
    def test_get_vpn_config_success(self, mock_hiddify_service):
        """Test successful VPN config retrieval (Stage 1: SingBox only)."""
        # Mock Hiddify service response
        mock_service_instance = Mock()
        mock_service_instance.get_singbox_config_for_user.return_value = (
            True,
            {
                'config': {'outbounds': []},
                'traffic_info': {'upload_bytes': 0, 'download_bytes': 1000}
            }
        )
        mock_hiddify_service.return_value = mock_service_instance

        # Stage 1: No type parameter needed (fixed to SingBox)
        response = self.client.get(self.config_url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['config_type'], 'singbox')
        self.assertIn('config', response.data)
        self.assertIn('subscription_info', response.data)
    
    def test_get_vpn_config_no_subscription(self):
        """Test VPN config request without active subscription."""
        # Deactivate subscription
        self.subscription.is_active = False
        self.subscription.save()
        
        response = self.client.get(self.config_url)
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertIn('No active subscription', response.data['error'])
    
    def test_get_vpn_config_no_hiddify_link(self):
        """Test VPN config request without Hiddify link."""
        # Delete Hiddify link
        self.hiddify_link.delete()
        
        response = self.client.get(self.config_url)
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertIn('VPN access not configured', response.data['error'])
    
    # TODO (Этап 2): Восстановить тест для проверки неправильного типа конфигурации
    # def test_get_vpn_config_invalid_type(self):
    #     """Test VPN config request with invalid type."""
    #     response = self.client.get(self.config_url + '?type=invalid')
    #
    #     self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    #     self.assertIn('Invalid config type', response.data['error'])
    
    @patch('vpn.views.HiddifyApiService')
    def test_get_traffic_stats_success(self, mock_hiddify_service):
        """Test successful traffic stats retrieval (Stage 1: using SingBox endpoint)."""
        # Mock Hiddify service response
        mock_service_instance = Mock()
        # Stage 1: Используем get_singbox_config_for_user вместо get_subscription_link_for_user
        mock_service_instance.get_singbox_config_for_user.return_value = (
            True,
            {
                'traffic_info': {
                    'upload_bytes': 500,
                    'download_bytes': 1500,
                    'total_bytes': 10000000
                }
            }
        )
        mock_hiddify_service.return_value = mock_service_instance
        
        response = self.client.get(self.stats_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertIn('traffic_stats', response.data)
        self.assertEqual(response.data['traffic_stats']['upload_bytes'], 500)
        self.assertEqual(response.data['traffic_stats']['download_bytes'], 1500)
    
    def test_get_traffic_stats_unauthenticated(self):
        """Test traffic stats request without authentication."""
        self.client.force_authenticate(user=None)
        
        response = self.client.get(self.stats_url)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
