"""
MVP API views for mobile device registration and VPN configuration.

⚠️ DEPRECATED: This API is deprecated and will be removed in Stage 2.
Use Legacy API (POST /api/auth/register/ + GET /api/vpn/config/) instead.

Migration guide: See STAGE1_FRONTEND_INTEGRATION_GUIDE.md
"""
import uuid
import json
import logging
import warnings
from datetime import datetime, timedelta
from django.utils import timezone
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from drf_spectacular.utils import extend_schema, OpenApiParameter, OpenApiExample
from drf_spectacular.openapi import OpenApiTypes

from .models import DeviceRegistration
from .serializers import (
    DeviceRegistrationSerializer, 
    DeviceRegistrationResponseSerializer,
    VPNConfigResponseSerializer,
    ErrorResponseSerializer
)
from vpn.services import HiddifyApiService

logger = logging.getLogger(__name__)


@extend_schema(
    tags=['MVP Device API - DEPRECATED'],
    summary='[DEPRECATED] Register device for VPN access',
    description='''
    **⚠️ DEPRECATED ENDPOINT**

    This endpoint is deprecated and will be removed in Stage 2.

    **Use instead:** POST /api/auth/register/

    **Migration guide:** See STAGE1_FRONTEND_INTEGRATION_GUIDE.md

    ---

    **🎯 ORIGINAL STAGE 1 CORE ENDPOINT**

    Регистрирует устройство для получения VPN доступа без создания полноценного пользователя.

    **Двухуровневая аутентификация Этапа 1:**
    - Уровень 1: Регистрация устройства (этот endpoint)
    - Уровень 2: Получение конфигурации по device_id

    **Этот endpoint создает:**
    1. Уникальный internal_device_id (ваш ключ доступа)
    2. Пользователя в Hiddify Manager с триальными лимитами
    3. Связь между device_id и VPN-конфигурацией

    **Триальные лимиты для Этапа 1:**
    - Трафик: 1 GB
    - Срок действия: 7 дней

    **Следующий шаг:** Используйте полученный internal_device_id для получения SingBox конфигурации через GET /api/v1/device/config/
    ''',
    deprecated=True,
    request=DeviceRegistrationSerializer,
    responses={
        201: DeviceRegistrationResponseSerializer,
        400: ErrorResponseSerializer,
        500: ErrorResponseSerializer
    },
    examples=[
        OpenApiExample(
            'Empty Request',
            description='Регистрация без дополнительных данных',
            value={}
        ),
        OpenApiExample(
            'With Device Info',
            description='Регистрация с информацией об устройстве',
            value={
                'client_generated_device_id': 'my-device-12345',
                'device_name': 'iPhone 15 Pro',
                'device_type': 'ios'
            }
        )
    ]
)
@api_view(['POST'])
@permission_classes([AllowAny])
def register_device(request):
    """
    [DEPRECATED] Регистрирует устройство для VPN доступа.

    ⚠️ DEPRECATED: Use POST /api/auth/register/ instead.

    PURPOSE:
      - Обеспечивает быструю регистрацию устройств для мобильных приложений
      - Создает пользователя в Hiddify Manager с триальными лимитами
      - Возвращает internal_device_id для дальнейшего получения конфигураций

    AAG (Actor -> Action -> Goal):
      - Мобильное приложение -> Отправляет POST запрос -> Получает device_id для VPN
      - Система -> Создает Hiddify пользователя -> Обеспечивает VPN доступ

    CONTRACT:
      PRECONDITIONS:
        - Запрос может быть пустым или содержать опциональные поля
        - Hiddify Manager доступен
      POSTCONDITIONS:
        - Создается запись DeviceRegistration
        - Создается пользователь в Hiddify Manager
        - Возвращается internal_device_id
    """
    # Deprecation warning
    warnings.warn(
        "MVP Device API is deprecated. Use Legacy API (POST /api/auth/register/) instead.",
        DeprecationWarning,
        stacklevel=2
    )

    serializer = DeviceRegistrationSerializer(data=request.data)
    
    if not serializer.is_valid():
        return Response({
            'error': 'Validation failed',
            'details': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)
    
    try:
        # Шаг 1: Генерируем уникальный internal_device_id
        internal_device_id = uuid.uuid4().hex
        
        # Шаг 2: Подготавливаем данные для Hiddify
        hiddify_name = f"device_{internal_device_id}"
        usage_limit_gb = 1  # Триальный лимит 1GB
        package_days = 7    # Триальный срок 7 дней
        
        # Метаданные для комментария в Hiddify
        comment_data = {
            'internal_device_id': internal_device_id,
            'client_generated_device_id': serializer.validated_data.get('client_generated_device_id', ''),
            'device_name': serializer.validated_data.get('device_name', ''),
            'device_type': serializer.validated_data.get('device_type', ''),
            'created_at': timezone.now().isoformat(),
            'source': 'mvp_api'
        }
        comment_json_string = json.dumps(comment_data)
        
        # Шаг 3: Создаем пользователя в Hiddify Manager
        hiddify_service = HiddifyApiService()
        success, hiddify_response = hiddify_service.create_hiddify_user(
            name=hiddify_name,
            usage_limit_gb=usage_limit_gb,
            package_days=package_days,
            comment_json_string=comment_json_string
        )
        
        if not success:
            logger.error(f"Failed to create Hiddify user for device {internal_device_id}: {hiddify_response}")
            return Response({
                'error': 'Failed to create VPN access. Please try again.'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        hiddify_user_uuid = hiddify_response['uuid']
        logger.info(f"Created Hiddify user {hiddify_user_uuid} for device {internal_device_id}")
        
        # Шаг 4: Сохраняем в нашей БД
        device_registration = DeviceRegistration.objects.create(
            internal_device_id=internal_device_id,
            hiddify_user_uuid=hiddify_user_uuid,
            client_generated_device_id=serializer.validated_data.get('client_generated_device_id', ''),
            device_name=serializer.validated_data.get('device_name', ''),
            device_type=serializer.validated_data.get('device_type', ''),
            traffic_limit_bytes=usage_limit_gb * 1024 * 1024 * 1024,  # GB to bytes
            hiddify_created_at=timezone.now(),
            is_active=True
        )
        
        logger.info(f"Device registered successfully: {internal_device_id} -> {hiddify_user_uuid}")
        
        # Шаг 5: Возвращаем ответ с deprecation headers
        response = Response({
            'internal_device_id': internal_device_id,
            'message': 'Device registered successfully',
            'deprecation_notice': 'This API is deprecated. Use POST /api/auth/register/ instead.'
        }, status=status.HTTP_201_CREATED)

        # Добавляем deprecation headers
        response['Deprecation'] = 'true'
        response['Sunset'] = '2025-07-01'
        response['Link'] = '</api/auth/register/>; rel="successor-version"'

        return response
        
    except Exception as e:
        logger.error(f"Error registering device: {str(e)}")
        return Response({
            'error': 'Internal server error. Please try again later.'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@extend_schema(
    tags=['MVP Device API'],
    summary='Get VPN configuration',
    description='Get VPN configuration for device (deprecated)',
    deprecated=True,
    parameters=[
        OpenApiParameter(
            name='X-Device-ID',
            type=OpenApiTypes.STR,
            location=OpenApiParameter.HEADER,
            required=True,
            description='Internal device ID, полученный при регистрации устройства'
        )
    ],
    responses={
        200: VPNConfigResponseSerializer,
        401: ErrorResponseSerializer,
        403: ErrorResponseSerializer,
        404: ErrorResponseSerializer,
        500: ErrorResponseSerializer
    },
    examples=[
        OpenApiExample(
            'Success Response',
            description='Успешный ответ с SingBox конфигурацией',
            value={
                "outbounds": [
                    {
                        "type": "vless",
                        "tag": "proxy",
                        "server": "ductuspro.ru",
                        "server_port": 443,
                        "uuid": "12465e4c-2ad3-4761-8e7e-94efecc05160"
                    }
                ],
                "inbounds": [
                    {
                        "type": "tun",
                        "tag": "tun-in",
                        "interface_name": "tun0"
                    }
                ]
            }
        )
    ]
)
@api_view(['GET'])
@permission_classes([AllowAny])
def get_device_config(request):
    """
    [DEPRECATED] Получает VPN конфигурацию для устройства.

    ⚠️ DEPRECATED: Use GET /api/vpn/config/ instead.

    PURPOSE:
      - Предоставляет SingBox конфигурацию для зарегистрированного устройства
      - Проверяет валидность device_id
      - Обновляет статистику последнего запроса конфигурации

    AAG (Actor -> Action -> Goal):
      - Мобильное приложение -> Запрашивает конфигурацию -> Получает VPN настройки
      - Система -> Проверяет device_id -> Возвращает актуальную конфигурацию

    CONTRACT:
      PRECONDITIONS:
        - Заголовок X-Device-ID содержит валидный internal_device_id
        - Устройство зарегистрировано и активно
        - Hiddify Manager доступен
      POSTCONDITIONS:
        - Возвращается SingBox JSON конфигурация
        - Обновляется last_config_request
    """
    # Deprecation warning
    warnings.warn(
        "MVP Device API is deprecated. Use Legacy API (GET /api/vpn/config/) instead.",
        DeprecationWarning,
        stacklevel=2
    )

    # Шаг 1: Извлекаем device_id из заголовка
    device_id = request.headers.get('X-Device-ID')
    
    if not device_id:
        return Response({
            'error': 'X-Device-ID header is required'
        }, status=status.HTTP_401_UNAUTHORIZED)
    
    try:
        # Шаг 2: Проверяем существование устройства
        try:
            device_registration = DeviceRegistration.objects.get(
                internal_device_id=device_id,
                is_active=True
            )
        except DeviceRegistration.DoesNotExist:
            logger.warning(f"Device not found or inactive: {device_id}")
            return Response({
                'error': 'Device not found or inactive'
            }, status=status.HTTP_403_FORBIDDEN)
        
        # Шаг 3: Получаем конфигурацию от Hiddify
        hiddify_service = HiddifyApiService()
        success, config_response = hiddify_service.get_singbox_config_for_user(
            str(device_registration.hiddify_user_uuid)
        )
        
        if not success:
            logger.error(f"Failed to get SingBox config for device {device_id}: {config_response}")
            return Response({
                'error': 'Failed to retrieve VPN configuration. Please try again.'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        # Шаг 4: Обновляем статистику
        device_registration.last_config_request = timezone.now()
        
        # Извлекаем информацию о трафике из ответа Hiddify (если есть)
        if 'traffic_info' in config_response:
            traffic_info = config_response['traffic_info']
            device_registration.traffic_used_bytes = traffic_info.get('used_bytes', 0)
            device_registration.last_traffic_sync = timezone.now()
        
        device_registration.save()
        
        logger.info(f"Returned SingBox config for device {device_id}")
        
        # Шаг 5: Возвращаем конфигурацию с deprecation headers
        response_data = config_response.get('config', config_response)

        # Добавляем deprecation notice в данные
        if isinstance(response_data, dict):
            response_data['deprecation_notice'] = 'This API is deprecated. Use GET /api/vpn/config/ instead.'

        response = Response(response_data, status=status.HTTP_200_OK)

        # Добавляем deprecation headers
        response['Deprecation'] = 'true'
        response['Sunset'] = '2025-07-01'
        response['Link'] = '</api/vpn/config/>; rel="successor-version"'

        return response
        
    except Exception as e:
        logger.error(f"Error getting config for device {device_id}: {str(e)}")
        return Response({
            'error': 'Internal server error. Please try again later.'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
