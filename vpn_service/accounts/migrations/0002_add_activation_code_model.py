# Generated manually for ActivationCode model

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ActivationCode',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(db_index=True, help_text='Уникальный 8-символьный код активации', max_length=8, unique=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Время создания кода активации')),
                ('expires_at', models.DateTimeField(help_text='Время истечения срока действия кода (15 минут от создания)')),
                ('is_active', models.BooleanField(default=True, help_text='Активен ли код (не использован и не истек)')),
                ('used_at', models.DateTimeField(blank=True, help_text='Время использования кода (если был использован)', null=True)),
                ('used_by_device_id', models.CharField(blank=True, help_text='ID устройства, которое использовало этот код', max_length=255, null=True)),
                ('user', models.ForeignKey(help_text='Пользователь, для которого сгенерирован код', on_delete=django.db.models.deletion.CASCADE, related_name='activation_codes', to='accounts.useraccount')),
            ],
            options={
                'db_table': 'activation_codes',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddIndex(
            model_name='activationcode',
            index=models.Index(fields=['code'], name='activation_codes_code_idx'),
        ),
        migrations.AddIndex(
            model_name='activationcode',
            index=models.Index(fields=['user', 'is_active'], name='activation_codes_user_active_idx'),
        ),
        migrations.AddIndex(
            model_name='activationcode',
            index=models.Index(fields=['expires_at'], name='activation_codes_expires_idx'),
        ),
    ]
