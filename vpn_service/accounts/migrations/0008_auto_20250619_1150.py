# Generated by Django 4.2.7 on 2025-06-19 11:50

from django.db import migrations
import uuid


def fix_duplicate_device_secrets(apps, schema_editor):
    """Исправляем дублирующиеся device_secret значения."""
    UserDevice = apps.get_model('accounts', 'UserDevice')

    # Обновляем все device_secret на уникальные значения
    for device in UserDevice.objects.all():
        device.device_secret = uuid.uuid4()
        device.save()


def reverse_fix_duplicate_device_secrets(apps, schema_editor):
    """Обратная операция - ничего не делаем."""
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0007_alter_userdevice_device_secret'),
    ]

    operations = [
        migrations.RunPython(
            fix_duplicate_device_secrets,
            reverse_fix_duplicate_device_secrets
        ),
    ]
