# Generated by Django 4.2.7 on 2025-06-19 11:15

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0005_add_anonymous_user_support'),
    ]

    operations = [
        migrations.AddField(
            model_name='userdevice',
            name='device_secret',
            field=models.UUIDField(default=uuid.uuid4, editable=False),
        ),
        migrations.CreateModel(
            name='UserSession',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('refresh_token', models.TextField(unique=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('expires_at', models.DateTimeField()),
                ('is_active', models.BooleanField(default=True)),
                ('device', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='sessions', to='accounts.userdevice')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sessions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'user_sessions',
                'indexes': [models.Index(fields=['user', 'is_active'], name='user_sessio_user_id_bb1b83_idx'), models.Index(fields=['refresh_token'], name='user_sessio_refresh_006d95_idx'), models.Index(fields=['expires_at'], name='user_sessio_expires_66ae96_idx')],
            },
        ),
    ]
