# Generated by Django 4.2.7 on 2025-06-01 12:54

from django.db import migrations, models
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='DeviceRegistration',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('internal_device_id', models.CharField(db_index=True, max_length=255, unique=True)),
                ('hiddify_user_uuid', models.UUIDField(db_index=True, unique=True)),
                ('client_generated_device_id', models.CharField(blank=True, max_length=255, null=True)),
                ('device_name', models.CharField(blank=True, max_length=100, null=True)),
                ('device_type', models.Char<PERSON>ield(blank=True, max_length=50, null=True)),
                ('traffic_used_bytes', models.BigIntegerField(default=0)),
                ('traffic_limit_bytes', models.BigIntegerField(default=0)),
                ('last_traffic_sync', models.DateTimeField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('hiddify_created_at', models.DateTimeField()),
                ('last_config_request', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'device_registrations',
                'indexes': [models.Index(fields=['internal_device_id'], name='device_regi_interna_6ce63a_idx'), models.Index(fields=['hiddify_user_uuid'], name='device_regi_hiddify_7f1465_idx'), models.Index(fields=['is_active'], name='device_regi_is_acti_09b9bb_idx'), models.Index(fields=['created_at'], name='device_regi_created_fafa9a_idx')],
            },
        ),
    ]
