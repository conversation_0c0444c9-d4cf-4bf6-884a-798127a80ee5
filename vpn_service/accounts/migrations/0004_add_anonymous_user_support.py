# Generated by Django 4.2.7 on 2025-06-07 10:07

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0003_merge_20250602_0550'),
    ]

    operations = [
        migrations.RenameIndex(
            model_name='activationcode',
            new_name='activation__code_16b4df_idx',
            old_name='activation_codes_code_idx',
        ),
        migrations.RenameIndex(
            model_name='activationcode',
            new_name='activation__user_id_793949_idx',
            old_name='activation_codes_user_active_idx',
        ),
        migrations.RenameIndex(
            model_name='activationcode',
            new_name='activation__expires_597c67_idx',
            old_name='activation_codes_expires_idx',
        ),
        migrations.AddField(
            model_name='useraccount',
            name='is_anonymous',
            field=models.BooleanField(default=False),
        ),
        migrations.AlterField(
            model_name='useraccount',
            name='email',
            field=models.EmailField(blank=True, max_length=254, null=True, unique=True),
        ),
        migrations.AddConstraint(
            model_name='useraccount',
            constraint=models.UniqueConstraint(condition=models.Q(('email__isnull', False)), fields=('email',), name='unique_email_when_not_null'),
        ),
    ]
