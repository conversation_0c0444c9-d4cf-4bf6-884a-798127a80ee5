# Generated by Django 4.2.7 on 2025-06-01 11:01

from django.conf import settings
import django.contrib.auth.models
import django.contrib.auth.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserAccount',
            fields=[
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.CharField(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('email', models.EmailField(max_length=254, unique=True)),
                ('phone', models.CharField(blank=True, max_length=20, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_email_verified', models.BooleanField(default=False)),
                ('last_login_ip', models.GenericIPAddressField(blank=True, null=True)),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'verbose_name': 'User Account',
                'verbose_name_plural': 'User Accounts',
                'db_table': 'user_accounts',
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='UserDevice',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('device_id', models.CharField(max_length=255)),
                ('device_name', models.CharField(blank=True, max_length=100)),
                ('device_type', models.CharField(blank=True, max_length=50)),
                ('fcm_token', models.TextField(blank=True, null=True)),
                ('last_seen', models.DateTimeField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='devices', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'user_devices',
            },
        ),
        migrations.CreateModel(
            name='HiddifyLink',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('hiddify_user_uuid', models.UUIDField(unique=True)),
                ('hiddify_comment', models.JSONField(default=dict)),
                ('traffic_used_bytes', models.BigIntegerField(default=0)),
                ('traffic_limit_bytes', models.BigIntegerField(default=0)),
                ('last_traffic_sync', models.DateTimeField(blank=True, null=True)),
                ('is_active_in_hiddify', models.BooleanField(default=True)),
                ('hiddify_created_at', models.DateTimeField()),
                ('last_config_request', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('device', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='hiddify_links', to='accounts.userdevice')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='hiddify_link', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'hiddify_links',
            },
        ),
        migrations.AddIndex(
            model_name='userdevice',
            index=models.Index(fields=['user', 'is_active'], name='user_device_user_id_14e9e9_idx'),
        ),
        migrations.AddIndex(
            model_name='userdevice',
            index=models.Index(fields=['device_id'], name='user_device_device__945e98_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='userdevice',
            unique_together={('user', 'device_id')},
        ),
        migrations.AddIndex(
            model_name='hiddifylink',
            index=models.Index(fields=['hiddify_user_uuid'], name='hiddify_lin_hiddify_0734d2_idx'),
        ),
        migrations.AddIndex(
            model_name='hiddifylink',
            index=models.Index(fields=['user'], name='hiddify_lin_user_id_745c2f_idx'),
        ),
        migrations.AddIndex(
            model_name='hiddifylink',
            index=models.Index(fields=['is_active_in_hiddify'], name='hiddify_lin_is_acti_9c079f_idx'),
        ),
    ]
