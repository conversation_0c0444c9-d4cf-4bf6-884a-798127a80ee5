"""
Celery tasks for accounts app.
"""
from celery import shared_task
from django.utils import timezone
from datetime import timedelta
import logging

logger = logging.getLogger(__name__)


@shared_task
def cleanup_inactive_devices():
    """
    Деактивирует устройства, которые не использовались более 30 дней.
    
    PURPOSE:
      - Очищает неактивные устройства для освобождения лимитов
      - Поддерживает актуальность списка устройств
      - Улучшает безопасность аккаунтов
    """
    from .models import UserDevice
    
    cutoff_date = timezone.now() - timedelta(days=30)
    
    inactive_devices = UserDevice.objects.filter(
        is_active=True,
        last_seen__lt=cutoff_date
    )
    
    count = inactive_devices.count()
    inactive_devices.update(is_active=False)
    
    logger.info(f"Deactivated {count} inactive devices")
    return f"Deactivated {count} inactive devices"


@shared_task
def send_verification_email(user_id):
    """
    Отправляет email для верификации адреса электронной почты.
    
    PURPOSE:
      - Подтверждает владение email адресом
      - Повышает безопасность аккаунтов
      - Обеспечивает возможность восстановления доступа
    """
    from .models import UserAccount
    from django.core.mail import send_mail
    from django.conf import settings
    
    try:
        user = UserAccount.objects.get(id=user_id)
        
        # Здесь должна быть логика генерации токена верификации
        # и отправки email с ссылкой для подтверждения
        
        subject = "Verify your email address"
        message = f"Please verify your email address by clicking the link below."
        
        send_mail(
            subject,
            message,
            settings.DEFAULT_FROM_EMAIL,
            [user.email],
            fail_silently=False,
        )
        
        logger.info(f"Verification email sent to {user.email}")
        return f"Verification email sent to {user.email}"
        
    except UserAccount.DoesNotExist:
        logger.error(f"User with id {user_id} not found")
        return f"User with id {user_id} not found"
    except Exception as e:
        logger.error(f"Failed to send verification email to user {user_id}: {e}")
        return f"Failed to send verification email: {e}"
