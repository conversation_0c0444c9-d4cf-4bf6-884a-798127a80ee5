"""
Tests for activation code functionality.
"""
from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from datetime import timedelta
from rest_framework.test import APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken

from accounts.models import UserAccount, ActivationCode, UserDevice
from subscriptions.models import SubscriptionPlan, ActiveSubscription


class ActivationCodeTestCase(TestCase):
    """Test cases for activation code functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.client = APIClient()
        
        # Create test user
        self.user = UserAccount.objects.create_user(
            email='<EMAIL>',
            password='testpassword123',
            username='<EMAIL>'
        )
        
        # Create test subscription plan
        self.plan = SubscriptionPlan.objects.create(
            name="Test Plan",
            price=9.99,
            duration_days=30,
            traffic_limit_gb=100,
            is_active=True
        )
        
        # Create active subscription
        self.subscription = ActiveSubscription.objects.create(
            user=self.user,
            plan=self.plan,
            start_date=timezone.now(),
            end_date=timezone.now() + timedelta(days=30),
            is_active=True
        )
        
        # URLs
        self.generate_code_url = reverse('accounts:generate_activation_code')
        self.activate_device_url = reverse('accounts:activate_device')
    
    def test_generate_activation_code_success(self):
        """Test successful activation code generation."""
        # Authenticate user
        self.client.force_authenticate(user=self.user)
        
        # Generate activation code
        response = self.client.get(self.generate_code_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('activation_code', response.data)
        self.assertIn('expires_at', response.data)
        self.assertIn('expires_in_minutes', response.data)
        
        # Check code in database
        code = response.data['activation_code']
        activation_code = ActivationCode.objects.get(code=code)
        self.assertEqual(activation_code.user, self.user)
        self.assertTrue(activation_code.is_active)
        self.assertTrue(activation_code.is_valid())
    
    def test_generate_activation_code_unauthenticated(self):
        """Test activation code generation without authentication."""
        response = self.client.get(self.generate_code_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_generate_activation_code_deactivates_previous(self):
        """Test that generating new code deactivates previous codes."""
        self.client.force_authenticate(user=self.user)
        
        # Generate first code
        response1 = self.client.get(self.generate_code_url)
        code1 = response1.data['activation_code']
        
        # Generate second code
        response2 = self.client.get(self.generate_code_url)
        code2 = response2.data['activation_code']
        
        # Check that first code is deactivated
        activation_code1 = ActivationCode.objects.get(code=code1)
        activation_code2 = ActivationCode.objects.get(code=code2)
        
        self.assertFalse(activation_code1.is_active)
        self.assertTrue(activation_code2.is_active)
        self.assertNotEqual(code1, code2)
    
    def test_activate_device_success(self):
        """Test successful device activation."""
        # Generate activation code
        self.client.force_authenticate(user=self.user)
        response = self.client.get(self.generate_code_url)
        activation_code = response.data['activation_code']
        
        # Activate device (without authentication)
        self.client.force_authenticate(user=None)
        activation_data = {
            'activation_code': activation_code,
            'device_id': 'test-device-123',
            'device_name': 'Test Device',
            'device_type': 'test'
        }
        
        response = self.client.post(self.activate_device_url, activation_data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertIn('tokens', response.data)
        self.assertIn('user', response.data)
        self.assertIn('device_info', response.data)
        
        # Check device was created
        device = UserDevice.objects.get(device_id='test-device-123')
        self.assertEqual(device.user, self.user)
        self.assertEqual(device.device_name, 'Test Device')
        self.assertTrue(device.is_active)
        
        # Check code was deactivated
        code_obj = ActivationCode.objects.get(code=activation_code)
        self.assertFalse(code_obj.is_active)
        self.assertIsNotNone(code_obj.used_at)
        self.assertEqual(code_obj.used_by_device_id, 'test-device-123')
    
    def test_activate_device_invalid_code(self):
        """Test device activation with invalid code."""
        activation_data = {
            'activation_code': 'INVALID1',
            'device_id': 'test-device-123'
        }
        
        response = self.client.post(self.activate_device_url, activation_data)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
    
    def test_activate_device_expired_code(self):
        """Test device activation with expired code."""
        # Create expired activation code
        expired_code = ActivationCode.objects.create(
            code='EXPIRED1',
            user=self.user,
            expires_at=timezone.now() - timedelta(minutes=1),
            is_active=True
        )
        
        activation_data = {
            'activation_code': 'EXPIRED1',
            'device_id': 'test-device-123'
        }
        
        response = self.client.post(self.activate_device_url, activation_data)
        self.assertEqual(response.status_code, status.HTTP_410_GONE)
    
    def test_activate_device_used_code(self):
        """Test device activation with already used code."""
        # Create used activation code
        used_code = ActivationCode.objects.create(
            code='USEDCODE',
            user=self.user,
            expires_at=timezone.now() + timedelta(minutes=15),
            is_active=False,
            used_at=timezone.now(),
            used_by_device_id='other-device'
        )
        
        activation_data = {
            'activation_code': 'USEDCODE',
            'device_id': 'test-device-123'
        }
        
        response = self.client.post(self.activate_device_url, activation_data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    
    def test_activate_existing_device(self):
        """Test activation of existing device updates it."""
        # Create existing device
        existing_device = UserDevice.objects.create(
            user=self.user,
            device_id='existing-device',
            device_name='Old Name',
            device_type='old_type',
            is_active=False
        )
        
        # Generate activation code
        self.client.force_authenticate(user=self.user)
        response = self.client.get(self.generate_code_url)
        activation_code = response.data['activation_code']
        
        # Activate existing device
        self.client.force_authenticate(user=None)
        activation_data = {
            'activation_code': activation_code,
            'device_id': 'existing-device',
            'device_name': 'New Name',
            'device_type': 'new_type'
        }
        
        response = self.client.post(self.activate_device_url, activation_data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertFalse(response.data['device_info']['created'])
        
        # Check device was updated
        existing_device.refresh_from_db()
        self.assertEqual(existing_device.device_name, 'New Name')
        self.assertEqual(existing_device.device_type, 'new_type')
        self.assertTrue(existing_device.is_active)


class ActivationCodeModelTestCase(TestCase):
    """Test cases for ActivationCode model methods."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.user = UserAccount.objects.create_user(
            email='<EMAIL>',
            password='testpassword123',
            username='<EMAIL>'
        )
    
    def test_is_expired_method(self):
        """Test is_expired method."""
        # Create expired code
        expired_code = ActivationCode.objects.create(
            code='EXPIRED1',
            user=self.user,
            expires_at=timezone.now() - timedelta(minutes=1)
        )
        
        # Create valid code
        valid_code = ActivationCode.objects.create(
            code='VALID001',
            user=self.user,
            expires_at=timezone.now() + timedelta(minutes=15)
        )
        
        self.assertTrue(expired_code.is_expired())
        self.assertFalse(valid_code.is_expired())
    
    def test_is_valid_method(self):
        """Test is_valid method."""
        # Create valid code
        valid_code = ActivationCode.objects.create(
            code='VALID001',
            user=self.user,
            expires_at=timezone.now() + timedelta(minutes=15),
            is_active=True
        )
        
        # Create invalid codes
        expired_code = ActivationCode.objects.create(
            code='EXPIRED1',
            user=self.user,
            expires_at=timezone.now() - timedelta(minutes=1),
            is_active=True
        )
        
        inactive_code = ActivationCode.objects.create(
            code='INACTIVE',
            user=self.user,
            expires_at=timezone.now() + timedelta(minutes=15),
            is_active=False
        )
        
        used_code = ActivationCode.objects.create(
            code='USEDCODE',
            user=self.user,
            expires_at=timezone.now() + timedelta(minutes=15),
            is_active=True,
            used_at=timezone.now()
        )
        
        self.assertTrue(valid_code.is_valid())
        self.assertFalse(expired_code.is_valid())
        self.assertFalse(inactive_code.is_valid())
        self.assertFalse(used_code.is_valid())
    
    def test_deactivate_method(self):
        """Test deactivate method."""
        code = ActivationCode.objects.create(
            code='TESTCODE',
            user=self.user,
            expires_at=timezone.now() + timedelta(minutes=15),
            is_active=True
        )
        
        self.assertTrue(code.is_active)
        self.assertIsNone(code.used_at)
        self.assertIsNone(code.used_by_device_id)
        
        code.deactivate(device_id='test-device')
        
        self.assertFalse(code.is_active)
        self.assertIsNotNone(code.used_at)
        self.assertEqual(code.used_by_device_id, 'test-device')
