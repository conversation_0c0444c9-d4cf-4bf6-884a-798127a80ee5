"""
User account models for VPN service.
"""
from django.contrib.auth.models import AbstractUser, BaseUserManager
from django.db import models
import uuid


class UserAccountManager(BaseUserManager):
    """
    Кастомный менеджер для UserAccount с поддержкой анонимных пользователей.

    PURPOSE:
      - Управляет созданием пользователей (зарегистрированных и анонимных)
      - Обеспечивает корректную обработку email как USERNAME_FIELD
      - Поддерживает создание суперпользователей

    AAG (Actor -> Action -> Goal):
      - Система -> Создает пользователей -> Обеспечивает корректную аутентификацию
      - Django Auth -> Использует менеджер -> Управляет пользователями
    """

    def create_user(self, email=None, password=None, is_anonymous=False, **extra_fields):
        """
        Создает обычного пользователя (зарегистрированного или анонимного).

        PURPOSE:
          - Создает пользователя с правильной обработкой email
          - Поддерживает анонимных пользователей без email
          - Устанавливает корректные значения по умолчанию

        ARGS:
          - email (str, optional): Email для зарегистрированных пользователей
          - password (str, optional): Пароль для зарегистрированных пользователей
          - is_anonymous (bool): Флаг анонимного пользователя
          - extra_fields: Дополнительные поля модели

        RETURNS:
          - UserAccount: Созданный пользователь
        """
        if not is_anonymous and not email:
            raise ValueError('Зарегистрированные пользователи должны иметь email')

        if email:
            email = self.normalize_email(email)

        # Для анонимных пользователей используем UUID как username
        if is_anonymous:
            username = str(uuid.uuid4())
            extra_fields.setdefault('is_anonymous', True)
        else:
            username = email
            extra_fields.setdefault('is_anonymous', False)

        user = self.model(username=username, email=email, **extra_fields)

        if password:
            user.set_password(password)
        else:
            user.set_unusable_password()

        user.save(using=self._db)
        return user

    def create_superuser(self, email, password=None, **extra_fields):
        """
        Создает суперпользователя.

        PURPOSE:
          - Создает администратора системы
          - Устанавливает необходимые права доступа
        """
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        extra_fields.setdefault('is_anonymous', False)

        if extra_fields.get('is_staff') is not True:
            raise ValueError('Superuser must have is_staff=True.')
        if extra_fields.get('is_superuser') is not True:
            raise ValueError('Superuser must have is_superuser=True.')

        return self.create_user(email, password, **extra_fields)


class UserAccount(AbstractUser):
    """
    Расширенная модель пользователя для VPN-сервиса с поддержкой анонимных пользователей.

    PURPOSE:
      - Хранит основную информацию о пользователях нашего сервиса
      - Интегрируется с Django Auth системой
      - Связывает пользователей с их VPN-подписками и устройствами
      - Поддерживает анонимных пользователей (без email/пароля)

    AAG (Actor -> Action -> Goal):
      - Пользователь -> Регистрируется в системе -> Получает доступ к VPN-сервису
      - Анонимный пользователь -> Инициализирует устройство -> Получает временный VPN-доступ
      - Система -> Управляет пользователями -> Контролирует доступ и подписки

    CONTRACT:
      PRECONDITIONS:
        - email (str, optional): Уникальный email адрес для зарегистрированных пользователей
        - password (str, optional): Хэшированный пароль для зарегистрированных пользователей
        - is_anonymous (bool): Флаг анонимного пользователя
      POSTCONDITIONS:
        - Создается уникальная запись пользователя
        - Генерируется UUID для внутреннего использования
        - Анонимные пользователи могут быть конвертированы в зарегистрированных
      INVARIANTS:
        - email уникален в системе только когда не NULL
        - created_at не изменяется после создания
        - is_anonymous=True означает email=NULL и отсутствие пароля
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    email = models.EmailField(null=True, blank=True)
    phone = models.CharField(max_length=20, blank=True, null=True)
    is_anonymous = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_email_verified = models.BooleanField(default=False)
    last_login_ip = models.GenericIPAddressField(blank=True, null=True)

    USERNAME_FIELD = 'username'  # Используем username вместо email для поддержки анонимных пользователей
    REQUIRED_FIELDS = []

    objects = UserAccountManager()

    class Meta:
        db_table = 'user_accounts'
        verbose_name = 'User Account'
        verbose_name_plural = 'User Accounts'
        constraints = [
            models.UniqueConstraint(
                fields=['email'],
                condition=models.Q(email__isnull=False),
                name='unique_email_when_not_null'
            )
        ]

    def __str__(self):
        if self.is_anonymous:
            return f"Anonymous User {str(self.id)[:8]}"
        return self.email or f"User {str(self.id)[:8]}"


class UserDevice(models.Model):
    """
    Модель для управления устройствами пользователей.

    PURPOSE:
      - Отслеживает устройства каждого пользователя
      - Ограничивает количество одновременных подключений
      - Хранит метаданные устройств для аналитики
      - Поддерживает восстановление сессии через device_secret

    AAG (Actor -> Action -> Goal):
      - Пользователь -> Добавляет устройство -> Получает VPN-доступ на устройстве
      - Система -> Контролирует устройства -> Ограничивает злоупотребления

    CONTRACT:
      PRECONDITIONS:
        - user (UserAccount): Существующий пользователь
        - device_id (str): Уникальный идентификатор устройства
        - device_secret (UUID): Уникальный секрет для восстановления сессии
      POSTCONDITIONS:
        - Устройство связывается с пользователем
        - Создается запись для отслеживания активности
      INVARIANTS:
        - Комбинация user + device_id уникальна
        - device_secret уникален глобально
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(UserAccount, on_delete=models.CASCADE, related_name='devices')
    device_id = models.CharField(max_length=255)  # Уникальный ID устройства от клиента
    device_secret = models.UUIDField(unique=True, default=uuid.uuid4, editable=False)  # Секрет для восстановления сессии
    device_name = models.CharField(max_length=100, blank=True)  # "iPhone 13", "Windows PC"
    device_type = models.CharField(max_length=50, blank=True)   # "ios", "android", "windows"
    fcm_token = models.TextField(blank=True, null=True)         # Для push-уведомлений
    last_seen = models.DateTimeField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'user_devices'
        unique_together = ['user', 'device_id']
        indexes = [
            models.Index(fields=['user', 'is_active']),
            models.Index(fields=['device_id']),
        ]

    def __str__(self):
        user_display = self.user.email or f"Anonymous {str(self.user.id)[:8]}"
        return f"{user_display} - {self.device_name or self.device_id}"


class HiddifyLink(models.Model):
    """
    Связующая модель между нашими пользователями и пользователями Hiddify.
    
    PURPOSE:
      - Связывает наших пользователей с UUID пользователей в Hiddify Manager
      - Хранит метаданные интеграции с Hiddify
      - Отслеживает статистику использования VPN
    
    AAG (Actor -> Action -> Goal):
      - Система -> Создает связь при регистрации -> Обеспечивает VPN-доступ
      - Система -> Синхронизирует данные -> Поддерживает актуальную информацию
    
    CONTRACT:
      PRECONDITIONS:
        - user (UserAccount): Существующий пользователь нашей системы
        - hiddify_user_uuid (UUID): UUID пользователя в Hiddify Manager
      POSTCONDITIONS:
        - Создается связь для доступа к VPN через Hiddify
        - Инициализируется отслеживание трафика
      INVARIANTS:
        - hiddify_user_uuid уникален в системе
        - user может иметь только одну активную связь
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.OneToOneField(UserAccount, on_delete=models.CASCADE, related_name='hiddify_link')
    device = models.ForeignKey(UserDevice, on_delete=models.CASCADE, related_name='hiddify_links', null=True, blank=True)
    hiddify_user_uuid = models.UUIDField(unique=True)
    hiddify_comment = models.JSONField(default=dict)  # Метаданные, переданные в Hiddify
    
    # Статистика трафика (синхронизируется с Hiddify)
    traffic_used_bytes = models.BigIntegerField(default=0)
    traffic_limit_bytes = models.BigIntegerField(default=0)
    last_traffic_sync = models.DateTimeField(blank=True, null=True)
    
    # Статус в Hiddify
    is_active_in_hiddify = models.BooleanField(default=True)
    hiddify_created_at = models.DateTimeField()
    last_config_request = models.DateTimeField(blank=True, null=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'hiddify_links'
        indexes = [
            models.Index(fields=['hiddify_user_uuid']),
            models.Index(fields=['user']),
            models.Index(fields=['is_active_in_hiddify']),
        ]

    def __str__(self):
        user_display = self.user.email or f"Anonymous {str(self.user.id)[:8]}"
        return f"{user_display} -> {self.hiddify_user_uuid}"


class DeviceRegistration(models.Model):
    """
    Упрощенная модель для регистрации устройств без создания полноценного пользователя.

    PURPOSE:
      - Обеспечивает быструю регистрацию устройств для мобильных приложений
      - Связывает internal_device_id с hiddify_user_uuid
      - Хранит минимальную информацию для MVP функциональности

    AAG (Actor -> Action -> Goal):
      - Мобильное приложение -> Регистрирует устройство -> Получает VPN доступ
      - Система -> Создает связь с Hiddify -> Обеспечивает VPN конфигурацию

    CONTRACT:
      PRECONDITIONS:
        - internal_device_id (str): Уникальный идентификатор устройства
        - hiddify_user_uuid (UUID): UUID пользователя в Hiddify Manager
      POSTCONDITIONS:
        - Создается связь для доступа к VPN через Hiddify
        - Устройство может получать VPN конфигурации
      INVARIANTS:
        - internal_device_id уникален в системе
        - hiddify_user_uuid уникален в системе
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    internal_device_id = models.CharField(max_length=255, unique=True, db_index=True)
    hiddify_user_uuid = models.UUIDField(unique=True, db_index=True)

    # Опциональная информация об устройстве
    client_generated_device_id = models.CharField(max_length=255, blank=True, null=True)
    device_name = models.CharField(max_length=100, blank=True, null=True)
    device_type = models.CharField(max_length=50, blank=True, null=True)

    # Статистика трафика (синхронизируется с Hiddify)
    traffic_used_bytes = models.BigIntegerField(default=0)
    traffic_limit_bytes = models.BigIntegerField(default=0)
    last_traffic_sync = models.DateTimeField(blank=True, null=True)

    # Статус
    is_active = models.BooleanField(default=True)
    hiddify_created_at = models.DateTimeField()
    last_config_request = models.DateTimeField(blank=True, null=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'device_registrations'
        indexes = [
            models.Index(fields=['internal_device_id']),
            models.Index(fields=['hiddify_user_uuid']),
            models.Index(fields=['is_active']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"Device {self.internal_device_id} -> {self.hiddify_user_uuid}"


class ActivationCode(models.Model):
    """
    Модель для кодов активации устройств в системе двухуровневой аутентификации.

    PURPOSE:
      - Обеспечивает безопасный перенос VPN доступа между устройствами
      - Реализует временные коды активации с ограниченным сроком действия
      - Позволяет пользователю активировать новые устройства без повторной регистрации

    AAG (Actor -> Action -> Goal):
      - Пользователь -> Генерирует код активации -> Получает временный код для нового устройства
      - Новое устройство -> Использует код активации -> Получает VPN доступ к аккаунту
      - Система -> Контролирует коды активации -> Обеспечивает безопасность переноса

    CONTRACT:
      PRECONDITIONS:
        - user (UserAccount): Должен быть валидным зарегистрированным пользователем
        - code (str): Уникальный 8-символьный код активации
        - expires_at (datetime): Время истечения не более 15 минут от создания
      POSTCONDITIONS:
        - Создается временный код активации
        - Код может быть использован для активации нового устройства
        - После использования код деактивируется
      INVARIANTS:
        - code всегда уникален в системе
        - is_active определяет возможность использования кода
        - expires_at всегда больше created_at
    """
    code = models.CharField(
        max_length=8,
        unique=True,
        db_index=True,
        help_text="Уникальный 8-символьный код активации"
    )
    user = models.ForeignKey(
        UserAccount,
        on_delete=models.CASCADE,
        related_name='activation_codes',
        help_text="Пользователь, для которого сгенерирован код"
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        help_text="Время создания кода активации"
    )
    expires_at = models.DateTimeField(
        help_text="Время истечения срока действия кода (15 минут от создания)"
    )
    is_active = models.BooleanField(
        default=True,
        help_text="Активен ли код (не использован и не истек)"
    )
    used_at = models.DateTimeField(
        blank=True,
        null=True,
        help_text="Время использования кода (если был использован)"
    )
    used_by_device_id = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        help_text="ID устройства, которое использовало этот код"
    )

    class Meta:
        db_table = 'activation_codes'
        indexes = [
            models.Index(fields=['code']),
            models.Index(fields=['user', 'is_active']),
            models.Index(fields=['expires_at']),
        ]
        ordering = ['-created_at']

    def __str__(self):
        user_display = self.user.email or f"Anonymous {str(self.user.id)[:8]}"
        return f"Code {self.code} for {user_display} ({'active' if self.is_active else 'inactive'})"

    def is_expired(self):
        """
        Проверяет, истек ли срок действия кода.

        PURPOSE:
          - Определяет валидность кода по времени
          - Используется при проверке возможности активации

        RETURNS:
          - bool: True если код истек, False если еще действителен
        """
        from django.utils import timezone
        return timezone.now() > self.expires_at

    def is_valid(self):
        """
        Проверяет, валиден ли код для использования.

        PURPOSE:
          - Комплексная проверка возможности использования кода
          - Учитывает активность, срок действия и использование

        RETURNS:
          - bool: True если код можно использовать, False если нельзя
        """
        return self.is_active and not self.is_expired() and self.used_at is None

    def deactivate(self, device_id=None):
        """
        Деактивирует код после использования.

        PURPOSE:
          - Помечает код как использованный
          - Предотвращает повторное использование
          - Сохраняет информацию об использовании

        ARGS:
          - device_id (str): ID устройства, которое использовало код
        """
        from django.utils import timezone
        self.is_active = False
        self.used_at = timezone.now()
        if device_id:
            self.used_by_device_id = device_id
        self.save()


class UserSession(models.Model):
    """
    Модель для хранения refresh токенов пользователей.

    PURPOSE:
      - Хранит refresh токены для JWT аутентификации
      - Обеспечивает отзыв токенов при необходимости
      - Связывает токены с конкретными устройствами

    AAG (Actor -> Action -> Goal):
      - Система -> Создает сессию при аутентификации -> Обеспечивает безопасное обновление токенов
      - Пользователь -> Использует refresh токен -> Получает новый access токен

    CONTRACT:
      PRECONDITIONS:
        - user (UserAccount): Существующий пользователь
        - device (UserDevice): Устройство пользователя
        - refresh_token (str): Уникальный refresh токен
      POSTCONDITIONS:
        - Создается сессия для отслеживания токена
        - Токен может быть использован для обновления access токена
      INVARIANTS:
        - refresh_token уникален в системе
        - expires_at > created_at
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(UserAccount, on_delete=models.CASCADE, related_name='sessions')
    device = models.ForeignKey(UserDevice, on_delete=models.CASCADE, related_name='sessions', null=True, blank=True)
    refresh_token = models.TextField(unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()
    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = 'user_sessions'
        indexes = [
            models.Index(fields=['user', 'is_active']),
            models.Index(fields=['refresh_token']),
            models.Index(fields=['expires_at']),
        ]

    def __str__(self):
        user_display = self.user.email or f"Anonymous {str(self.user.id)[:8]}"
        return f"Session for {user_display} (expires: {self.expires_at})"

    def is_expired(self):
        """Проверяет, истек ли срок действия токена."""
        from django.utils import timezone
        return timezone.now() > self.expires_at
