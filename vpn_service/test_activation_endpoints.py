#!/usr/bin/env python3
"""
Тестовый скрипт для проверки эндпоинтов активации.

Проверяет работу трех эндпоинтов:
1. GET /api/auth/activation-code - генерация кода активации
2. POST /api/auth/activate - активация устройства
3. POST /api/subscriptions/activate - активация подписки через промокод
"""

import os
import sys
import django
import requests
import json
from datetime import datetime, timedelta

# Настройка Django
sys.path.append('/root/matrix/vpn_service')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'vpn_service.settings')
django.setup()

from django.contrib.auth import get_user_model
from accounts.models import UserAccount, UserDevice, ActivationCode, HiddifyLink
from subscriptions.models import SubscriptionPlan, ActiveSubscription
from promo.models import PromoCode
from django.utils import timezone

# Конфигурация
BASE_URL = "http://localhost:8090"
API_BASE = f"{BASE_URL}/api"

def create_test_data():
    """Создает тестовые данные для проверки эндпоинтов."""
    print("🔧 Создание тестовых данных...")
    
    # Создаем тестовый тарифный план
    plan, created = SubscriptionPlan.objects.get_or_create(
        name="Test Premium Plan",
        defaults={
            'description': 'Test plan for activation endpoints',
            'price': 10.00,
            'currency': 'USD',
            'duration_days': 30,
            'traffic_limit_gb': 100,
            'max_devices': 3,
            'is_active': True,
            'is_trial': False
        }
    )
    print(f"✅ Тарифный план: {plan.name} ({'создан' if created else 'найден'})")
    
    # Создаем тестового пользователя
    user, created = UserAccount.objects.get_or_create(
        email="<EMAIL>",
        defaults={
            'username': '<EMAIL>',
            'is_anonymous': False
        }
    )
    if created:
        user.set_password('testpass123')
        user.save()
    print(f"✅ Пользователь: {user.email} ({'создан' if created else 'найден'})")
    
    # Создаем устройство для пользователя
    device, created = UserDevice.objects.get_or_create(
        user=user,
        device_id="test-device-123",
        defaults={
            'device_name': 'Test Device',
            'device_type': 'test',
            'is_active': True
        }
    )
    print(f"✅ Устройство: {device.device_id} ({'создано' if created else 'найдено'})")
    
    # Создаем HiddifyLink (заглушка)
    hiddify_link, created = HiddifyLink.objects.get_or_create(
        user=user,
        defaults={
            'device': device,
            'hiddify_user_uuid': 'test-uuid-123',
            'hiddify_comment': {},
            'traffic_limit_bytes': plan.traffic_limit_gb * 1024 * 1024 * 1024,
            'is_active_in_hiddify': True,
            'hiddify_created_at': timezone.now()
        }
    )
    print(f"✅ HiddifyLink: {hiddify_link.hiddify_user_uuid} ({'создан' if created else 'найден'})")
    
    # Создаем активную подписку
    subscription, created = ActiveSubscription.objects.get_or_create(
        user=user,
        is_active=True,
        defaults={
            'plan': plan,
            'start_date': timezone.now(),
            'end_date': timezone.now() + timedelta(days=plan.duration_days)
        }
    )
    print(f"✅ Подписка: {subscription.plan.name} ({'создана' if created else 'найдена'})")
    
    # Создаем промокод
    promo_code, created = PromoCode.objects.get_or_create(
        code="TESTPROMO2024",
        defaults={
            'plan': plan,
            'is_activated': False,
            'expires_at': timezone.now() + timedelta(days=30)
        }
    )
    print(f"✅ Промокод: {promo_code.code} ({'создан' if created else 'найден'})")
    
    return user, device, plan, promo_code

def get_auth_token(email, password):
    """Получает JWT токен для аутентификации."""
    print(f"🔑 Получение токена для {email}...")
    
    response = requests.post(f"{API_BASE}/auth/login/", json={
        'email': email,
        'password': password,
        'device_id': 'test-device-123'
    })
    
    if response.status_code == 200:
        data = response.json()
        token = data['tokens']['access']
        print("✅ Токен получен успешно")
        return token
    else:
        print(f"❌ Ошибка получения токена: {response.status_code}")
        print(response.text)
        return None

def test_generate_activation_code(token):
    """Тестирует GET /api/auth/activation-code"""
    print("\n📱 Тестирование генерации кода активации...")
    
    headers = {'Authorization': f'Bearer {token}'}
    response = requests.get(f"{API_BASE}/auth/activation-code/", headers=headers)
    
    print(f"Статус: {response.status_code}")
    print(f"Ответ: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    
    if response.status_code == 200:
        data = response.json()
        activation_code = data.get('activation_code')
        print(f"✅ Код активации сгенерирован: {activation_code}")
        return activation_code
    else:
        print("❌ Ошибка генерации кода активации")
        return None

def test_activate_device(activation_code):
    """Тестирует POST /api/auth/activate"""
    print(f"\n🔗 Тестирование активации устройства с кодом {activation_code}...")
    
    response = requests.post(f"{API_BASE}/auth/activate/", json={
        'activation_code': activation_code,
        'device_id': 'new-test-device-456',
        'device_name': 'New Test Device',
        'device_type': 'test'
    })
    
    print(f"Статус: {response.status_code}")
    print(f"Ответ: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    
    if response.status_code == 200:
        print("✅ Устройство активировано успешно")
        return True
    else:
        print("❌ Ошибка активации устройства")
        return False

def test_activate_subscription(token, promo_code):
    """Тестирует POST /api/subscriptions/activate"""
    print(f"\n💳 Тестирование активации подписки с промокодом {promo_code}...")
    
    headers = {'Authorization': f'Bearer {token}'}
    response = requests.post(f"{API_BASE}/subscriptions/activate/", 
                           headers=headers,
                           json={'promo_code': promo_code})
    
    print(f"Статус: {response.status_code}")
    print(f"Ответ: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    
    if response.status_code == 200:
        print("✅ Подписка активирована успешно")
        return True
    else:
        print("❌ Ошибка активации подписки")
        return False

def main():
    """Основная функция тестирования."""
    print("🚀 Запуск тестирования эндпоинтов активации")
    print("=" * 50)
    
    try:
        # Создаем тестовые данные
        user, device, plan, promo_code = create_test_data()
        
        # Получаем токен аутентификации
        token = get_auth_token(user.email, 'testpass123')
        if not token:
            print("❌ Не удалось получить токен. Завершение тестов.")
            return
        
        # Тест 1: Генерация кода активации
        activation_code = test_generate_activation_code(token)
        
        # Тест 2: Активация устройства (если код получен)
        if activation_code:
            test_activate_device(activation_code)
        
        # Тест 3: Активация подписки через промокод
        test_activate_subscription(token, promo_code.code)
        
        print("\n" + "=" * 50)
        print("🎉 Тестирование завершено!")
        
    except Exception as e:
        print(f"❌ Ошибка во время тестирования: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
