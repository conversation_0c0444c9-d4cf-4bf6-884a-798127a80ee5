# VPN Service - Django Backend with Hiddify Manager Integration

Полноценный Django backend для управления VPN-сервисом с интеграцией Hiddify Manager.

## 🚀 Возможности

- ✅ **Полная интеграция с Hiddify Manager** через Admin и User API
- ✅ **JWT аутентификация** с поддержкой устройств
- ✅ **Управление подписками** и тарифными планами
- ✅ **Получение VPN конфигураций** (SingBox, Clash, Subscription)
- ✅ **Мониторинг трафика** и синхронизация с Hiddify
- ✅ **Rate limiting** и защита от злоупотреблений
- ✅ **Background tasks** для автоматизации
- ✅ **Docker поддержка** для легкого развертывания

## 📋 Требования

- Python 3.11+
- PostgreSQL 13+
- Redis 6+
- Docker & Docker Compose (опционально)

## 🛠 Установка и запуск

### Вариант 1: Docker (рекомендуется)

1. **Клонируйте проект и перейдите в директорию:**
```bash
cd vpn_service
```

2. **Создайте файл окружения:**
```bash
cp .env.example .env
```

3. **Отредактируйте .env файл:**
```bash
# Обязательно измените:
SECRET_KEY=your-super-secret-key
HIDDIFY_ADMIN_API_KEY=your-hiddify-api-key

# При необходимости измените URL Hiddify:
HIDDIFY_ADMIN_BASE_URL=https://your-hiddify-domain.com/admin-path
HIDDIFY_USER_BASE_URL=https://your-hiddify-domain.com/user-path
```

4. **Запустите с Docker Compose:**
```bash
docker-compose up -d
```

5. **Выполните миграции:**
```bash
docker-compose exec web python manage.py migrate
```

6. **Создайте суперпользователя:**
```bash
docker-compose exec web python manage.py createsuperuser
```

7. **Создайте тестовые тарифные планы:**
```bash
docker-compose exec web python manage.py shell
```

```python
from subscriptions.models import SubscriptionPlan

# Пробный план
SubscriptionPlan.objects.create(
    name="Trial",
    description="7-day trial with 10GB traffic",
    price=0.00,
    duration_days=7,
    traffic_limit_gb=10,
    max_devices=2,
    is_trial=True,
    is_active=True
)

# Базовый план
SubscriptionPlan.objects.create(
    name="Basic",
    description="Monthly plan with 100GB traffic",
    price=9.99,
    duration_days=30,
    traffic_limit_gb=100,
    max_devices=3,
    is_active=True
)
```

### Вариант 2: Локальная установка

1. **Создайте виртуальное окружение:**
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# или
venv\Scripts\activate  # Windows
```

2. **Установите зависимости:**
```bash
pip install -r requirements.txt
```

3. **Настройте базу данных PostgreSQL и Redis**

4. **Создайте .env файл и настройте переменные**

5. **Выполните миграции:**
```bash
python manage.py migrate
```

6. **Запустите сервер:**
```bash
python manage.py runserver
```

7. **В отдельных терминалах запустите Celery:**
```bash
# Worker
celery -A vpn_service worker -l info

# Beat scheduler
celery -A vpn_service beat -l info --scheduler django_celery_beat.schedulers:DatabaseScheduler
```

## 📡 API Эндпоинты

### Аутентификация
```http
POST /api/auth/register/
POST /api/auth/login/
POST /api/auth/token/refresh/
```

### Профиль пользователя
```http
GET /api/auth/profile/
PUT /api/auth/profile/
GET /api/auth/devices/
POST /api/auth/devices/
```

### VPN Конфигурации (Stage 1: SingBox Only)
```http
GET /api/vpn/config/          # Stage 1: только SingBox конфигурации
GET /api/vpn/stats/           # Статистика трафика
```

**Stage 1 Limitations:**
- Поддерживается только SingBox конфигурация
- Параметр `type` не требуется (фиксированно SingBox)
- Clash и Subscription будут добавлены в Stage 2-3

### Подписки
```http
GET /api/subscriptions/plans/
GET /api/subscriptions/current/
POST /api/subscriptions/purchase/
```

## 🔧 Примеры использования

### Регистрация пользователя
```bash
curl -X POST http://localhost:8000/api/auth/register/ \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "securepassword123",
    "password_confirm": "securepassword123",
    "device_id": "unique-device-id-123",
    "device_name": "iPhone 13",
    "device_type": "ios"
  }'
```

### Получение VPN конфигурации (Stage 1: SingBox Only)
```bash
curl -X GET "http://localhost:8000/api/vpn/config/" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Проверка статистики трафика
```bash
curl -X GET http://localhost:8000/api/vpn/stats/ \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 🏗 Архитектура

### Модели данных
- **UserAccount**: Расширенная модель пользователя
- **UserDevice**: Управление устройствами пользователей
- **SubscriptionPlan**: Тарифные планы
- **ActiveSubscription**: Активные подписки
- **HiddifyLink**: Связь с Hiddify Manager
- **PaymentTransaction**: Платежные транзакции

### Сервисы
- **HiddifyApiService**: Интеграция с Hiddify Manager API
- **Rate Limiting Middleware**: Защита от злоупотреблений
- **Background Tasks**: Автоматизация процессов

## 🔒 Безопасность

- JWT аутентификация с device_id
- Rate limiting (60 запросов/минуту по умолчанию)
- Безопасное хранение API ключей
- Валидация всех входных данных
- Структурированное логирование

## 📊 Мониторинг

### Логи
- `logs/django.log` - основные логи Django
- `logs/hiddify_api.log` - логи взаимодействия с Hiddify API

### Background Tasks
- Синхронизация трафика каждые 5 минут
- Проверка истекших подписок каждый час
- Очистка неактивных устройств каждый день

## 🚀 Развертывание в production

1. **Измените настройки безопасности в .env:**
```bash
DEBUG=False
SECRET_KEY=your-production-secret-key
ALLOWED_HOSTS=yourdomain.com,api.yourdomain.com
```

2. **Настройте HTTPS и SSL сертификаты**

3. **Используйте внешние сервисы:**
   - Managed PostgreSQL (AWS RDS, Google Cloud SQL)
   - Managed Redis (AWS ElastiCache, Redis Cloud)
   - Email service (SendGrid, AWS SES)

4. **Настройте мониторинг:**
   - Sentry для отслеживания ошибок
   - Prometheus + Grafana для метрик
   - ELK Stack для логов

## 🧪 Тестирование

```bash
# Запуск тестов
python manage.py test

# С покрытием кода
coverage run --source='.' manage.py test
coverage report
```

## 📝 Дополнительная документация

- [Hiddify Manager API](https://github.com/hiddify/hiddify-manager)
- [Django REST Framework](https://www.django-rest-framework.org/)
- [Celery Documentation](https://docs.celeryproject.org/)

## 🤝 Поддержка

При возникновении проблем:
1. Проверьте логи в `logs/` директории
2. Убедитесь, что Hiddify Manager доступен
3. Проверьте настройки в .env файле
4. Убедитесь, что PostgreSQL и Redis запущены

## 📄 Лицензия

MIT License
