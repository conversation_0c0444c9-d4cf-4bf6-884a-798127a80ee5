# Generated by Django 4.2.7 on 2025-06-07 11:51

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('subscriptions', '0001_initial'),
        ('vpn', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Location',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Уникальный идентификатор локации', primary_key=True, serialize=False)),
                ('name', models.CharField(help_text="Название локации (например, 'Netherlands - Amsterdam')", max_length=100)),
                ('country_code', models.CharField(help_text="ISO код страны (например, 'NL')", max_length=2)),
                ('city', models.Char<PERSON><PERSON>(blank=True, help_text='Город (опционально)', max_length=100)),
                ('flag_emoji', models.CharField(blank=True, help_text="Эмодзи флага страны (например, '🇳🇱')", max_length=10)),
                ('is_active', models.BooleanField(default=True, help_text='Активна ли локация для использования')),
                ('hiddify_params', models.JSONField(default=dict, help_text='Параметры для генерации SingBox outbound (server, server_port, etc.)')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Время создания локации')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='Время последнего обновления')),
            ],
            options={
                'verbose_name': 'VPN Location',
                'verbose_name_plural': 'VPN Locations',
                'db_table': 'vpn_locations',
                'ordering': ['country_code', 'city', 'name'],
            },
        ),
        migrations.CreateModel(
            name='SubscriptionPlanLocation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_default', models.BooleanField(default=False, help_text='Локация по умолчанию для этого плана')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Время создания связи')),
                ('location', models.ForeignKey(help_text='VPN локация', on_delete=django.db.models.deletion.CASCADE, related_name='location_plans', to='vpn.location')),
                ('plan', models.ForeignKey(help_text='Тарифный план', on_delete=django.db.models.deletion.CASCADE, related_name='plan_locations', to='subscriptions.subscriptionplan')),
            ],
            options={
                'verbose_name': 'Plan Location',
                'verbose_name_plural': 'Plan Locations',
                'db_table': 'subscription_plan_locations',
            },
        ),
        migrations.AddIndex(
            model_name='location',
            index=models.Index(fields=['country_code'], name='vpn_locatio_country_1af411_idx'),
        ),
        migrations.AddIndex(
            model_name='location',
            index=models.Index(fields=['is_active'], name='vpn_locatio_is_acti_7cfc12_idx'),
        ),
        migrations.AddIndex(
            model_name='location',
            index=models.Index(fields=['country_code', 'is_active'], name='vpn_locatio_country_cfdab9_idx'),
        ),
        migrations.AddIndex(
            model_name='subscriptionplanlocation',
            index=models.Index(fields=['plan', 'is_default'], name='subscriptio_plan_id_b95aae_idx'),
        ),
        migrations.AddIndex(
            model_name='subscriptionplanlocation',
            index=models.Index(fields=['location', 'plan'], name='subscriptio_locatio_7a8106_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='subscriptionplanlocation',
            unique_together={('plan', 'location')},
        ),
    ]
