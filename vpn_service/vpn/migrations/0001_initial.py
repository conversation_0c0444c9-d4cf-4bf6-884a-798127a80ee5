# Generated by Django 4.2.7 on 2025-06-01 17:17

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='VPNServer',
            fields=[
                ('server_id', models.CharField(help_text='Уникальный ID сервера (например: vpn-de1)', max_length=50, primary_key=True, serialize=False)),
                ('name', models.Char<PERSON><PERSON>(help_text='Название сервера (например: Germany Frankfurt)', max_length=100)),
                ('host', models.Char<PERSON>ield(help_text='Хост сервера (например: vpn-de1.ductuspro.ru)', max_length=255)),
                ('location', models.Char<PERSON><PERSON>(blank=True, help_text='Географическое расположение', max_length=100)),
                ('management_api_url', models.URLField(blank=True, help_text='URL API управления сервером')),
                ('is_active', models.BooleanField(default=True, help_text='Активен ли сервер')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'vpn_servers',
                'indexes': [models.Index(fields=['is_active'], name='vpn_servers_is_acti_7aac38_idx'), models.Index(fields=['location'], name='vpn_servers_locatio_f32510_idx')],
            },
        ),
        migrations.CreateModel(
            name='ConnectionLog',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('status', models.CharField(choices=[('connected', 'Connected'), ('disconnected', 'Disconnected')], max_length=20)),
                ('device_id', models.CharField(help_text='ID устройства пользователя', max_length=255)),
                ('server_id', models.CharField(help_text='ID VPN сервера', max_length=50)),
                ('client_ip', models.GenericIPAddressField(help_text='IP адрес клиента')),
                ('timestamp', models.DateTimeField(help_text='Время события подключения/отключения')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user_agent', models.TextField(blank=True, help_text='User-Agent клиента')),
                ('session_duration', models.DurationField(blank=True, help_text='Длительность сессии (для disconnected)', null=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='connection_logs', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'vpn_connection_logs',
                'ordering': ['-timestamp'],
                'indexes': [models.Index(fields=['user', 'timestamp'], name='vpn_connect_user_id_da4608_idx'), models.Index(fields=['status', 'timestamp'], name='vpn_connect_status_404d96_idx'), models.Index(fields=['server_id', 'timestamp'], name='vpn_connect_server__edcd03_idx'), models.Index(fields=['device_id'], name='vpn_connect_device__622a9e_idx')],
            },
        ),
    ]
