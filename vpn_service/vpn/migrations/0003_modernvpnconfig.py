# Generated by Django 4.2.7 on 2025-06-19 11:15

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('vpn', '0002_location_subscriptionplanlocation_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='ModernVPNConfig',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('protocol', models.CharField(choices=[('vmess', 'VMess'), ('vless', 'VLESS'), ('trojan', 'Trojan'), ('shadowsocks', 'Shadowsocks')], default='vmess', max_length=20)),
                ('transport', models.CharField(choices=[('httpupgrade', 'HTTP Upgrade'), ('xhttp', 'XHTTP'), ('grpc', 'gRPC'), ('websocket', 'WebSocket'), ('quic', 'QUIC/HTTP3'), ('tcp', 'TCP Direct')], default='httpupgrade', max_length=20)),
                ('server_name', models.CharField(default='ductuspro.ru', max_length=255)),
                ('server_port', models.IntegerField(default=443)),
                ('ws_path', models.CharField(blank=True, help_text='Путь для WebSocket/HTTP транспорта', max_length=255)),
                ('ws_headers', models.JSONField(default=dict, help_text='Заголовки для маскировки')),
                ('tls_enabled', models.BooleanField(default=True)),
                ('tls_server_name', models.CharField(default='ductuspro.ru', max_length=255)),
                ('tls_alpn', models.CharField(default='h2', help_text='h2, http/1.1, h3', max_length=50)),
                ('utls_enabled', models.BooleanField(default=True)),
                ('utls_fingerprint', models.CharField(default='chrome', help_text='chrome, firefox, safari, ios', max_length=50)),
                ('cdn_enabled', models.BooleanField(default=True)),
                ('cdn_host', models.CharField(blank=True, help_text='CDN хост для маскировки', max_length=255)),
                ('fragment_enabled', models.BooleanField(default=False)),
                ('fragment_size', models.CharField(default='1-200', max_length=20)),
                ('fragment_sleep', models.CharField(default='0-100', max_length=20)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('location', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='modern_configs', to='vpn.location')),
            ],
            options={
                'db_table': 'vpn_modern_configs',
                'indexes': [models.Index(fields=['location', 'protocol', 'transport'], name='vpn_modern__locatio_9c7ff4_idx'), models.Index(fields=['is_active'], name='vpn_modern__is_acti_c7cac5_idx')],
            },
        ),
    ]
