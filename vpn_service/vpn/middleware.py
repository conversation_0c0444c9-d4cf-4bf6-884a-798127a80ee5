"""
Middleware for VPN app.
"""
from django.core.cache import cache
from django.http import JsonResponse
from django.conf import settings
import time


class HiddifyApiRateLimitMiddleware:
    """
    Middleware для ограничения частоты запросов к Hiddify API.
    
    PURPOSE:
      - Предотвращает превышение лимитов API Hiddify
      - Защищает от DDoS атак на наш сервис
      - Обеспечивает справедливое распределение ресурсов
    
    AAG (Actor -> Action -> Goal):
      - Пользователь -> Делает запрос к VPN API -> Получает ограниченный доступ
      - Middleware -> Проверяет лимиты -> Защищает от злоупотреблений
    
    CONTRACT:
      PRECONDITIONS:
        - HIDDIFY_RATE_LIMIT_PER_MINUTE настроен в settings
        - Redis доступен для кэширования
      POSTCONDITIONS:
        - Запросы ограничиваются согласно настройкам
        - Превышение лимита возвращает HTTP 429
      INVARIANTS:
        - Лимиты применяются per-user
        - Счетчики сбрасываются каждую минуту
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.rate_limit = getattr(settings, 'HIDDIFY_RATE_LIMIT_PER_MINUTE', 60)
    
    def __call__(self, request):
        if self.is_vpn_config_request(request):
            if not self.check_rate_limit(request):
                return JsonResponse({
                    'error': 'Rate limit exceeded. Please try again later.',
                    'retry_after': 60
                }, status=429)
        
        response = self.get_response(request)
        return response
    
    def is_vpn_config_request(self, request):
        """Проверяет, является ли запрос VPN-конфигурационным."""
        return request.path.startswith('/api/vpn/')
    
    def check_rate_limit(self, request):
        """Проверяет лимит запросов для пользователя."""
        user_id = getattr(request.user, 'id', None)
        if not user_id:
            # Для неаутентифицированных пользователей используем IP
            user_id = request.META.get('REMOTE_ADDR', 'unknown')
        
        cache_key = f"rate_limit_user_{user_id}"
        current_requests = cache.get(cache_key, 0)
        
        if current_requests >= self.rate_limit:
            return False
        
        cache.set(cache_key, current_requests + 1, 60)  # 1 минута
        return True


class RequestLoggingMiddleware:
    """
    Middleware для логирования запросов к VPN API.
    
    PURPOSE:
      - Логирует все запросы к VPN эндпоинтам
      - Отслеживает производительность API
      - Обеспечивает аудит использования
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        start_time = time.time()
        
        response = self.get_response(request)
        
        # Логируем только VPN запросы
        if request.path.startswith('/api/vpn/'):
            duration = time.time() - start_time
            self.log_request(request, response, duration)
        
        return response
    
    def log_request(self, request, response, duration):
        """Логирует информацию о запросе."""
        import logging
        logger = logging.getLogger('vpn')
        
        user_id = getattr(request.user, 'id', 'anonymous')
        
        log_data = {
            'user_id': str(user_id),
            'method': request.method,
            'path': request.path,
            'status_code': response.status_code,
            'duration_ms': round(duration * 1000, 2),
            'ip': request.META.get('REMOTE_ADDR'),
            'user_agent': request.META.get('HTTP_USER_AGENT', '')[:100]
        }
        
        if response.status_code >= 400:
            logger.error(f"VPN API Error: {log_data}")
        else:
            logger.info(f"VPN API Request: {log_data}")
