from django.utils import timezone
"""
Современные views для генерации VPN конфигураций с поддержкой
продвинутых транспортов и технологий маскировки.
"""
import logging
from typing import Dict, Any
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status

from .models import Location
from .modern_services import ModernVPNConfigGenerator, ConfigCacheManager
from .services import HiddifyApiService
from subscriptions.models import ActiveSubscription

logger = logging.getLogger(__name__)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_modern_vpn_config(request):
    """
    Получение современной VPN конфигурации с поддержкой продвинутых транспортов.
    
    Поддерживаемые параметры:
    - location_id: ID локации (обязательный)
    - transport: тип транспорта (httpupgrade, grpc, xhttp, websocket)
    - protocol: протокол (vmess, vless) 
    - cdn: использовать ли CDN маскировку (true/false)
    
    Пример: /vpn/modern/config/?location_id=123&transport=httpupgrade&protocol=vmess&cdn=true
    """
    
    # Извлечение параметров
    user_account = request.user
    location_id = request.GET.get('location_id')
    transport_type = request.GET.get('transport', 'httpupgrade')  
    protocol = request.GET.get('protocol', 'vmess')
    use_cdn = request.GET.get('cdn', 'false').lower() == 'true'
    
    # Валидация параметров
    if not location_id:
        return Response({
            'error': 'location_id parameter is required',
            'available_endpoints': [
                '/vpn/modern/config/?location_id=123&transport=httpupgrade&protocol=vmess',
                '/vpn/modern/config/?location_id=123&transport=grpc&protocol=vless&cdn=true'
            ]
        }, status=status.HTTP_400_BAD_REQUEST)
    
    # Валидация транспорта
    valid_transports = ['httpupgrade', 'grpc', 'xhttp', 'websocket', 'quic']
    if transport_type not in valid_transports:
        return Response({
            'error': f'Invalid transport type: {transport_type}',
            'valid_transports': valid_transports
        }, status=status.HTTP_400_BAD_REQUEST)
    
    # Валидация протокола
    valid_protocols = ['vmess', 'vless']
    if protocol not in valid_protocols:
        return Response({
            'error': f'Invalid protocol: {protocol}',
            'valid_protocols': valid_protocols
        }, status=status.HTTP_400_BAD_REQUEST)
    
    try:
        # Проверка локации
        location = Location.objects.get(id=location_id, is_active=True)
        
        # Проверка подписки пользователя
        active_subscription = ActiveSubscription.objects.filter(
            user=user_account,
            is_active=True
        ).first()
        
        if not active_subscription:
            return Response({
                'error': 'No active subscription found',
                'message': 'Please activate a subscription to get VPN config'
            }, status=status.HTTP_403_FORBIDDEN)
        
        # Проверка кэша
        cached_config = ConfigCacheManager.get_cached_config(
            location_id=str(location.id),
            user_uuid=active_subscription.hiddify_user_uuid,
            transport=transport_type,
            protocol=protocol
        )
        
        if cached_config:
            logger.info(f"Returning cached modern config for user {user_account.id}")
            
            # Применяем CDN маскировку если запрошена
            if use_cdn:
                cached_config = ModernVPNConfigGenerator.create_cloudflare_masked_config(cached_config)
            
            return Response({
                'config': cached_config,
                'config_type': 'singbox_modern',
                'transport': transport_type,
                'protocol': protocol,
                'location': {
                    'id': str(location.id),
                    'name': location.name,
                    'country_code': location.country_code,
                    'city': location.city
                },
                'features': {
                    'utls_fingerprinting': True,
                    'cdn_masking': use_cdn,
                    'fragment_support': True,
                    'http2_alpn': transport_type != 'quic',
                    'http3_alpn': transport_type == 'quic'
                },
                'cached': True
            })
        
        # Генерация новой конфигурации
        logger.info(f"Generating modern {protocol} config via {transport_type} for location {location.name}")
        
        config = ModernVPNConfigGenerator.generate_modern_singbox_config(
            location=location,
            user_uuid=active_subscription.hiddify_user_uuid,
            transport_type=transport_type,
            protocol=protocol
        )
        
        # Применяем CDN маскировку если запрошена
        if use_cdn:
            config = ModernVPNConfigGenerator.create_cloudflare_masked_config(config)
        
        # Кэшируем результат
        ConfigCacheManager.cache_config(
            location_id=str(location.id),
            user_uuid=active_subscription.hiddify_user_uuid,
            transport=transport_type,
            protocol=protocol,
            config=config
        )
        
        # Получаем статистику трафика из Hiddify
        hiddify_service = HiddifyApiService()
        success, hiddify_response = hiddify_service.get_singbox_config_for_user(
            active_subscription.hiddify_user_uuid
        )
        
        traffic_info = {}
        if success and 'traffic_info' in hiddify_response:
            traffic_info = hiddify_response['traffic_info']
        
        return Response({
            'config': config,
            'config_type': 'singbox_modern',
            'transport': transport_type,
            'protocol': protocol,
            'location': {
                'id': str(location.id),
                'name': location.name,
                'country_code': location.country_code,
                'city': location.city,
                'flag_emoji': location.flag_emoji
            },
            'features': {
                'utls_fingerprinting': True,
                'cdn_masking': use_cdn,
                'fragment_support': True,
                'http2_alpn': transport_type != 'quic',
                'http3_alpn': transport_type == 'quic',
                'real_hiddify_paths': True
            },
            'traffic_info': traffic_info,
            'cached': False,
            'timestamp': timezone.now().isoformat()
        })
        
    except Location.DoesNotExist:
        return Response({
            'error': 'Location not found or not active',
            'location_id': location_id
        }, status=status.HTTP_404_NOT_FOUND)
        
    except Exception as e:
        logger.error(f"Error generating modern VPN config: {str(e)}")
        return Response({
            'error': 'Internal server error',
            'message': 'Failed to generate VPN configuration'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def get_available_transports(request):
    """
    Возвращает список доступных современных транспортов.
    
    Endpoint: /vpn/modern/transports/
    """
    transports = ModernVPNConfigGenerator.get_available_transports()
    
    return Response({
        'transports': transports,
        'protocols': [
            {
                'type': 'vmess',
                'name': 'VMess',
                'description': 'Популярный протокол с хорошей совместимостью'
            },
            {
                'type': 'vless', 
                'name': 'VLESS',
                'description': 'Современный легкий протокол'
            }
        ],
        'cdn_support': True,
        'utls_fingerprints': ['chrome', 'firefox', 'safari', 'ios', 'android']
    })


@api_view(['GET'])  
@permission_classes([IsAuthenticated])
def get_optimized_config(request):
    """
    Автоматически выбирает оптимальную конфигурацию для пользователя.
    
    Логика выбора:
    - Для мобильных устройств: httpupgrade + vmess
    - Для стран с блокировками: grpc + vless + cdn
    - По умолчанию: httpupgrade + vmess
    
    Endpoint: /vpn/modern/optimized/?location_id=123
    """
    user_account = request.user
    location_id = request.GET.get('location_id')
    user_agent = request.META.get('HTTP_USER_AGENT', '').lower()
    
    if not location_id:
        return Response({
            'error': 'location_id parameter is required'
        }, status=status.HTTP_400_BAD_REQUEST)
    
    try:
        location = Location.objects.get(id=location_id, is_active=True)
        
        # Автоматический выбор оптимального транспорта
        is_mobile = any(x in user_agent for x in ['mobile', 'android', 'iphone'])
        is_blocked_country = location.country_code in ['CN', 'IR', 'RU']  # Примеры стран с блокировками
        
        if is_blocked_country:
            # Для стран с блокировками используем максимальную маскировку
            transport = 'grpc'
            protocol = 'vless' 
            use_cdn = True
        elif is_mobile:
            # Для мобильных устройств приоритет производительности
            transport = 'httpupgrade'
            protocol = 'vmess'
            use_cdn = False
        else:
            # По умолчанию сбалансированная конфигурация
            transport = 'httpupgrade'
            protocol = 'vmess'
            use_cdn = True
        
        # Перенаправляем на основной endpoint с выбранными параметрами
        from django.http import HttpResponseRedirect
        from urllib.parse import urlencode
        
        params = {
            'location_id': location_id,
            'transport': transport,
            'protocol': protocol,
            'cdn': str(use_cdn).lower()
        }
        
        # Внутренний вызов
        request.GET = request.GET.copy()
        for key, value in params.items():
            request.GET[key] = value
            
        return get_modern_vpn_config(request)
        
    except Location.DoesNotExist:
        return Response({
            'error': 'Location not found'
        }, status=status.HTTP_404_NOT_FOUND)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def clear_config_cache(request):
    """
    Очищает кэш конфигураций для пользователя.
    
    Endpoint: /vpn/modern/clear-cache/
    """
    user_account = request.user
    location_id = request.data.get('location_id')
    
    try:
        if location_id:
            ConfigCacheManager.invalidate_location_cache(location_id)
            message = f"Cache cleared for location {location_id}"
        else:
            # Очищаем весь кэш пользователя
            message = "All config cache cleared"
        
        return Response({
            'success': True,
            'message': message
        })
        
    except Exception as e:
        logger.error(f"Error clearing cache: {str(e)}")
        return Response({
            'error': 'Failed to clear cache'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
