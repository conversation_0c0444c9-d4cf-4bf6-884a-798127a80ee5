"""
Celery tasks for VPN app.
"""
from celery import shared_task
from django.utils import timezone
from datetime import timedelta
import logging

logger = logging.getLogger(__name__)


@shared_task
def sync_traffic_statistics():
    """
    Периодическая синхронизация статистики трафика с Hiddify.
    
    PURPOSE:
      - Обновляет статистику использования трафика
      - Выявляет превышения лимитов
      - Поддерживает актуальность данных
    
    AAG (Actor -> Action -> Goal):
      - Система -> Синхронизирует данные -> Поддерживает актуальную статистику
      - Celery Task -> Обновляет записи -> Обеспечивает мониторинг
    
    CONTRACT:
      PRECONDITIONS:
        - Существуют активные HiddifyLink записи
        - Hiddify Manager доступен
      POSTCONDITIONS:
        - Обновляется traffic_used_bytes для всех активных ссылок
        - Логируются результаты синхронизации
      INVARIANTS:
        - Обрабатывается максимум 100 записей за раз
        - Ошибки не прерывают обработку других записей
    """
    from accounts.models import HiddifyLink
    from .services import HiddifyApiService
    
    # Получаем ссылки, которые нужно обновить (старше 1 часа)
    cutoff_time = timezone.now() - timedelta(hours=1)
    links_to_update = HiddifyLink.objects.filter(
        is_active_in_hiddify=True,
        last_traffic_sync__lt=cutoff_time
    )[:100]  # Ограничиваем количество для избежания перегрузки
    
    hiddify_service = HiddifyApiService()
    updated_count = 0
    error_count = 0
    
    for link in links_to_update:
        try:
            success, config_response = hiddify_service.get_subscription_link_for_user(
                str(link.hiddify_user_uuid)
            )
            
            if success and 'traffic_info' in config_response:
                traffic_info = config_response['traffic_info']
                total_bytes = traffic_info.get('download_bytes', 0) + traffic_info.get('upload_bytes', 0)
                
                link.traffic_used_bytes = total_bytes
                link.last_traffic_sync = timezone.now()
                link.save(update_fields=['traffic_used_bytes', 'last_traffic_sync'])
                
                updated_count += 1
                logger.debug(f"Updated traffic for link {link.id}: {total_bytes} bytes")
            else:
                error_count += 1
                logger.warning(f"Failed to get traffic info for link {link.id}: {config_response}")
                
        except Exception as e:
            error_count += 1
            logger.error(f"Failed to sync traffic for link {link.id}: {e}")
    
    result_message = f"Traffic sync completed: {updated_count} updated, {error_count} errors"
    logger.info(result_message)
    return result_message


@shared_task
def check_traffic_limits():
    """
    Проверяет превышения лимитов трафика и уведомляет пользователей.
    
    PURPOSE:
      - Выявляет пользователей, превысивших лимиты трафика
      - Отправляет уведомления о превышении
      - Может деактивировать доступ при критическом превышении
    """
    from accounts.models import HiddifyLink
    from subscriptions.models import ActiveSubscription
    from django.core.mail import send_mail
    from django.conf import settings
    
    # Получаем активные ссылки с недавней синхронизацией
    recent_sync_time = timezone.now() - timedelta(hours=2)
    active_links = HiddifyLink.objects.filter(
        is_active_in_hiddify=True,
        last_traffic_sync__gte=recent_sync_time
    )
    
    warning_sent = 0
    limit_exceeded = 0
    
    for link in active_links:
        try:
            # Получаем активную подписку
            subscription = ActiveSubscription.objects.filter(
                user=link.user,
                is_active=True,
                start_date__lte=timezone.now(),
                end_date__gte=timezone.now()
            ).first()
            
            if not subscription:
                continue
            
            # Проверяем лимиты
            limit_bytes = subscription.plan.traffic_limit_gb * 1024 * 1024 * 1024
            used_bytes = link.traffic_used_bytes or 0
            usage_percentage = (used_bytes / limit_bytes) * 100 if limit_bytes > 0 else 0
            
            # Предупреждение при 80% использования
            if usage_percentage >= 80 and usage_percentage < 100:
                try:
                    subject = "Traffic limit warning"
                    message = f"""
                    Dear {link.user.email},
                    
                    You have used {usage_percentage:.1f}% of your monthly traffic limit.
                    Used: {used_bytes / (1024**3):.2f} GB
                    Limit: {subscription.plan.traffic_limit_gb} GB
                    
                    Please monitor your usage to avoid service interruption.
                    
                    Best regards,
                    VPN Service Team
                    """
                    
                    send_mail(
                        subject,
                        message,
                        settings.DEFAULT_FROM_EMAIL,
                        [link.user.email],
                        fail_silently=True,
                    )
                    
                    warning_sent += 1
                    logger.info(f"Traffic warning sent to {link.user.email}")
                    
                except Exception as e:
                    logger.error(f"Failed to send traffic warning to {link.user.email}: {e}")
            
            # Деактивация при превышении лимита
            elif usage_percentage >= 100:
                # Здесь можно добавить логику деактивации
                # Пока только логируем
                limit_exceeded += 1
                logger.warning(f"Traffic limit exceeded for user {link.user.email}: {usage_percentage:.1f}%")
                
        except Exception as e:
            logger.error(f"Failed to check traffic limits for link {link.id}: {e}")
    
    result_message = f"Traffic limits check completed: {warning_sent} warnings sent, {limit_exceeded} limits exceeded"
    logger.info(result_message)
    return result_message


@shared_task
def cleanup_old_traffic_data():
    """
    Очищает старые данные трафика для оптимизации производительности.
    
    PURPOSE:
      - Удаляет устаревшие записи трафика
      - Оптимизирует размер базы данных
      - Поддерживает производительность системы
    """
    from accounts.models import HiddifyLink
    
    # Сбрасываем статистику трафика для неактивных ссылок старше 30 дней
    cutoff_date = timezone.now() - timedelta(days=30)
    
    updated_count = HiddifyLink.objects.filter(
        is_active_in_hiddify=False,
        updated_at__lt=cutoff_date,
        traffic_used_bytes__gt=0
    ).update(
        traffic_used_bytes=0,
        last_traffic_sync=None
    )
    
    result_message = f"Cleaned up traffic data for {updated_count} inactive links"
    logger.info(result_message)
    return result_message
