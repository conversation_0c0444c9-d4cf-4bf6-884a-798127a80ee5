"""
Расширенные модели для современных VPN конфигураций.
Поддержка Hiddify Manager с продвинутыми транспортами.
"""
from django.db import models
from .models import Location
import uuid


class ModernVPNConfig(models.Model):
    """
    Модель для современных VPN конфигураций с поддержкой
    продвинутых транспортов и технологий маскировки.
    """
    
    TRANSPORT_CHOICES = [
        ('httpupgrade', 'HTTP Upgrade'),
        ('xhttp', 'XHTTP'),
        ('grpc', 'gRPC'),
        ('websocket', 'WebSocket'),
        ('quic', 'QUIC/HTTP3'),
        ('tcp', 'TCP Direct'),
    ]
    
    PROTOCOL_CHOICES = [
        ('vmess', 'VMess'),
        ('vless', 'VLESS'),
        ('trojan', 'Trojan'),
        ('shadowsocks', 'Shadowsocks'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    location = models.ForeignKey(Location, on_delete=models.CASCADE, related_name='modern_configs')
    
    # Основные параметры
    protocol = models.CharField(max_length=20, choices=PROTOCOL_CHOICES, default='vmess')
    transport = models.CharField(max_length=20, choices=TRANSPORT_CHOICES, default='httpupgrade')
    server_name = models.CharField(max_length=255, default='ductuspro.ru')
    server_port = models.IntegerField(default=443)
    
    # WebSocket/HTTP параметры  
    ws_path = models.CharField(max_length=255, blank=True, help_text="Путь для WebSocket/HTTP транспорта")
    ws_headers = models.JSONField(default=dict, help_text="Заголовки для маскировки")
    
    # TLS настройки
    tls_enabled = models.BooleanField(default=True)
    tls_server_name = models.CharField(max_length=255, default='ductuspro.ru')
    tls_alpn = models.CharField(max_length=50, default='h2', help_text="h2, http/1.1, h3")
    
    # UTLS маскировка
    utls_enabled = models.BooleanField(default=True)
    utls_fingerprint = models.CharField(max_length=50, default='chrome', help_text="chrome, firefox, safari, ios")
    
    # CDN поддержка
    cdn_enabled = models.BooleanField(default=True)
    cdn_host = models.CharField(max_length=255, blank=True, help_text="CDN хост для маскировки")
    
    # Дополнительные параметры
    fragment_enabled = models.BooleanField(default=False)
    fragment_size = models.CharField(max_length=20, default='1-200')
    fragment_sleep = models.CharField(max_length=20, default='0-100')
    
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'vpn_modern_configs'
        indexes = [
            models.Index(fields=['location', 'protocol', 'transport']),
            models.Index(fields=['is_active']),
        ]
    
    def __str__(self):
        return f"{self.location.name} - {self.protocol.upper()} via {self.transport}"
    
    def generate_singbox_outbound(self, user_uuid: str) -> dict:
        """
        Генерирует современную SingBox конфигурацию outbound.
        """
        outbound = {
            "type": self.protocol,
            "tag": f"{self.location.country_code.lower()}-{self.transport}",
            "server": self.server_name,
            "server_port": self.server_port,
            "uuid": user_uuid,
        }
        
        # VMess специфичные параметры
        if self.protocol == 'vmess':
            outbound.update({
                "security": "auto",
                "alter_id": 0,
                "packet_encoding": "xudp"
            })
        
        # VLESS специфичные параметры  
        if self.protocol == 'vless':
            outbound.update({
                "packet_encoding": "xudp"
            })
        
        # TLS конфигурация
        if self.tls_enabled:
            tls_config = {
                "enabled": True,
                "server_name": self.tls_server_name,
                "alpn": self.tls_alpn
            }
            
            # UTLS fingerprinting
            if self.utls_enabled:
                tls_config["utls"] = {
                    "enabled": True,
                    "fingerprint": self.utls_fingerprint
                }
            
            outbound["tls"] = tls_config
        
        # Transport конфигурация
        if self.transport in ['httpupgrade', 'websocket']:
            transport_config = {
                "type": self.transport,
                "path": self.ws_path,
                "headers": self.ws_headers or {"Host": self.server_name}
            }
            outbound["transport"] = transport_config
            
        elif self.transport == 'grpc':
            outbound["transport"] = {
                "type": "grpc", 
                "service_name": self.ws_path,
                "idle_timeout": "1m55s",
                "ping_timeout": "15s"
            }
            
        return outbound
    
    def get_real_hiddify_paths(self):
        """
        Возвращает реальные пути из Hiddify конфигурации.
        """
        # Реальные пути из текущей конфигурации Hiddify
        hiddify_paths = {
            'vmess_httpupgrade': '/39m0pgSOdKbicJLIaR',
            'vless_httpupgrade': '/gxz5QZieTLrKrQfdKbicJLIaR', 
            'vmess_grpc': '39m0pgSOOh7gdS9',
            'vless_grpc': 'gxz5QZieTLrKrQfOh7gdS9',
            'vmess_xhttp': '/39m0pgSOMtTtVckTshxQbR0',
            'vless_xhttp': '/gxz5QZieTLrKrQfMtTtVckTshxQbR0'
        }
        
        key = f"{self.protocol}_{self.transport}"
        return hiddify_paths.get(key, '/default-path')
