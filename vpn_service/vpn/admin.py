"""
Admin configuration for VPN app models.
"""
from django.contrib import admin
from .models import VPNServer, ConnectionLog, Location, SubscriptionPlanLocation


@admin.register(VPNServer)
class VPNServerAdmin(admin.ModelAdmin):
    """
    Админ-панель для управления VPN серверами.

    PURPOSE:
      - Обеспечивает удобное управление серверной инфраструктурой
      - Позволяет мониторить состояние серверов
      - Предоставляет быстрый доступ к настройкам серверов

    AAG (Actor -> Action -> Goal):
      - Администратор -> Управляет серверами -> Поддерживает VPN инфраструктуру
    """
    list_display = ('server_id', 'name', 'host', 'location', 'is_active', 'created_at')
    list_filter = ('is_active', 'location', 'created_at')
    search_fields = ('server_id', 'name', 'host', 'location')
    readonly_fields = ('created_at', 'updated_at')
    ordering = ('server_id',)


@admin.register(ConnectionLog)
class ConnectionLogAdmin(admin.ModelAdmin):
    """
    Админ-панель для просмотра логов подключений.

    PURPOSE:
      - Предоставляет доступ к истории подключений пользователей
      - Обеспечивает аудит и мониторинг активности
      - Помогает в диагностике проблем подключения

    AAG (Actor -> Action -> Goal):
      - Администратор -> Анализирует логи -> Мониторит использование VPN
    """
    list_display = ('user', 'status', 'server_id', 'device_id', 'client_ip', 'timestamp')
    list_filter = ('status', 'timestamp', 'server_id')
    search_fields = ('user__email', 'user__username', 'device_id', 'server_id', 'client_ip')
    readonly_fields = ('id', 'created_at')
    ordering = ('-timestamp',)
    date_hierarchy = 'timestamp'

    def has_add_permission(self, request):
        """Запрещаем создание логов через админку."""
        return False

    def has_change_permission(self, request, obj=None):
        """Запрещаем изменение логов через админку."""
        return False


class SubscriptionPlanLocationInline(admin.TabularInline):
    """
    Inline для управления локациями в тарифных планах.

    PURPOSE:
      - Обеспечивает удобное управление привязкой локаций к планам
      - Позволяет настраивать дефолтные локации прямо в плане
      - Упрощает администрирование доступа к серверам

    AAG (Actor -> Action -> Goal):
      - Администратор -> Настраивает локации для плана -> Контролирует доступ пользователей
    """
    model = SubscriptionPlanLocation
    extra = 1
    fields = ('location', 'is_default')
    autocomplete_fields = ('location',)
    verbose_name = "Available Location"
    verbose_name_plural = "Available Locations"


@admin.register(Location)
class LocationAdmin(admin.ModelAdmin):
    """
    Админ-панель для управления VPN локациями.

    PURPOSE:
      - Обеспечивает управление доступными VPN локациями
      - Позволяет настраивать технические параметры серверов
      - Контролирует доступность локаций для пользователей

    AAG (Actor -> Action -> Goal):
      - Администратор -> Управляет локациями -> Предоставляет выбор серверов пользователям
    """
    list_display = ('name', 'country_code', 'city', 'is_active', 'created_at')
    list_filter = ('country_code', 'is_active', 'created_at')
    search_fields = ('name', 'country_code', 'city')
    readonly_fields = ('id', 'created_at', 'updated_at')
    ordering = ('country_code', 'city', 'name')
    
    fieldsets = (
        ('Основная информация', {
            'fields': ('name', 'country_code', 'city', 'flag_emoji', 'is_active')
        }),
        ('Технические параметры', {
            'fields': ('hiddify_params',),
            'description': 'JSON с параметрами для генерации SingBox outbound. '
                          'Обязательные поля: server, server_port. '
                          'Пример: {"server": "nl.vpn.com", "server_port": 443, "tls_server_name": "nl.vpn.com"}'
        }),
        ('Системная информация', {
            'fields': ('id', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )


@admin.register(SubscriptionPlanLocation)
class SubscriptionPlanLocationAdmin(admin.ModelAdmin):
    """
    Админ-панель для управления связями планов и локаций.

    PURPOSE:
      - Предоставляет детальное управление доступом к локациям по планам
      - Позволяет настраивать дефолтные локации
      - Обеспечивает контроль дифференциации сервиса

    AAG (Actor -> Action -> Goal):
      - Администратор -> Настраивает доступ к локациям -> Дифференцирует сервис по тарифам
    """
    list_display = ('plan', 'location', 'is_default', 'created_at')
    list_filter = ('is_default', 'plan', 'location__country_code', 'created_at')
    search_fields = ('plan__name', 'location__name', 'location__country_code')
    autocomplete_fields = ('plan', 'location')
    readonly_fields = ('created_at',)
    ordering = ('plan__name', 'location__country_code', 'location__name')

    def get_queryset(self, request):
        """Оптимизируем запросы с select_related."""
        return super().get_queryset(request).select_related('plan', 'location')
