"""
Serializers для административных API endpoints VPN приложения.
"""
from rest_framework import serializers
from accounts.models import UserAccount


class AdminGenerateConfigRequestSerializer(serializers.Serializer):
    """
    Serializer для запроса генерации персонализированной конфигурации администратором.
    
    PURPOSE:
      - Валидирует входные данные для административного endpoint
      - Обеспечивает корректность параметров запроса
      - Предоставляет документацию для Swagger UI
    
    AAG (Actor -> Action -> Goal):
      - Администратор -> Отправляет запрос -> Получает валидированные данные
      - Система -> Валидирует данные -> Генерирует конфигурацию
    
    CONTRACT:
      PRECONDITIONS:
        - user_id: UUID существующего пользователя
        - force_recreate: boolean (опционально)
      POSTCONDITIONS:
        - Возвращает валидированные данные
        - Проверяет существование пользователя
      INVARIANTS:
        - user_id всегда валидный UUID
        - force_recreate всегда boolean
    """
    
    user_id = serializers.UUIDField(
        help_text="UUID пользователя для генерации конфигурации",
        required=True
    )
    
    force_recreate = serializers.BooleanField(
        help_text="Принудительно пересоздать пользователя в Hiddify Manager (если уже существует)",
        required=False,
        default=False
    )
    
    def validate_user_id(self, value):
        """
        Проверяет, что пользователь существует.
        
        PURPOSE:
          - Убеждается, что указанный пользователь существует в системе
          - Предотвращает генерацию конфигураций для несуществующих пользователей
        
        ARGS:
          - value (UUID): UUID пользователя
        
        RETURNS:
          - UUID: Валидированный UUID пользователя
        
        RAISES:
          - ValidationError: Если пользователь не найден
        """
        try:
            user = UserAccount.objects.get(id=value)
            # Сохраняем объект пользователя для использования в view
            self._validated_user = user
            return value
        except UserAccount.DoesNotExist:
            raise serializers.ValidationError(f"User with ID {value} does not exist")
    
    def get_validated_user(self):
        """
        Возвращает валидированный объект пользователя.
        
        PURPOSE:
          - Предоставляет доступ к объекту пользователя после валидации
          - Избегает повторных запросов к базе данных
        
        RETURNS:
          - UserAccount: Объект пользователя
        """
        return getattr(self, '_validated_user', None)


class SingBoxConfigResponseSerializer(serializers.Serializer):
    """
    Serializer для персонализированной SingBox VPN конфигурации.

    ОПИСАНИЕ:
      Документирует структуру ответа endpoint'а, который возвращает готовую к использованию
      SingBox конфигурацию в формате JSON. Структура точно соответствует эталонному файлу
      singbox_Config_example без дополнительных полей обертки.

    ТЕХНИЧЕСКИЕ ХАРАКТЕРИСТИКИ:
      🌐 Сервер: *********** (IP адрес вместо домена)
      🔒 TLS Server Name: ***********.sslip.io
      🔐 Протоколы: Trojan, VMess
      🚀 Транспорты: WebSocket, gRPC, HTTP Upgrade
      📱 Клиент: SingBox (Android, iOS, Windows, macOS, Linux)

    СТРУКТУРА КОНФИГУРАЦИИ:
      - dns: DNS серверы и правила разрешения
      - inbounds: TUN интерфейс для перехвата трафика
      - outbounds: VPN протоколы и прямые подключения
      - route: Правила маршрутизации трафика

    ИСПОЛЬЗОВАНИЕ:
      Сохраните полученную конфигурацию в файл .json и импортируйте в SingBox клиент.
      Конфигурация готова к использованию без дополнительных настроек.
    """

    dns = serializers.DictField(
        help_text=(
            "DNS конфигурация с серверами Cloudflare, Google и локальными DNS. "
            "Включает правила для обхода блокировок и оптимизации скорости. "
            "Поддерживает DoH (DNS over HTTPS) и DoT (DNS over TLS)."
        )
    )

    inbounds = serializers.ListField(
        help_text=(
            "Входящие подключения с TUN интерфейсом для перехвата системного трафика. "
            "Настроен для IPv4/IPv6 с автоматической маршрутизацией и sniffing доменов. "
            "Адреса: **********/30 (IPv4), fdfe:dcba:9876::1/126 (IPv6)."
        ),
        child=serializers.DictField()
    )

    outbounds = serializers.ListField(
        help_text=(
            "Исходящие VPN подключения с поддержкой множественных протоколов: "
            "1. selector - автоматический выбор лучшего подключения "
            "2. trojan-ws - Trojan через WebSocket "
            "3. vmess-ws - VMess через WebSocket "
            "4. vmess-httpupgrade - VMess через HTTP Upgrade "
            "5. trojan-grpc - Trojan через gRPC "
            "6. vmess-grpc - VMess через gRPC "
            "7. direct - прямое подключение без VPN "
            "8. block - блокировка трафика "
            "9. dns-out - DNS запросы"
        ),
        child=serializers.DictField()
    )

    route = serializers.DictField(
        help_text=(
            "Правила маршрутизации трафика с поддержкой GeoIP и доменных правил. "
            "Локальный трафик (private, cn, .ru, .cn) идет напрямую, "
            "остальной трафик через VPN. Автоматическое определение интерфейса."
        )
    )


class AdminGenerateConfigResponseSerializer(SingBoxConfigResponseSerializer):
    """
    Serializer для ответа административного endpoint генерации конфигурации.
    Наследует от SingBoxConfigResponseSerializer для точного соответствия структуре.
    """
    pass


class AdminGenerateConfigErrorSerializer(serializers.Serializer):
    """
    Serializer для ошибок административного endpoint.
    
    PURPOSE:
      - Документирует возможные ошибки для Swagger UI
      - Обеспечивает консистентность формата ошибок
      - Помогает в отладке и мониторинге
    """
    
    success = serializers.BooleanField(
        default=False,
        help_text="Статус операции (всегда false для ошибок)"
    )
    
    error = serializers.CharField(
        help_text="Описание ошибки"
    )
    
    error_code = serializers.CharField(
        help_text="Код ошибки для программной обработки",
        required=False
    )
    
    details = serializers.DictField(
        help_text="Дополнительные детали ошибки",
        child=serializers.CharField(),
        required=False
    )


class UserInfoSerializer(serializers.ModelSerializer):
    """
    Serializer для информации о пользователе в административных ответах.
    
    PURPOSE:
      - Предоставляет безопасную информацию о пользователе
      - Исключает чувствительные данные (пароли, токены)
      - Используется в составе других serializers
    """
    
    active_subscription = serializers.SerializerMethodField()
    device_count = serializers.SerializerMethodField()
    
    class Meta:
        model = UserAccount
        fields = [
            'id', 'email', 'is_anonymous', 'is_active', 
            'date_joined', 'last_login', 'active_subscription', 'device_count'
        ]
        read_only_fields = fields
    
    def get_active_subscription(self, obj):
        """
        Возвращает информацию об активной подписке пользователя.
        
        ARGS:
          - obj (UserAccount): Объект пользователя
        
        RETURNS:
          - dict: Информация о подписке или None
        """
        from subscriptions.models import ActiveSubscription
        from django.utils import timezone
        
        active_subscription = ActiveSubscription.objects.filter(
            user=obj,
            is_active=True,
            start_date__lte=timezone.now(),
            end_date__gte=timezone.now()
        ).select_related('plan').first()
        
        if active_subscription:
            return {
                'plan_name': active_subscription.plan.name,
                'expires_at': active_subscription.end_date.isoformat(),
                'traffic_limit_gb': active_subscription.plan.traffic_limit_gb,
                'max_devices': active_subscription.plan.max_devices
            }
        return None
    
    def get_device_count(self, obj):
        """
        Возвращает количество устройств пользователя.
        
        ARGS:
          - obj (UserAccount): Объект пользователя
        
        RETURNS:
          - int: Количество активных устройств
        """
        return obj.devices.filter(is_active=True).count()


class LocationInfoSerializer(serializers.Serializer):
    """
    Serializer для информации о локации в административных ответах.

    PURPOSE:
      - Предоставляет информацию о выбранной локации
      - Документирует структуру данных локации
      - Используется в составе ответов конфигурации
    """

    id = serializers.UUIDField(help_text="UUID локации")
    name = serializers.CharField(help_text="Название локации")
    country_code = serializers.CharField(help_text="Код страны")
    city = serializers.CharField(help_text="Город", required=False)
    flag_emoji = serializers.CharField(help_text="Эмодзи флага", required=False)
    is_active = serializers.BooleanField(help_text="Активна ли локация")
    server_info = serializers.DictField(
        help_text="Информация о сервере локации",
        child=serializers.CharField(),
        required=False
    )


# Новые serializers для упрощенного API

class SimpleAdminConfigRequestSerializer(serializers.Serializer):
    """
    Serializer для автоматической генерации персонализированных SingBox VPN конфигураций.

    ОПИСАНИЕ:
      Этот endpoint предоставляет упрощенный способ создания VPN конфигураций для пользователей.
      Автоматически создает новых пользователей, подписки и интегрируется с Hiddify Manager
      для генерации готовых к использованию SingBox конфигураций.

    ФУНКЦИОНАЛЬНОСТЬ:
      ✅ Автоматическое создание новых пользователей по email
      ✅ Создание Trial подписки для новых пользователей
      ✅ Интеграция с Hiddify Manager для VPN пользователей
      ✅ Генерация персонализированных SingBox конфигураций
      ✅ Поддержка протоколов: Trojan, VMess
      ✅ Поддержка транспортов: WebSocket, gRPC, HTTP Upgrade
      ✅ Использование IP адреса *********** для серверов
      ✅ Возврат чистой конфигурации без обертки

    ПРОЦЕСС РАБОТЫ:
      1. Проверка существования пользователя по email
      2. Создание нового пользователя если не найден
      3. Создание Trial подписки (30 дней, 50GB)
      4. Создание пользователя в Hiddify Manager
      5. Генерация персонализированной SingBox конфигурации
      6. Возврат готовой конфигурации в формате JSON

    АУТЕНТИФИКАЦИЯ:
      Требуется заголовок X-API-Key с валидным API ключом Hiddify Manager.

    ФОРМАТ ОТВЕТА:
      Возвращает чистую SingBox конфигурацию в формате JSON без дополнительных полей.
      Структура точно соответствует эталонному файлу singbox_Config_example.
    """

    user_email = serializers.EmailField(
        help_text=(
            "Email адрес пользователя для генерации VPN конфигурации. "
            "Если пользователь с таким email не существует, будет создан автоматически "
            "с Trial подпиской (30 дней, 50GB трафика). "
            "Пример: <EMAIL>"
        ),
        required=True,
        error_messages={
            'required': 'Email адрес обязателен для генерации конфигурации',
            'invalid': 'Введите корректный email адрес в формате <EMAIL>'
        }
    )



    def validate_user_email(self, value):
        """
        Валидирует email адрес пользователя.

        ARGS:
          - value (str): Email адрес для валидации

        RETURNS:
          - str: Валидированный email адрес

        RAISES:
          - ValidationError: Если email имеет недопустимый формат
        """
        if not value:
            raise serializers.ValidationError(
                "Email адрес обязателен для генерации VPN конфигурации"
            )

        # Дополнительная валидация email
        if len(value) > 254:
            raise serializers.ValidationError(
                "Email адрес слишком длинный (максимум 254 символа)"
            )

        return value.lower().strip()


class SimpleAdminConfigResponseSerializer(SingBoxConfigResponseSerializer):
    """
    Serializer для ответа упрощенного административного endpoint.
    Наследует от SingBoxConfigResponseSerializer для точного соответствия структуре.
    """
    pass
