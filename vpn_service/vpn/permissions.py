"""
Permissions классы для VPN приложения.
"""
from rest_framework import permissions


class IsAdminUser(permissions.BasePermission):
    """
    Разрешение только для администраторов (staff пользователей).
    
    PURPOSE:
      - Ограничивает доступ к административным API endpoints
      - Проверяе<PERSON>, что пользователь является staff member
      - Обеспечивает безопасность административных функций
    
    AAG (Actor -> Action -> Goal):
      - Администратор -> Обращается к admin API -> Получает доступ к функциям
      - Обычный пользователь -> Обращается к admin API -> Получает отказ в доступе
    
    CONTRACT:
      PRECONDITIONS:
        - request: HTTP запрос с аутентифицированным пользователем
      POSTCONDITIONS:
        - True если пользователь является staff, False иначе
      INVARIANTS:
        - Проверка выполняется для каждого запроса
        - Анонимные пользователи всегда получают False
    """
    
    def has_permission(self, request, view):
        """
        Проверяет, является ли пользователь администратором.
        
        ARGS:
          - request: HTTP запрос
          - view: View класс
        
        RETURNS:
          - bool: True если пользователь администратор, False иначе
        """
        return (
            request.user and
            request.user.is_authenticated and
            request.user.is_staff
        )


class IsSuperUser(permissions.BasePermission):
    """
    Разрешение только для суперпользователей.
    
    PURPOSE:
      - Ограничивает доступ к критически важным функциям
      - Проверяет, что пользователь является суперпользователем
      - Обеспечивает дополнительный уровень безопасности
    """
    
    def has_permission(self, request, view):
        """
        Проверяет, является ли пользователь суперпользователем.
        
        ARGS:
          - request: HTTP запрос
          - view: View класс
        
        RETURNS:
          - bool: True если пользователь суперпользователь, False иначе
        """
        return (
            request.user and
            request.user.is_authenticated and
            request.user.is_superuser
        )


class IsAdminOrReadOnly(permissions.BasePermission):
    """
    Разрешение на чтение для всех, на изменение только для администраторов.

    PURPOSE:
      - Позволяет всем пользователям читать данные
      - Ограничивает изменения только администраторами
      - Используется для публичных справочников с административным управлением
    """

    def has_permission(self, request, view):
        """
        Проверяет права доступа в зависимости от метода запроса.

        ARGS:
          - request: HTTP запрос
          - view: View класс

        RETURNS:
          - bool: True если доступ разрешен, False иначе
        """
        # Разрешаем чтение всем аутентифицированным пользователям
        if request.method in permissions.SAFE_METHODS:
            return request.user and request.user.is_authenticated

        # Разрешаем изменения только администраторам
        return (
            request.user and
            request.user.is_authenticated and
            request.user.is_staff
        )


class HasValidAPIKey(permissions.BasePermission):
    """
    Разрешение на основе проверки API ключа.

    PURPOSE:
      - Обеспечивает простую аутентификацию через статический API ключ
      - Заменяет сложную JWT аутентификацию для административных endpoint'ов
      - Использует тот же API ключ, что и Hiddify Manager для консистентности

    AAG (Actor -> Action -> Goal):
      - Внешний сервис -> Передает API ключ -> Получает доступ к endpoint
      - Система -> Проверяет ключ -> Разрешает или запрещает доступ
      - Администратор -> Использует известный ключ -> Упрощает интеграцию

    CONTRACT:
      PRECONDITIONS:
        - API ключ передается через заголовок X-API-Key или параметр api_key
        - Ключ должен соответствовать статическому значению
      POSTCONDITIONS:
        - True если ключ валиден, False иначе
        - Логирование попыток доступа
      INVARIANTS:
        - Статический ключ: be84eb6e-cf9d-4b2b-b063-fdf26960ebca
        - Проверка выполняется для каждого запроса
    """

    # Статический API ключ (тот же, что используется в Hiddify Manager)
    VALID_API_KEY = "be84eb6e-cf9d-4b2b-b063-fdf26960ebca"

    def has_permission(self, request, view):
        """
        Проверяет валидность API ключа из заголовка или параметра запроса.

        PURPOSE:
          - Извлекает API ключ из заголовка X-API-Key или параметра api_key
          - Сравнивает с валидным статическим ключом
          - Логирует попытки доступа для мониторинга

        ARGS:
          - request: HTTP запрос
          - view: View класс

        RETURNS:
          - bool: True если API ключ валиден, False иначе
        """
        import logging
        logger = logging.getLogger(__name__)

        # Извлекаем API ключ из заголовка
        api_key_header = request.META.get('HTTP_X_API_KEY')

        # Извлекаем API ключ из параметров запроса (GET или POST)
        api_key_param = None
        if request.method == 'GET':
            api_key_param = request.GET.get('api_key')
        elif request.method == 'POST' and hasattr(request, 'data'):
            api_key_param = request.data.get('api_key')

        # Используем ключ из заголовка или параметра
        provided_key = api_key_header or api_key_param

        if not provided_key:
            logger.warning(f"API key missing in request from {request.META.get('REMOTE_ADDR', 'unknown')}")
            return False

        # Проверяем валидность ключа
        is_valid = provided_key == self.VALID_API_KEY

        if is_valid:
            logger.info(f"Valid API key access from {request.META.get('REMOTE_ADDR', 'unknown')}")
        else:
            logger.warning(f"Invalid API key attempt from {request.META.get('REMOTE_ADDR', 'unknown')}: {provided_key[:8]}...")

        return is_valid


class IsAdminUserOrHasAPIKey(permissions.BasePermission):
    """
    Разрешение для администраторов через JWT или пользователей с валидным API ключом.

    PURPOSE:
      - Поддерживает два типа аутентификации: JWT для администраторов и API ключ
      - Обеспечивает обратную совместимость с существующими JWT endpoint'ами
      - Позволяет использовать простую API ключ аутентификацию для интеграций

    AAG (Actor -> Action -> Goal):
      - Администратор -> Использует JWT токен -> Получает доступ к endpoint
      - Внешний сервис -> Использует API ключ -> Получает доступ к endpoint
      - Система -> Проверяет оба типа аутентификации -> Разрешает доступ

    CONTRACT:
      PRECONDITIONS:
        - Либо валидный JWT токен с правами администратора
        - Либо валидный API ключ в заголовке или параметре
      POSTCONDITIONS:
        - True если любой из типов аутентификации прошел
        - False если оба типа аутентификации не прошли
      INVARIANTS:
        - Проверка JWT выполняется первой
        - API ключ проверяется только если JWT не прошел
    """

    def has_permission(self, request, view):
        """
        Проверяет права доступа через JWT или API ключ.

        PURPOSE:
          - Сначала проверяет JWT аутентификацию для администраторов
          - Если JWT не прошел, проверяет API ключ
          - Логирует тип использованной аутентификации

        ARGS:
          - request: HTTP запрос
          - view: View класс

        RETURNS:
          - bool: True если доступ разрешен, False иначе
        """
        import logging
        logger = logging.getLogger(__name__)

        # Проверяем JWT аутентификацию для администраторов
        if (request.user and
            request.user.is_authenticated and
            request.user.is_staff):
            logger.info(f"Admin JWT access granted for {request.user.email}")
            return True

        # Проверяем API ключ
        api_key_permission = HasValidAPIKey()
        if api_key_permission.has_permission(request, view):
            logger.info("API key access granted")
            return True

        logger.warning("Access denied: neither JWT admin nor valid API key")
        return False
