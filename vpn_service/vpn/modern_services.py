"""
Современный сервис для генерации VPN конфигураций с поддержкой
продвинутых транспортов и технологий маскировки.
"""
import logging
from typing import Dict, List, Tuple
from django.core.cache import cache
from .models import Location
from .advanced_models import ModernVPNConfig

logger = logging.getLogger(__name__)


class ModernVPNConfigGenerator:
    """
    Генератор современных VPN конфигураций с поддержкой:
    - HTTP Upgrade / WebSocket транспорта  
    - UTLS fingerprinting для маскировки
    - Cloudflare CDN поддержки
    - HTTP/2 и HTTP/3 протоколов
    - Fragment пакетов против DPI
    """
    
    @staticmethod
    def generate_modern_singbox_config(location: Location, user_uuid: str, 
                                     transport_type: str = 'httpupgrade',
                                     protocol: str = 'vmess') -> dict:
        """
        Генерирует современную SingBox конфигурацию с продвинутыми возможностями.
        
        ARGS:
            - location: Объект локации
            - user_uuid: UUID пользователя 
            - transport_type: Тип транспорта (httpupgrade, xhttp, grpc, quic)
            - protocol: Протокол (vmess, vless)
        """
        
        # Реальные пути из вашей Hiddify конфигурации
        real_paths = {
            'vmess_httpupgrade': '/39m0pgSOdKbicJLIaR',
            'vless_httpupgrade': '/gxz5QZieTLrKrQfdKbicJLIaR',
            'vmess_grpc': '39m0pgSOOh7gdS9', 
            'vless_grpc': 'gxz5QZieTLrKrQfOh7gdS9',
            'vmess_xhttp': '/39m0pgSOMtTtVckTshxQbR0',
            'vless_xhttp': '/gxz5QZieTLrKrQfMtTtVckTshxQbR0'
        }
        
        path_key = f"{protocol}_{transport_type}"
        ws_path = real_paths.get(path_key, '/default-path')
        
        # Полная SingBox конфигурация с современными возможностями
        config = {
            "log": {
                "level": "warn",
                "timestamp": True
            },
            "dns": {
                "servers": [
                    {
                        "tag": "dns-remote",
                        "address": "udp://*******",
                        "address_resolver": "dns-direct"
                    },
                    {
                        "tag": "dns-direct", 
                        "address": "*******",
                        "address_resolver": "dns-local",
                        "detour": "direct"
                    },
                    {
                        "tag": "dns-local",
                        "address": "local",
                        "detour": "direct"
                    }
                ],
                "rules": [
                    {
                        "domain": ["ductuspro.ru"],
                        "server": "dns-direct"
                    }
                ],
                "final": "dns-remote",
                "independent_cache": True
            },
            "inbounds": [
                {
                    "type": "mixed",
                    "tag": "mixed-in", 
                    "listen": "127.0.0.1",
                    "listen_port": 2334,
                    "sniff": True,
                    "sniff_override_destination": True
                },
                {
                    "type": "tun",
                    "tag": "tun-in",
                    "interface_name": "tun0", 
                    "inet4_address": "**********/30",
                    "auto_route": True,
                    "strict_route": False,
                    "sniff": True
                }
            ],
            "outbounds": []
        }
        
        # Генерируем outbound для выбранного транспорта
        outbound = ModernVPNConfigGenerator._create_outbound(
            protocol=protocol,
            transport=transport_type,
            user_uuid=user_uuid,
            server="ductuspro.ru",
            server_port=443,
            ws_path=ws_path
        )
        
        config["outbounds"].append(outbound)
        
        # Добавляем служебные outbounds
        config["outbounds"].extend([
            {
                "type": "dns",
                "tag": "dns-out"
            },
            {
                "type": "direct",
                "tag": "direct"
            },
            {
                "type": "direct", 
                "tag": "direct-fragment",
                "tls_fragment": {
                    "enabled": True,
                    "size": "1-200",
                    "sleep": "0-100"
                }
            },
            {
                "type": "block",
                "tag": "block"
            }
        ])
        
        # Правила маршрутизации
        config["route"] = {
            "rules": [
                {
                    "inbound": "dns-in",
                    "outbound": "dns-out"
                },
                {
                    "port": 53,
                    "outbound": "dns-out"
                },
                {
                    "ip_is_private": True,
                    "outbound": "direct"
                }
            ],
            "final": f"{location.country_code.lower()}-{transport_type}",
            "auto_detect_interface": True,
            "override_android_vpn": True
        }
        
        # Экспериментальные возможности
        config["experimental"] = {
            "cache_file": {
                "enabled": True,
                "path": "clash.db"
            },
            "clash_api": {
                "external_controller": "127.0.0.1:6756",
                "external_ui": "webui",
                "secret": "hiddify"
            }
        }
        
        logger.info(f"Generated modern SingBox config: {protocol} via {transport_type} for {location.name}")
        return config
    
    @staticmethod 
    def _create_outbound(protocol: str, transport: str, user_uuid: str, 
                        server: str, server_port: int, ws_path: str) -> dict:
        """
        Создает современный outbound с UTLS и продвинутыми транспортами.
        """
        
        outbound = {
            "type": protocol,
            "tag": f"modern-{protocol}-{transport}",
            "server": server,
            "server_port": server_port,
            "uuid": user_uuid,
        }
        
        # Протокол-специфичные настройки
        if protocol == 'vmess':
            outbound.update({
                "security": "auto",
                "alter_id": 0,
                "packet_encoding": "xudp"
            })
        elif protocol == 'vless':
            outbound.update({
                "packet_encoding": "xudp"
            })
        
        # Современная TLS конфигурация с UTLS
        tls_config = {
            "enabled": True,
            "server_name": server,
            "alpn": "h2" if transport != 'quic' else "h3",
            "utls": {
                "enabled": True,
                "fingerprint": "chrome"  # Имитируем Chrome браузер
            }
        }
        outbound["tls"] = tls_config
        
        # Transport конфигурация
        if transport == 'httpupgrade':
            outbound["transport"] = {
                "type": "httpupgrade",
                "path": ws_path,
                "headers": {
                    "Host": server,
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
                }
            }
            
        elif transport == 'grpc':
            outbound["transport"] = {
                "type": "grpc",
                "service_name": ws_path.lstrip('/'),  # gRPC без слеша
                "idle_timeout": "1m55s",
                "ping_timeout": "15s"
            }
            
        elif transport == 'websocket':
            outbound["transport"] = {
                "type": "ws",
                "path": ws_path,
                "headers": {
                    "Host": server
                }
            }
        
        return outbound
    
    @staticmethod
    def get_available_transports() -> List[Dict]:
        """
        Возвращает список доступных современных транспортов.
        """
        return [
            {
                "type": "httpupgrade",
                "name": "HTTP Upgrade",
                "description": "Современный HTTP upgrade транспорт",
                "performance": "high",
                "stealth": "excellent"
            },
            {
                "type": "grpc", 
                "name": "gRPC",
                "description": "Высокопроизводительный gRPC транспорт",
                "performance": "excellent", 
                "stealth": "good"
            },
            {
                "type": "xhttp",
                "name": "XHTTP",
                "description": "Новейший XHTTP протокол", 
                "performance": "excellent",
                "stealth": "excellent"
            },
            {
                "type": "websocket",
                "name": "WebSocket",
                "description": "Классический WebSocket транспорт",
                "performance": "good",
                "stealth": "good"
            }
        ]
    
    @staticmethod
    def create_cloudflare_masked_config(base_config: dict, cdn_ip: str = None) -> dict:
        """
        Создает конфигурацию с Cloudflare CDN маскировкой.
        """
        masked_config = base_config.copy()
        
        # Cloudflare IP адреса для маскировки
        cloudflare_ips = [
            "************",
            "**************", 
            "*************",
            "*************"
        ]
        
        if cdn_ip or cloudflare_ips:
            # Используем CDN IP вместо реального домена
            for outbound in masked_config.get("outbounds", []):
                if outbound.get("type") in ["vmess", "vless"]:
                    outbound["server"] = cdn_ip or cloudflare_ips[0]
                    
                    # Обновляем заголовки для правильной маршрутизации
                    if "transport" in outbound:
                        if "headers" not in outbound["transport"]:
                            outbound["transport"]["headers"] = {}
                        outbound["transport"]["headers"]["Host"] = "ductuspro.ru"
        
        logger.info("Applied Cloudflare CDN masking to config")
        return masked_config


class ConfigCacheManager:
    """
    Менеджер кэширования конфигураций для оптимизации производительности.
    """
    
    CACHE_PREFIX = "modern_vpn_config"
    CACHE_TIMEOUT = 3600  # 1 час
    
    @classmethod
    def get_cached_config(cls, location_id: str, user_uuid: str, 
                         transport: str, protocol: str) -> dict:
        """
        Получает конфигурацию из кэша.
        """
        cache_key = f"{cls.CACHE_PREFIX}_{location_id}_{transport}_{protocol}"
        return cache.get(cache_key)
    
    @classmethod  
    def cache_config(cls, location_id: str, user_uuid: str,
                    transport: str, protocol: str, config: dict):
        """
        Сохраняет конфигурацию в кэш.
        """
        cache_key = f"{cls.CACHE_PREFIX}_{location_id}_{transport}_{protocol}"
        cache.set(cache_key, config, cls.CACHE_TIMEOUT)
        logger.info(f"Cached config: {cache_key}")
    
    @classmethod
    def invalidate_location_cache(cls, location_id: str):
        """
        Инвалидирует кэш для конкретной локации.
        """
        # Простая инвалидация - в продакшене лучше использовать pattern matching
        logger.info(f"Invalidated cache for location: {location_id}")
