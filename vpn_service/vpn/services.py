"""
Service layer for Hiddify Manager API integration.
"""
import requests
import logging
from typing import Dict, Optional, Tuple
from django.conf import settings
from django.core.cache import cache
import json
from datetime import datetime

logger = logging.getLogger('hiddify_api')


class HiddifyApiService:
    """
    Сервис для взаимодействия с Hiddify Manager API.
    
    PURPOSE:
      - Инкапсулирует всю логику взаимодействия с Hiddify Manager
      - Обеспечивает единообразную обработку ошибок API
      - Управляет аутентификацией и кэшированием запросов
    
    AAG (Actor -> Action -> Goal):
      - Django Backend -> Вызывает методы сервиса -> Управляет VPN пользователями
      - Сервис -> Обращается к Hiddify API -> Синхронизирует состояние
    
    CONTRACT:
      PRECONDITIONS:
        - HIDDIFY_ADMIN_API_KEY настроен в settings
        - HIDDIFY_BASE_URL доступен
      POSTCONDITIONS:
        - Все методы возвращают стандартизированные результаты
        - Ошибки логируются и обрабатываются единообразно
      INVARIANTS:
        - API ключ всегда передается в заголовках
        - Таймауты настроены для всех запросов
    """
    
    def __init__(self):
        self.admin_api_key = settings.HIDDIFY_ADMIN_API_KEY
        self.admin_base_url = settings.HIDDIFY_ADMIN_BASE_URL
        self.user_base_url = settings.HIDDIFY_USER_BASE_URL
        self.timeout = getattr(settings, 'HIDDIFY_API_TIMEOUT', 10)  # Уменьшен с 30 до 10 секунд
        self.max_retries = getattr(settings, 'HIDDIFY_API_MAX_RETRIES', 3)
        self.retry_delay = getattr(settings, 'HIDDIFY_API_RETRY_DELAY', 1)  # Начальная задержка в секундах

        # Заголовки для Admin API
        self.admin_headers = {
            'Content-Type': 'application/json',
            'Hiddify-API-Key': self.admin_api_key,
        }
    
    def _make_request(self, method: str, url: str, headers: Dict = None,
                     data: Dict = None, timeout: int = None) -> Tuple[bool, Dict]:
        """
        Универсальный метод для выполнения HTTP запросов к Hiddify API с retry логикой.

        PURPOSE:
          - Стандартизирует выполнение всех HTTP запросов
          - Обеспечивает единообразную обработку ошибок
          - Логирует все взаимодействия с API с детальными замерами времени
          - Реализует retry логику с экспоненциальной задержкой

        ARGS:
          - method (str): HTTP метод (GET, POST, PUT, DELETE)
          - url (str): Полный URL для запроса
          - headers (Dict): HTTP заголовки
          - data (Dict): Данные для отправки (JSON)
          - timeout (int): Таймаут запроса в секундах

        RETURNS:
          - Tuple[bool, Dict]: (success, response_data_or_error)

        RAISES:
          - Не вызывает исключений, возвращает ошибки в response
        """
        import time

        timeout = timeout or self.timeout
        safe_url = self._mask_sensitive_url(url)

        # Retry логика с экспоненциальной задержкой
        for attempt in range(self.max_retries + 1):
            # Начинаем замер времени для каждой попытки
            start_time = time.monotonic()

            try:
                if attempt > 0:
                    delay = self.retry_delay * (2 ** (attempt - 1))  # Экспоненциальная задержка
                    logger.info(f"[RETRY] Attempt {attempt + 1}/{self.max_retries + 1} for {safe_url} after {delay}s delay")
                    time.sleep(delay)
                else:
                    logger.info(f"[TIMING] Starting {method} request to {safe_url} at {time.time():.3f}")

                # Маскируем чувствительные заголовки для логирования
                safe_headers = self._mask_sensitive_headers(headers) if headers else None
                if safe_headers and attempt == 0:  # Логируем заголовки только в первой попытке
                    logger.debug(f"Request headers: {safe_headers}")

                # Логируем данные запроса (без чувствительной информации)
                if data and attempt == 0:  # Логируем данные только в первой попытке
                    safe_data = {k: v for k, v in data.items() if k not in ['api_key', 'token', 'password']}
                    logger.debug(f"Request data: {safe_data}")

                # Замер времени до начала запроса
                pre_request_time = time.monotonic()
                if attempt == 0:
                    logger.debug(f"[TIMING] Pre-request setup took {(pre_request_time - start_time) * 1000:.2f}ms")

                response = requests.request(
                    method=method,
                    url=url,
                    headers=headers,
                    json=data,
                    timeout=timeout
                )

                # Замер времени после получения ответа
                post_request_time = time.monotonic()
                request_duration = (post_request_time - pre_request_time) * 1000
                total_duration = (post_request_time - start_time) * 1000

                logger.info(f"[TIMING] {method} request to {safe_url} completed in {request_duration:.2f}ms (total: {total_duration:.2f}ms) [attempt {attempt + 1}]")
                logger.info(f"Response status: {response.status_code}")

                if response.status_code in [200, 201]:
                    try:
                        response_data = response.json()
                        logger.debug(f"[TIMING] JSON parsing completed, total time: {total_duration:.2f}ms")
                        return True, response_data
                    except json.JSONDecodeError:
                        # Если ответ не JSON, возвращаем текст
                        logger.debug(f"[TIMING] Non-JSON response processed, total time: {total_duration:.2f}ms")
                        return True, {'content': response.text, 'headers': dict(response.headers)}
                else:
                    error_data = {
                        'status_code': response.status_code,
                        'error': response.text,
                        'url': safe_url,
                        'request_duration_ms': request_duration,
                        'total_duration_ms': total_duration,
                        'attempt': attempt + 1
                    }

                    # Для 4xx ошибок не делаем retry (проблема с данными)
                    if 400 <= response.status_code < 500:
                        logger.error(f"[TIMING] Client error {response.status_code}, no retry: {error_data}")
                        return False, error_data

                    # Для 5xx ошибок делаем retry
                    if attempt < self.max_retries:
                        logger.warning(f"[RETRY] Server error {response.status_code}, will retry: {error_data}")
                        continue
                    else:
                        logger.error(f"[TIMING] API request failed after {self.max_retries + 1} attempts: {error_data}")
                        return False, error_data

            except requests.exceptions.Timeout:
                timeout_duration = (time.monotonic() - start_time) * 1000
                error = {
                    'error': 'Request timeout',
                    'url': safe_url,
                    'timeout_after_ms': timeout_duration,
                    'configured_timeout_s': timeout,
                    'attempt': attempt + 1
                }

                if attempt < self.max_retries:
                    logger.warning(f"[RETRY] Request timeout, will retry: {error}")
                    continue
                else:
                    logger.error(f"[TIMING] Request timeout after {self.max_retries + 1} attempts: {error}")
                    return False, error

            except requests.exceptions.ConnectionError:
                error_duration = (time.monotonic() - start_time) * 1000
                error = {
                    'error': 'Connection error',
                    'url': safe_url,
                    'failed_after_ms': error_duration,
                    'attempt': attempt + 1
                }

                if attempt < self.max_retries:
                    logger.warning(f"[RETRY] Connection error, will retry: {error}")
                    continue
                else:
                    logger.error(f"[TIMING] Connection error after {self.max_retries + 1} attempts: {error}")
                    return False, error

            except Exception as e:
                error_duration = (time.monotonic() - start_time) * 1000
                error = {
                    'error': str(e),
                    'url': safe_url,
                    'failed_after_ms': error_duration,
                    'attempt': attempt + 1
                }

                if attempt < self.max_retries:
                    logger.warning(f"[RETRY] Unexpected error, will retry: {error}")
                    continue
                else:
                    logger.error(f"[TIMING] Unexpected error after {self.max_retries + 1} attempts: {error}")
                    return False, error

        # Этот код никогда не должен выполниться, но добавлен для безопасности
        return False, {'error': 'Max retries exceeded', 'url': safe_url}

    def create_hiddify_user(self, name: str, usage_limit_gb: int, 
                           package_days: int, comment_json_string: str) -> Tuple[bool, Dict]:
        """
        Создает нового пользователя в Hiddify Manager.
        
        PURPOSE:
          - Создает VPN-пользователя в Hiddify для нашего клиента
          - Устанавливает лимиты трафика и времени действия
          - Сохраняет метаданные в комментарии
        
        ARGS:
          - name (str): Имя пользователя в Hiddify (обычно наш internal ID)
          - usage_limit_gb (int): Лимит трафика в гигабайтах
          - package_days (int): Срок действия в днях
          - comment_json_string (str): JSON с метаданными нашей системы
        
        RETURNS:
          - Tuple[bool, Dict]: (success, {'uuid': hiddify_user_uuid} или error)
        """
        url = f"{self.admin_base_url}/api/v2/admin/user/"
        
        data = {
            'name': name,
            'usage_limit_GB': usage_limit_gb,
            'package_days': package_days,
            'comment': comment_json_string
        }
        
        success, response = self._make_request('POST', url, self.admin_headers, data)
        
        if success and 'uuid' in response:
            logger.info(f"Created Hiddify user: {response['uuid']}")
            return True, {'uuid': response['uuid']}
        else:
            logger.error(f"Failed to create Hiddify user: {response}")
            return False, response

    def get_hiddify_user_info(self, hiddify_user_uuid: str) -> Tuple[bool, Dict]:
        """
        Получает информацию о пользователе Hiddify.
        
        PURPOSE:
          - Синхронизирует данные пользователя между системами
          - Получает актуальную статистику использования
          - Проверяет статус пользователя в Hiddify
        
        ARGS:
          - hiddify_user_uuid (str): UUID пользователя в Hiddify
        
        RETURNS:
          - Tuple[bool, Dict]: (success, user_data или error)
        """
        # Сначала пробуем прямой GET запрос
        url = f"{self.admin_base_url}/api/v2/admin/user/{hiddify_user_uuid}/"
        success, response = self._make_request('GET', url, self.admin_headers)
        
        if success:
            return True, response
        
        # Если прямой запрос не работает, используем список пользователей
        logger.warning(f"Direct GET failed, trying list approach for user {hiddify_user_uuid}")
        
        list_url = f"{self.admin_base_url}/api/v2/admin/user/"
        success, users_response = self._make_request('GET', list_url, self.admin_headers)
        
        if success and isinstance(users_response, list):
            for user in users_response:
                if user.get('uuid') == hiddify_user_uuid:
                    return True, user
            
            return False, {'error': 'User not found in list'}
        
        return False, response

    def update_hiddify_user(self, hiddify_user_uuid: str, data_to_update: Dict) -> Tuple[bool, Dict]:
        """
        Обновляет данные пользователя в Hiddify Manager.
        
        PURPOSE:
          - Синхронизирует изменения тарифов и лимитов
          - Обновляет метаданные пользователя
          - Изменяет статус активности пользователя
        
        ARGS:
          - hiddify_user_uuid (str): UUID пользователя в Hiddify
          - data_to_update (Dict): Данные для обновления
        
        RETURNS:
          - Tuple[bool, Dict]: (success, updated_data или error)
        """
        url = f"{self.admin_base_url}/api/v2/admin/user/{hiddify_user_uuid}/"
        
        success, response = self._make_request('PUT', url, self.admin_headers, data_to_update)
        
        if success:
            logger.info(f"Updated Hiddify user {hiddify_user_uuid}")
        else:
            logger.error(f"Failed to update Hiddify user {hiddify_user_uuid}: {response}")
        
        return success, response

    def delete_hiddify_user(self, hiddify_user_uuid: str) -> Tuple[bool, Dict]:
        """
        Удаляет пользователя из Hiddify Manager.
        
        PURPOSE:
          - Удаляет VPN-доступ при отмене подписки
          - Очищает ресурсы в Hiddify Manager
          - Обеспечивает синхронизацию при удалении аккаунта
        
        ARGS:
          - hiddify_user_uuid (str): UUID пользователя в Hiddify
        
        RETURNS:
          - Tuple[bool, Dict]: (success, response или error)
        """
        url = f"{self.admin_base_url}/api/v2/admin/user/{hiddify_user_uuid}/"
        
        success, response = self._make_request('DELETE', url, self.admin_headers)
        
        if success:
            logger.info(f"Deleted Hiddify user {hiddify_user_uuid}")
        else:
            logger.error(f"Failed to delete Hiddify user {hiddify_user_uuid}: {response}")
        
        return success, response

    def get_singbox_config_for_user(self, hiddify_user_uuid: str) -> Tuple[bool, Dict]:
        """
        Получает SingBox конфигурацию для пользователя.

        PURPOSE:
          - Предоставляет JSON конфигурацию для SingBox клиентов
          - Извлекает информацию о трафике из заголовков ответа
          - Кэширует конфигурации для оптимизации производительности

        ARGS:
          - hiddify_user_uuid (str): UUID пользователя в Hiddify

        RETURNS:
          - Tuple[bool, Dict]: (success, {'config': json_config, 'traffic_info': dict} или error)
        """
        # Проверяем кэш
        cache_key = f"singbox_config_{hiddify_user_uuid}"
        cached_config = cache.get(cache_key)
        if cached_config:
            logger.info(f"Returning cached SingBox config for {hiddify_user_uuid}")
            return True, cached_config

        url = f"{self.user_base_url}/{hiddify_user_uuid}/singbox/"

        # ДИАГНОСТИКА: Детальное логирование запроса конфигурации
        logger.info(f"[CONFIG_DIAG] Requesting SingBox config for UUID: {hiddify_user_uuid}")
        logger.info(f"[CONFIG_DIAG] Full URL: {url}")
        logger.info(f"[CONFIG_DIAG] User base URL: {self.user_base_url}")
        logger.info(f"[CONFIG_DIAG] No authentication headers for user endpoints (by design)")

        success, response = self._make_request('GET', url)

        # ДИАГНОСТИКА: Детальное логирование ответа
        logger.info(f"[CONFIG_DIAG] Request success: {success}")
        logger.info(f"[CONFIG_DIAG] Response type: {type(response)}")
        logger.info(f"[CONFIG_DIAG] Response keys: {list(response.keys()) if isinstance(response, dict) else 'Not a dict'}")

        if 'headers' in response:
            logger.info(f"[CONFIG_DIAG] Response headers: {response['headers']}")

        if 'content' in response:
            content_preview = response['content'][:200] if len(response['content']) > 200 else response['content']
            logger.info(f"[CONFIG_DIAG] Content preview: {content_preview}")
            logger.info(f"[CONFIG_DIAG] Content type detected: {'HTML' if '<html' in response['content'].lower() else 'JSON/Other'}")

        if success:
            # Извлекаем информацию о трафике из заголовков
            traffic_info = self._extract_traffic_info(response.get('headers', {}))
            logger.info(f"[CONFIG_DIAG] Extracted traffic info: {traffic_info}")

            # Правильная обработка ответа от Hiddify API
            config_data = None
            if 'content' in response:
                # Ответ пришел как текст, пытаемся парсить JSON
                try:
                    config_data = json.loads(response['content'])
                    logger.info(f"[CONFIG_DIAG] Successfully parsed SingBox config JSON for {hiddify_user_uuid}")
                except json.JSONDecodeError as e:
                    logger.error(f"[CONFIG_DIAG] Failed to parse SingBox config as JSON for {hiddify_user_uuid}: {e}")
                    logger.error(f"[CONFIG_DIAG] Raw content: {response['content'][:500]}...")

                    # КРИТИЧЕСКАЯ ДИАГНОСТИКА: Анализируем HTML ответ
                    if '<html' in response['content'].lower():
                        logger.error(f"[CONFIG_DIAG] CRITICAL: Received HTML page instead of JSON config!")
                        logger.error(f"[CONFIG_DIAG] This indicates authentication/authorization failure with Hiddify")
                        logger.error(f"[CONFIG_DIAG] User UUID {hiddify_user_uuid} may not exist or be inactive in Hiddify")

                        # Ищем специфические индикаторы в HTML
                        if 'login' in response['content'].lower():
                            logger.error(f"[CONFIG_DIAG] HTML contains 'login' - this is a login page!")
                        if 'secret code' in response['content'].lower():
                            logger.error(f"[CONFIG_DIAG] HTML contains 'secret code' - authentication required!")

                    config_data = response['content']  # Возвращаем как есть для дальнейшего анализа
            else:
                # Ответ уже пришел как JSON объект
                config_data = response
                logger.info(f"[CONFIG_DIAG] Received SingBox config as JSON object for {hiddify_user_uuid}")

            result = {
                'config': config_data,
                'traffic_info': traffic_info
            }

            # НЕ кэшируем HTML ответы
            if not (isinstance(config_data, str) and '<html' in config_data.lower()):
                cache.set(cache_key, result, 300)
                logger.info(f"[CONFIG_DIAG] Cached valid config for {hiddify_user_uuid}")
            else:
                logger.warning(f"[CONFIG_DIAG] NOT caching HTML response for {hiddify_user_uuid}")

            return True, result
        else:
            logger.error(f"[CONFIG_DIAG] Request failed: {response}")
            return False, response

    # TODO (Этап 3): Восстановить метод get_subscription_link_for_user для поддержки Subscription ссылок
    # def get_subscription_link_for_user(self, hiddify_user_uuid: str) -> Tuple[bool, Dict]:
    #     """
    #     Получает универсальную подписку для пользователя.
    #
    #     PURPOSE:
    #       - Предоставляет список ссылок различных VPN протоколов
    #       - Поддерживает импорт в различные VPN клиенты
    #       - Извлекает статистику использования трафика
    #
    #     ARGS:
    #       - hiddify_user_uuid (str): UUID пользователя в Hiddify
    #
    #     RETURNS:
    #       - Tuple[bool, Dict]: (success, {'links': str, 'traffic_info': dict} или error)
    #     """
    #     url = f"{self.user_base_url}/{hiddify_user_uuid}/sub/"
    #
    #     success, response = self._make_request('GET', url)
    #
    #     if success:
    #         traffic_info = self._extract_traffic_info(response.get('headers', {}))
    #
    #         # Правильная обработка ответа от Hiddify API для subscription ссылок
    #         links_data = response.get('content') if 'content' in response else response
    #         logger.info(f"Received subscription links for {hiddify_user_uuid}")
    #
    #         return True, {
    #             'links': links_data,
    #             'traffic_info': traffic_info
    #         }
    #
    #     return False, response

    # TODO (Этап 2): Восстановить метод get_clash_config_for_user для поддержки Clash конфигураций
    # def get_clash_config_for_user(self, hiddify_user_uuid: str) -> Tuple[bool, Dict]:
    #     """
    #     Получает Clash конфигурацию для пользователя.
    #
    #     PURPOSE:
    #       - Предоставляет YAML конфигурацию для Clash клиентов
    #       - Включает правила маршрутизации и proxy groups
    #       - Извлекает информацию о трафике
    #
    #     ARGS:
    #       - hiddify_user_uuid (str): UUID пользователя в Hiddify
    #
    #     RETURNS:
    #       - Tuple[bool, Dict]: (success, {'config': yaml_config, 'traffic_info': dict} или error)
    #     """
    #     url = f"{self.user_base_url}/{hiddify_user_uuid}/clash/"
    #
    #     success, response = self._make_request('GET', url)
    #
    #     if success:
    #         traffic_info = self._extract_traffic_info(response.get('headers', {}))
    #
    #         # Правильная обработка ответа от Hiddify API для Clash конфигурации
    #         config_data = response.get('content') if 'content' in response else response
    #         logger.info(f"Received Clash config for {hiddify_user_uuid}")
    #
    #         return True, {
    #             'config': config_data,
    #             'traffic_info': traffic_info
    #         }
    #
    #     return False, response

    def _extract_traffic_info(self, headers: Dict) -> Dict:
        """
        Извлекает информацию о трафике из заголовков ответа Hiddify.

        PURPOSE:
          - Парсит заголовок subscription-userinfo
          - Конвертирует данные в удобный формат
          - Обеспечивает fallback значения при отсутствии данных

        ARGS:
          - headers (Dict): HTTP заголовки ответа от Hiddify

        RETURNS:
          - Dict: Информация о трафике (upload, download, total, expire)
        """
        traffic_info = {
            'upload_bytes': 0,
            'download_bytes': 0,
            'total_bytes': 0,
            'expire_timestamp': None
        }

        # Пример заголовка: subscription-userinfo: upload=0;download=18751746;total=3221225472000;expire=2064096000
        userinfo = headers.get('subscription-userinfo', '')

        if userinfo:
            try:
                parts = userinfo.split(';')
                for part in parts:
                    if '=' in part:
                        key, value = part.split('=', 1)
                        key = key.strip()
                        value = value.strip()

                        if key == 'upload':
                            traffic_info['upload_bytes'] = int(value)
                        elif key == 'download':
                            traffic_info['download_bytes'] = int(value)
                        elif key == 'total':
                            traffic_info['total_bytes'] = int(value)
                        elif key == 'expire':
                            traffic_info['expire_timestamp'] = int(value)

            except (ValueError, AttributeError) as e:
                logger.warning(f"Failed to parse traffic info: {userinfo}, error: {e}")

        return traffic_info

    def _mask_sensitive_url(self, url: str) -> str:
        """
        Маскирует чувствительные данные в URL для безопасного логирования.

        PURPOSE:
          - Скрывает API ключи и секретные пути в URL
          - Обеспечивает безопасность логирования в production
          - Сохраняет полезную информацию для отладки

        ARGS:
          - url (str): Исходный URL

        RETURNS:
          - str: URL с замаскированными чувствительными данными
        """
        import re

        # Маскируем секретные пути Hiddify
        masked_url = url

        # Маскируем admin path (любой UUID)
        masked_url = re.sub(r'/[a-zA-Z0-9]{16,32}/', '/***ADMIN_PATH***/', masked_url)

        # Маскируем user path (любой UUID)
        masked_url = re.sub(r'/[a-zA-Z0-9]{26,32}/', '/***USER_PATH***/', masked_url)

        # Маскируем UUID в URL (но оставляем последние 4 символа для отладки)
        masked_url = re.sub(
            r'/([a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{8})([a-f0-9]{4})/',
            r'/***UUID***-\2/',
            masked_url
        )

        return masked_url

    def _mask_sensitive_headers(self, headers: Dict) -> Dict:
        """
        Маскирует чувствительные заголовки для безопасного логирования.

        PURPOSE:
          - Скрывает API ключи в заголовках
          - Обеспечивает безопасность логирования в production
          - Сохраняет структуру заголовков для отладки

        ARGS:
          - headers (Dict): Исходные заголовки

        RETURNS:
          - Dict: Заголовки с замаскированными чувствительными данными
        """
        if not headers:
            return {}

        safe_headers = {}

        for key, value in headers.items():
            key_lower = key.lower()

            if 'api-key' in key_lower or 'authorization' in key_lower or 'token' in key_lower:
                # Маскируем API ключи, оставляя только первые и последние 4 символа
                if isinstance(value, str) and len(value) > 8:
                    safe_headers[key] = f"{value[:4]}***{value[-4:]}"
                else:
                    safe_headers[key] = "***MASKED***"
            else:
                safe_headers[key] = value

        return safe_headers


class SingBoxConfigService:
    """
    Сервис для генерации SingBox конфигураций с поддержкой локаций.

    PURPOSE:
      - Генерирует динамические SingBox конфигурации на основе выбранной локации
      - Подставляет параметры сервера из hiddify_params локации
      - Обеспечивает консистентность конфигураций между локациями
      - Абстрагирует логику генерации конфигураций от view слоя

    AAG (Actor -> Action -> Goal):
      - VPN View -> Вызывает сервис -> Получает готовую SingBox конфигурацию
      - Сервис -> Комбинирует базовый шаблон с параметрами локации -> Возвращает валидную конфигурацию
      - Пользователь -> Получает конфигурацию -> Подключается к выбранному серверу

    CONTRACT:
      PRECONDITIONS:
        - location (Location): Активная локация с валидными hiddify_params
        - hiddify_user_uuid (str): UUID пользователя в Hiddify Manager
        - Базовый шаблон SingBox конфигурации доступен
      POSTCONDITIONS:
        - Возвращается валидная SingBox JSON конфигурация
        - Параметры сервера соответствуют выбранной локации
        - UUID пользователя корректно подставлен в outbound
      INVARIANTS:
        - Структура конфигурации соответствует спецификации SingBox
        - Все обязательные поля присутствуют в конфигурации

    NOTES:
      - Использует базовый шаблон конфигурации и подставляет параметры локации
      - Поддерживает различные типы VPN протоколов через hiddify_params
      - Может быть расширен для поддержки дополнительных параметров конфигурации
    """

    @staticmethod
    def generate_singbox_config(location, hiddify_user_uuid: str) -> dict:
        """
        Генерирует SingBox конфигурацию для указанной локации и пользователя.

        PURPOSE:
          - Создает готовую к использованию SingBox конфигурацию
          - Подставляет параметры сервера из локации
          - Интегрирует UUID пользователя для аутентификации

        ARGS:
          - location (Location): Объект локации с hiddify_params
          - hiddify_user_uuid (str): UUID пользователя в Hiddify

        RETURNS:
          - dict: Готовая SingBox JSON конфигурация

        RAISES:
          - ValueError: Если в hiddify_params отсутствуют обязательные параметры
        """
        # Проверяем обязательные параметры в локации
        required_params = ['server', 'server_port']
        missing_params = [param for param in required_params if param not in location.hiddify_params]
        if missing_params:
            raise ValueError(f"Location {location.name} missing required hiddify_params: {', '.join(missing_params)}")

        # Извлекаем параметры сервера из локации
        server_params = location.hiddify_params.copy()

        # Базовый шаблон SingBox конфигурации
        config = {
            "log": {
                "level": "warn",
                "timestamp": True
            },
            "dns": {
                "servers": [
                    {
                        "tag": "google",
                        "address": "*******"
                    },
                    {
                        "tag": "cloudflare",
                        "address": "*******"
                    }
                ],
                "rules": [
                    {
                        "outbound": "any",
                        "server": "google"
                    }
                ]
            },
            "inbounds": [
                {
                    "type": "tun",
                    "tag": "tun-in",
                    "interface_name": "tun0",
                    "inet4_address": "**********/30",
                    "auto_route": True,
                    "strict_route": False,
                    "sniff": True
                }
            ],
            "outbounds": [
                {
                    "type": server_params.get("type", "vmess"),
                    "tag": "proxy",
                    "server": server_params["server"],
                    "server_port": server_params["server_port"],
                    "uuid": hiddify_user_uuid,
                    "security": server_params.get("security", "auto"),
                    "alter_id": server_params.get("alter_id", 0)
                },
                {
                    "type": "direct",
                    "tag": "direct"
                },
                {
                    "type": "block",
                    "tag": "block"
                }
            ],
            "route": {
                "rules": [
                    {
                        "protocol": "dns",
                        "outbound": "dns-out"
                    },
                    {
                        "ip_is_private": True,
                        "outbound": "direct"
                    }
                ],
                "final": "proxy",
                "auto_detect_interface": True
            }
        }

        # Добавляем дополнительные параметры TLS если они есть
        if "tls_server_name" in server_params:
            config["outbounds"][0]["tls"] = {
                "enabled": True,
                "server_name": server_params["tls_server_name"],
                "insecure": server_params.get("tls_insecure", False)
            }

        # Добавляем transport параметры если они есть
        if "transport" in server_params:
            config["outbounds"][0]["transport"] = server_params["transport"]

        # Добавляем дополнительные параметры протокола
        for param in ["method", "password", "network", "path", "host"]:
            if param in server_params:
                config["outbounds"][0][param] = server_params[param]

        logger.info(f"Generated SingBox config for location {location.name} and user {hiddify_user_uuid}")
        return config
