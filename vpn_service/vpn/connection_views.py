"""
Views for VPN connection status tracking.
"""
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from drf_spectacular.utils import extend_schema, OpenApiExample
from django.utils import timezone
from datetime import timedelta
import logging

from .models import ConnectionLog
from .serializers import (
    ConnectionStatusSerializer, 
    ConnectionStatusResponseSerializer,
    VpnErrorResponseSerializer
)

logger = logging.getLogger(__name__)


@extend_schema(
    tags=['VPN'],
    summary='Report connection status',
    description='Report VPN connection status',
    request=ConnectionStatusSerializer,
    responses={
        200: ConnectionStatusResponseSerializer,
        400: VpnErrorResponseSerializer,
        401: VpnErrorResponseSerializer,
    },
    examples=[
        OpenApiExample(
            'Connection established',
            summary='Уведомление о подключении',
            description='Пример уведомления при подключении к VPN серверу',
            value={
                "status": "connected",
                "device_id": "my-device-001",
                "server_id": "vpn-de1",
                "timestamp": "2025-06-01T15:30:00Z",
                "client_ip": "*************",
                "user_agent": "SingBox/1.8.0"
            },
            request_only=True,
        ),
        OpenApiExample(
            'Connection terminated',
            summary='Уведомление об отключении',
            description='Пример уведомления при отключении от VPN сервера',
            value={
                "status": "disconnected",
                "device_id": "my-device-001",
                "server_id": "vpn-de1",
                "timestamp": "2025-06-01T16:45:00Z",
                "client_ip": "*************",
                "session_duration": 4500,
                "user_agent": "SingBox/1.8.0"
            },
            request_only=True,
        ),
        OpenApiExample(
            'Success response',
            summary='Успешный ответ',
            description='Ответ при успешной обработке уведомления',
            value={
                "success": True,
                "message": "Connection status logged successfully",
                "log_id": "123e4567-e89b-12d3-a456-426614174000",
                "timestamp": "2025-06-01T15:30:05Z"
            },
            response_only=True,
        ),
    ]
)
@api_view(['POST'])
@permission_classes([IsAuthenticated])
def connection_status(request):
    """
    Уведомление о подключении/отключении VPN клиента.
    
    PURPOSE:
      - Получает уведомления от VPN клиентов о статусе подключения
      - Сохраняет информацию о подключениях в базу данных
      - Обеспечивает аудит и аналитику использования VPN
    
    AAG (Actor -> Action -> Goal):
      - VPN клиент -> Отправляет статус подключения -> Система логирует активность
      - Администратор -> Анализирует логи -> Мониторит использование VPN
    
    CONTRACT:
      PRECONDITIONS:
        - Пользователь аутентифицирован (JWT токен)
        - Данные соответствуют схеме ConnectionStatusSerializer
      POSTCONDITIONS:
        - Создается запись в ConnectionLog
        - Возвращается подтверждение с ID записи
      INVARIANTS:
        - Все подключения логируются
        - Timestamp события сохраняется точно
    
    ARGS:
      - request.data (dict): Данные о статусе подключения
        - status (str): 'connected' или 'disconnected'
        - device_id (str): ID устройства пользователя
        - server_id (str): ID VPN сервера
        - timestamp (str): Время события в ISO формате
        - client_ip (str): IP адрес клиента
        - user_agent (str, optional): User-Agent клиента
        - session_duration (int, optional): Длительность сессии в секундах
    
    RETURNS:
      - Response: JSON с результатом обработки
        - success (bool): Успешность операции
        - message (str): Описание результата
        - log_id (str): UUID созданной записи
        - timestamp (str): Время обработки
    
    RAISES:
      - 400 Bad Request: Некорректные данные
      - 401 Unauthorized: Пользователь не аутентифицирован
      - 500 Internal Server Error: Ошибка сервера
    """
    try:
        # Шаг 1: Валидация входных данных
        serializer = ConnectionStatusSerializer(data=request.data)
        if not serializer.is_valid():
            logger.warning(f"Invalid connection status data from user {request.user.id}: {serializer.errors}")
            return Response({
                'success': False,
                'error': 'Invalid data format',
                'details': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)
        
        validated_data = serializer.validated_data
        
        # Шаг 2: Получение дополнительной информации
        user_agent = validated_data.get('user_agent', request.META.get('HTTP_USER_AGENT', ''))
        session_duration_seconds = validated_data.get('session_duration')
        session_duration = None
        
        if session_duration_seconds is not None:
            session_duration = timedelta(seconds=session_duration_seconds)
        
        # Шаг 3: Создание записи в логе
        connection_log = ConnectionLog.objects.create(
            user=request.user,
            status=validated_data['status'],
            device_id=validated_data['device_id'],
            server_id=validated_data['server_id'],
            client_ip=validated_data['client_ip'],
            timestamp=validated_data['timestamp'],
            user_agent=user_agent,
            session_duration=session_duration
        )
        
        # Шаг 4: Логирование для мониторинга
        logger.info(
            f"Connection status logged: user={request.user.email}, "
            f"status={validated_data['status']}, "
            f"device={validated_data['device_id']}, "
            f"server={validated_data['server_id']}, "
            f"log_id={connection_log.id}"
        )
        
        # Шаг 5: Формирование ответа
        response_data = {
            'success': True,
            'message': 'Connection status logged successfully',
            'log_id': str(connection_log.id),
            'timestamp': timezone.now().isoformat()
        }
        
        return Response(response_data, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Failed to log connection status for user {request.user.id}: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to process connection status',
            'message': 'Internal server error occurred'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@extend_schema(
    tags=['VPN'],
    summary='Get connection logs',
    description='Get user connection history',
    responses={
        200: ConnectionStatusResponseSerializer,
        401: VpnErrorResponseSerializer,
    }
)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_connection_logs(request):
    """
    Получение истории подключений пользователя.
    
    PURPOSE:
      - Предоставляет историю подключений для отладки
      - Позволяет пользователю видеть свою активность
      - Поддерживает аналитику использования VPN
    """
    try:
        # Получаем последние 50 записей пользователя
        logs = ConnectionLog.objects.filter(user=request.user)[:50]
        
        logs_data = []
        for log in logs:
            logs_data.append({
                'id': str(log.id),
                'status': log.status,
                'device_id': log.device_id,
                'server_id': log.server_id,
                'client_ip': log.client_ip,
                'timestamp': log.timestamp.isoformat(),
                'session_duration': log.session_duration.total_seconds() if log.session_duration else None,
                'created_at': log.created_at.isoformat()
            })
        
        return Response({
            'success': True,
            'logs': logs_data,
            'total_count': len(logs_data)
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Failed to get connection logs for user {request.user.id}: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to retrieve connection logs'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
