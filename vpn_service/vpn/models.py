"""
VPN app models for connection tracking and server management.
"""
from django.db import models
from django.contrib.auth import get_user_model
import uuid

User = get_user_model()


class VPNServer(models.Model):
    """
    Модель VPN серверов для управления конфигурациями и мониторинга.
    
    PURPOSE:
      - Хранит информацию о доступных VPN серверах
      - Обеспечивает централизованное управление серверной инфраструктурой
      - Поддерживает мониторинг состояния серверов
    
    AAG (Actor -> Action -> Goal):
      - Система -> Регистрирует серверы -> Управляет VPN инфраструктурой
      - Мониторинг -> Проверяет серверы -> Обеспечивает доступность
    
    CONTRACT:
      PRECONDITIONS:
        - server_id (str): Уникальный идентификатор сервера
        - name (str): Человекочитаемое имя сервера
        - host (str): Хост или IP адрес сервера
      POSTCONDITIONS:
        - Создается запись сервера в системе
        - Сервер доступен для назначения пользователям
      INVARIANTS:
        - server_id всегда уникален
        - is_active определяет доступность сервера
    """
    server_id = models.CharField(max_length=50, primary_key=True, help_text="Уникальный ID сервера (например: vpn-de1)")
    name = models.CharField(max_length=100, help_text="Название сервера (например: Germany Frankfurt)")
    host = models.CharField(max_length=255, help_text="Хост сервера (например: vpn-de1.ductuspro.ru)")
    location = models.CharField(max_length=100, blank=True, help_text="Географическое расположение")
    management_api_url = models.URLField(blank=True, help_text="URL API управления сервером")
    is_active = models.BooleanField(default=True, help_text="Активен ли сервер")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'vpn_servers'
        indexes = [
            models.Index(fields=['is_active']),
            models.Index(fields=['location']),
        ]

    def __str__(self):
        return f"{self.server_id} ({self.name})"


class ConnectionLog(models.Model):
    """
    Лог подключений пользователей к VPN серверам.
    
    PURPOSE:
      - Отслеживает активность пользователей VPN
      - Предоставляет данные для аналитики и мониторинга
      - Обеспечивает аудит подключений для безопасности
    
    AAG (Actor -> Action -> Goal):
      - VPN клиент -> Уведомляет о подключении -> Система логирует активность
      - Администратор -> Анализирует логи -> Мониторит использование VPN
    
    CONTRACT:
      PRECONDITIONS:
        - user (User): Аутентифицированный пользователь
        - status (str): Статус подключения (connected/disconnected)
        - device_id (str): Идентификатор устройства пользователя
      POSTCONDITIONS:
        - Создается запись о подключении/отключении
        - Данные доступны для аналитики
      INVARIANTS:
        - timestamp отражает время события
        - status может быть только 'connected' или 'disconnected'
    """
    
    STATUS_CHOICES = [
        ('connected', 'Connected'),
        ('disconnected', 'Disconnected'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='connection_logs')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES)
    device_id = models.CharField(max_length=255, help_text="ID устройства пользователя")
    server_id = models.CharField(max_length=50, help_text="ID VPN сервера")
    client_ip = models.GenericIPAddressField(help_text="IP адрес клиента")
    timestamp = models.DateTimeField(help_text="Время события подключения/отключения")
    created_at = models.DateTimeField(auto_now_add=True)
    
    # Дополнительные поля для аналитики
    user_agent = models.TextField(blank=True, help_text="User-Agent клиента")
    session_duration = models.DurationField(null=True, blank=True, help_text="Длительность сессии (для disconnected)")

    class Meta:
        db_table = 'vpn_connection_logs'
        indexes = [
            models.Index(fields=['user', 'timestamp']),
            models.Index(fields=['status', 'timestamp']),
            models.Index(fields=['server_id', 'timestamp']),
            models.Index(fields=['device_id']),
        ]
        ordering = ['-timestamp']

    def __str__(self):
        return f"{self.user.email} {self.status} {self.server_id} at {self.timestamp}"


class Location(models.Model):
    """
    Модель VPN-локаций для выбора серверов пользователями.

    PURPOSE:
      - Хранит информацию о доступных VPN-серверах в разных локациях
      - Связывает географические локации с техническими параметрами Hiddify
      - Обеспечивает пользователям выбор оптимального сервера
      - Управляет доступностью локаций через флаг активности

    AAG (Actor -> Action -> Goal):
      - Пользователь -> Выбирает локацию -> Подключается к оптимальному серверу
      - Система -> Генерирует конфигурацию -> Использует параметры выбранной локации
      - Администратор -> Управляет локациями -> Контролирует доступные серверы

    CONTRACT:
      PRECONDITIONS:
        - name (str): Человекочитаемое название локации (до 100 символов)
        - country_code (str): ISO код страны (2 символа)
        - hiddify_params (dict): JSON с параметрами для генерации SingBox outbound
      POSTCONDITIONS:
        - Создается локация, доступная для привязки к тарифным планам
        - hiddify_params содержит все необходимые параметры для SingBox
      INVARIANTS:
        - country_code всегда в верхнем регистре (2 символа)
        - hiddify_params содержит как минимум server и server_port
        - is_active определяет доступность локации для использования

    NOTES:
      - hiddify_params должен содержать параметры для SingBox outbound:
        {"server": "nl.vpn.com", "server_port": 443, "tls_server_name": "nl.vpn.com"}
      - flag_emoji используется для UI отображения
      - city может быть пустым для страновых локаций
    """
    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
        help_text="Уникальный идентификатор локации"
    )
    name = models.CharField(
        max_length=100,
        help_text="Название локации (например, 'Netherlands - Amsterdam')"
    )
    country_code = models.CharField(
        max_length=2,
        help_text="ISO код страны (например, 'NL')"
    )
    city = models.CharField(
        max_length=100,
        blank=True,
        help_text="Город (опционально)"
    )
    flag_emoji = models.CharField(
        max_length=10,
        blank=True,
        help_text="Эмодзи флага страны (например, '🇳🇱')"
    )
    is_active = models.BooleanField(
        default=True,
        help_text="Активна ли локация для использования"
    )
    hiddify_params = models.JSONField(
        default=dict,
        help_text="Параметры для генерации SingBox outbound (server, server_port, etc.)"
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        help_text="Время создания локации"
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        help_text="Время последнего обновления"
    )

    class Meta:
        db_table = 'vpn_locations'
        verbose_name = 'VPN Location'
        verbose_name_plural = 'VPN Locations'
        indexes = [
            models.Index(fields=['country_code']),
            models.Index(fields=['is_active']),
            models.Index(fields=['country_code', 'is_active']),
        ]
        ordering = ['country_code', 'city', 'name']

    def __str__(self):
        return f"{self.name} ({self.country_code})"

    def clean(self):
        """Валидация модели."""
        from django.core.exceptions import ValidationError

        # Приводим country_code к верхнему регистру
        if self.country_code:
            self.country_code = self.country_code.upper()

        # Проверяем обязательные параметры в hiddify_params
        if not isinstance(self.hiddify_params, dict):
            raise ValidationError("hiddify_params должен быть JSON объектом")

        required_params = ['server', 'server_port']
        missing_params = [param for param in required_params if param not in self.hiddify_params]
        if missing_params:
            raise ValidationError(f"hiddify_params должен содержать: {', '.join(missing_params)}")

    def save(self, *args, **kwargs):
        """Переопределяем save для вызова clean()."""
        self.clean()
        super().save(*args, **kwargs)


class SubscriptionPlanLocation(models.Model):
    """
    Промежуточная модель для связи тарифных планов с доступными локациями.

    PURPOSE:
      - Определяет, какие локации доступны для каждого тарифного плана
      - Позволяет настраивать дефолтную локацию для плана
      - Обеспечивает гибкое управление доступом к серверам по тарифам
      - Поддерживает дифференциацию сервиса по уровням подписки

    AAG (Actor -> Action -> Goal):
      - Администратор -> Привязывает локации к планам -> Настраивает доступ по тарифам
      - Система -> Проверяет доступность локации -> Контролирует права пользователя
      - Пользователь -> Выбирает из доступных локаций -> Получает сервис согласно тарифу

    CONTRACT:
      PRECONDITIONS:
        - plan (SubscriptionPlan): Существующий активный тарифный план
        - location (Location): Существующая активная локация
        - Уникальная комбинация plan + location
      POSTCONDITIONS:
        - Создается связь между планом и локацией
        - Локация становится доступной для пользователей этого плана
        - При is_default=True локация используется по умолчанию
      INVARIANTS:
        - Только одна локация может быть дефолтной для плана
        - Связь существует только для активных планов и локаций

    NOTES:
      - is_default используется когда пользователь не указывает location_id
      - Если дефолтной локации нет, используется первая доступная
      - Рекомендуется всегда иметь хотя бы одну дефолтную локацию для плана
    """
    plan = models.ForeignKey(
        'subscriptions.SubscriptionPlan',
        on_delete=models.CASCADE,
        related_name='plan_locations',
        help_text="Тарифный план"
    )
    location = models.ForeignKey(
        Location,
        on_delete=models.CASCADE,
        related_name='location_plans',
        help_text="VPN локация"
    )
    is_default = models.BooleanField(
        default=False,
        help_text="Локация по умолчанию для этого плана"
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        help_text="Время создания связи"
    )

    class Meta:
        db_table = 'subscription_plan_locations'
        verbose_name = 'Plan Location'
        verbose_name_plural = 'Plan Locations'
        unique_together = ('plan', 'location')
        indexes = [
            models.Index(fields=['plan', 'is_default']),
            models.Index(fields=['location', 'plan']),
        ]

    def __str__(self):
        default_mark = " (default)" if self.is_default else ""
        return f"{self.plan.name} -> {self.location.name}{default_mark}"

    def clean(self):
        """Валидация модели."""
        from django.core.exceptions import ValidationError

        # Проверяем, что план и локация активны
        if self.plan and not self.plan.is_active:
            raise ValidationError("Нельзя привязать неактивный тарифный план")

        if self.location and not self.location.is_active:
            raise ValidationError("Нельзя привязать неактивную локацию")

    def save(self, *args, **kwargs):
        """Переопределяем save для обеспечения единственности дефолтной локации."""
        self.clean()

        # Если устанавливаем эту локацию как дефолтную, убираем флаг у других
        if self.is_default:
            SubscriptionPlanLocation.objects.filter(
                plan=self.plan,
                is_default=True
            ).exclude(pk=self.pk).update(is_default=False)

        super().save(*args, **kwargs)
