"""
Serializers for VPN app - Stage 1 Focus: SingBox Only
"""
from rest_framework import serializers
from drf_spectacular.utils import extend_schema_serializer, OpenApiExample


class VPNConfigResponseSerializer(serializers.Serializer):
    """Serializer for SingBox VPN configuration response (Stage 1 Focus)."""
    success = serializers.BooleanField()
    config_type = serializers.CharField(help_text="Always 'singbox' in Stage 1")
    config = serializers.JSONField(help_text="SingBox JSON configuration")
    subscription_info = serializers.DictField()
    # TODO (Этап 3): Восстановить поле links для subscription типа
    # links = serializers.CharField(required=False, help_text="For subscription type only")


class TrafficStatsSerializer(serializers.Serializer):
    """Serializer for traffic statistics."""
    upload_bytes = serializers.IntegerField()
    download_bytes = serializers.IntegerField()
    total_used_bytes = serializers.IntegerField()
    total_used_gb = serializers.FloatField()
    limit_gb = serializers.IntegerField()
    limit_bytes = serializers.IntegerField()
    last_sync = serializers.DateTimeField(allow_null=True)


class VpnErrorResponseSerializer(serializers.Serializer):
    """Serializer for VPN error response documentation."""
    error = serializers.CharField()


class ConnectionStatusSerializer(serializers.Serializer):
    """
    Сериализатор для валидации данных о статусе подключения VPN.

    PURPOSE:
      - Валидирует входящие данные от VPN клиентов
      - Обеспечивает корректность формата уведомлений
      - Стандартизирует структуру данных о подключениях

    AAG (Actor -> Action -> Goal):
      - VPN клиент -> Отправляет статус -> Система валидирует данные
      - API -> Проверяет формат -> Сохраняет в базу данных

    CONTRACT:
      PRECONDITIONS:
        - status (str): Должен быть 'connected' или 'disconnected'
        - device_id (str): Непустой идентификатор устройства
        - server_id (str): Идентификатор VPN сервера
        - timestamp (str): ISO формат даты/времени
        - client_ip (str): Валидный IP адрес
      POSTCONDITIONS:
        - Возвращает валидированные данные
        - timestamp преобразован в datetime объект
      INVARIANTS:
        - Все обязательные поля присутствуют
        - Форматы данных соответствуют ожиданиям
    """

    STATUS_CHOICES = [
        ('connected', 'Connected'),
        ('disconnected', 'Disconnected'),
    ]

    status = serializers.ChoiceField(
        choices=STATUS_CHOICES,
        help_text="Статус подключения: 'connected' или 'disconnected'"
    )
    device_id = serializers.CharField(
        max_length=255,
        help_text="Идентификатор устройства пользователя"
    )
    server_id = serializers.CharField(
        max_length=50,
        help_text="Идентификатор VPN сервера (например: vpn-de1)"
    )
    timestamp = serializers.DateTimeField(
        help_text="Время события в ISO формате (например: 2025-06-01T15:30:00Z)"
    )
    client_ip = serializers.IPAddressField(
        help_text="IP адрес клиента"
    )

    # Опциональные поля
    user_agent = serializers.CharField(
        max_length=500,
        required=False,
        allow_blank=True,
        help_text="User-Agent строка клиента (опционально)"
    )
    session_duration = serializers.IntegerField(
        required=False,
        allow_null=True,
        help_text="Длительность сессии в секундах (только для disconnected)"
    )

    def validate_timestamp(self, value):
        """
        Валидация timestamp - не должен быть в будущем.
        """
        from django.utils import timezone
        if value > timezone.now():
            raise serializers.ValidationError("Timestamp не может быть в будущем")
        return value

    def validate(self, data):
        """
        Кросс-валидация полей.
        """
        # Для disconnected статуса может быть указана длительность сессии
        if data['status'] == 'disconnected' and 'session_duration' in data:
            if data['session_duration'] is not None and data['session_duration'] < 0:
                raise serializers.ValidationError({
                    'session_duration': 'Длительность сессии не может быть отрицательной'
                })

        return data


class ConnectionStatusResponseSerializer(serializers.Serializer):
    """Сериализатор для ответа на запрос статуса подключения."""
    success = serializers.BooleanField(help_text="Успешность обработки запроса")
    message = serializers.CharField(help_text="Сообщение о результате")
    log_id = serializers.UUIDField(help_text="ID созданной записи в логе")
    timestamp = serializers.DateTimeField(help_text="Время обработки запроса")


class ConnectionLogSerializer(serializers.ModelSerializer):
    """Сериализатор для модели ConnectionLog."""

    class Meta:
        from .models import ConnectionLog
        model = ConnectionLog
        fields = [
            'id', 'user', 'status', 'device_id', 'server_id',
            'client_ip', 'timestamp', 'user_agent', 'session_duration',
            'created_at'
        ]
        read_only_fields = ['id', 'user', 'created_at']


class VpnSubscriptionInfoSerializer(serializers.Serializer):
    """Serializer for VPN subscription information."""
    plan_name = serializers.CharField()
    end_date = serializers.DateTimeField()
    days_remaining = serializers.IntegerField()


class TrafficStatsResponseSerializer(serializers.Serializer):
    """Serializer for traffic statistics response."""
    success = serializers.BooleanField()
    traffic_stats = TrafficStatsSerializer()
    subscription_info = VpnSubscriptionInfoSerializer()


class VpnErrorResponseSerializer(serializers.Serializer):
    """Serializer for VPN-specific error responses."""
    error = serializers.CharField()


@extend_schema_serializer(
    examples=[
        OpenApiExample(
            'SingBox Configuration (Stage 1 Focus)',
            description='Stage 1: Only SingBox configurations are supported',
            value={
                "success": True,
                "config_type": "singbox",
                "config": {
                    "outbounds": [
                        {
                            "type": "vmess",
                            "tag": "proxy",
                            "server": "example.com",
                            "server_port": 443,
                            "uuid": "uuid-here",
                            "security": "auto",
                            "alter_id": 0
                        }
                    ],
                    "inbounds": [
                        {
                            "type": "tun",
                            "tag": "tun-in",
                            "interface_name": "tun0",
                            "inet4_address": "**********/30",
                            "auto_route": True,
                            "strict_route": False
                        }
                    ]
                },
                "subscription_info": {
                    "plan_name": "Basic",
                    "end_date": "2024-01-01T10:00:00Z",
                    "traffic_limit_gb": 100,
                    "traffic_used_gb": 15.5
                }
            }
        )
        # TODO (Этап 2): Восстановить пример Clash конфигурации
        # TODO (Этап 3): Восстановить пример Subscription ссылок
    ]
)
class VPNConfigExampleSerializer(VPNConfigResponseSerializer):
    """Extended VPN config serializer with examples."""
    pass


@extend_schema_serializer(
    examples=[
        OpenApiExample(
            'Traffic Statistics',
            value={
                "success": True,
                "traffic_stats": {
                    "upload_bytes": 1048576,
                    "download_bytes": 15728640,
                    "total_used_bytes": 16777216,
                    "total_used_gb": 0.016,
                    "limit_gb": 100,
                    "limit_bytes": 107374182400,
                    "last_sync": "2023-12-01T10:00:00Z"
                },
                "subscription_info": {
                    "plan_name": "Basic",
                    "end_date": "2024-01-01T10:00:00Z",
                    "days_remaining": 30
                }
            }
        )
    ]
)
class TrafficStatsExampleSerializer(TrafficStatsResponseSerializer):
    """Extended traffic stats serializer with examples."""
    pass


class LocationSerializer(serializers.Serializer):
    """
    Сериализатор для VPN локаций в ответах API.

    PURPOSE:
      - Предоставляет клиентам информацию о доступных VPN локациях
      - Скрывает технические детали (hiddify_params) от пользователей
      - Обеспечивает консистентный формат данных о локациях

    AAG (Actor -> Action -> Goal):
      - API -> Сериализует локации -> Предоставляет выбор пользователю
      - Клиент -> Получает список локаций -> Выбирает оптимальный сервер

    CONTRACT:
      PRECONDITIONS:
        - Локация активна и доступна для тарифного плана пользователя
      POSTCONDITIONS:
        - Возвращается только публичная информация о локации
        - Технические параметры (hiddify_params) скрыты
      INVARIANTS:
        - id всегда уникален
        - country_code в верхнем регистре (2 символа)
    """
    id = serializers.UUIDField(
        help_text="Уникальный идентификатор локации"
    )
    name = serializers.CharField(
        help_text="Название локации (например, 'Netherlands - Amsterdam')"
    )
    country_code = serializers.CharField(
        help_text="ISO код страны (например, 'NL')"
    )
    city = serializers.CharField(
        help_text="Город (может быть пустым)",
        allow_blank=True
    )
    flag_emoji = serializers.CharField(
        help_text="Эмодзи флага страны (например, '🇳🇱')",
        allow_blank=True
    )


class LocationListResponseSerializer(serializers.Serializer):
    """
    Сериализатор для ответа со списком доступных локаций.

    PURPOSE:
      - Стандартизирует формат ответа эндпоинта GET /api/locations/
      - Обеспечивает консистентность API ответов
      - Предоставляет метаинформацию о доступных локациях

    AAG (Actor -> Action -> Goal):
      - API -> Формирует ответ -> Предоставляет список доступных локаций
    """
    success = serializers.BooleanField(
        default=True,
        help_text="Успешность запроса"
    )
    locations = LocationSerializer(
        many=True,
        help_text="Список доступных VPN локаций"
    )
    count = serializers.IntegerField(
        help_text="Количество доступных локаций"
    )
