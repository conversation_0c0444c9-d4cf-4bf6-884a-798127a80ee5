"""
Views for VPN app - Stage 1 Focus: SingBox Only
"""
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from django.utils import timezone
import logging
from drf_spectacular.utils import extend_schema, OpenApiParameter, OpenApiExample
from drf_spectacular.types import OpenApiTypes

from accounts.models import HiddifyLink, UserDevice
from subscriptions.models import ActiveSubscription
from .services import HiddifyApiService, SingBoxConfigService
from .trojan_service import TrojanConfigService
from .serializers import VPNConfigExampleSerializer, TrafficStatsExampleSerializer, VpnErrorResponseSerializer, LocationListResponseSerializer
from .models import Location, SubscriptionPlanLocation

logger = logging.getLogger(__name__)


@extend_schema(
    tags=[' 01 - Core User Flow'],
    summary='Шаг 3: Получение VPN конфигурации',
    description='''
    Финальный и главный эндпоинт. Любой аутентифицированный пользователь с активной подпиской и активным устройством получает здесь свою персонализированную Sing-Box конфигурацию.

    **Требования для доступа:**
    - Пользователь должен быть аутентифицирован (JWT токен)
    - Наличие активной подписки
    - Устройство должно быть активным
    - JWT токен должен содержать hiddify_uuid и deviceId

    **Возвращаемая конфигурация:**
    - Полная SingBox конфигурация в JSON формате
    - Персонализированные UUID для Trojan и VMess протоколов
    - Настроенные DNS серверы и правила маршрутизации
    - Поддержка WebSocket, gRPC и HTTP Upgrade транспортов

    **Безопасность:** Конфигурация возвращается с заголовками no-cache для предотвращения кеширования.
    ''',
    responses={
        200: VPNConfigExampleSerializer,
        403: VpnErrorResponseSerializer,
        404: VpnErrorResponseSerializer
    },
    examples=[
        OpenApiExample(
            'Успешный ответ (структура конфигурации)',
            description='Персонализированная SingBox конфигурация с пользовательским UUID',
            value={
                "dns": {
                    "servers": [{"tag": "cloudflare", "address": "https://*******/dns-query"}],
                    "final": "cloudflare"
                },
                "inbounds": [{"type": "tun", "inet4_address": "**********/30"}],
                "outbounds": [
                    {"type": "selector", "tag": "proxy", "outbounds": ["trojan-ws", "vmess-ws"]},
                    {"type": "trojan", "tag": "trojan-ws", "server": "***********", "password": "user-uuid-here"}
                ],
                "route": {"final": "proxy"}
            },
            response_only=True
        ),
        OpenApiExample(
            'Ошибка 403 - нет активной подписки',
            description='Пользователь не имеет активной подписки для получения VPN конфигурации',
            value={
                'error': 'No active subscription. Please renew your subscription.'
            },
            status_codes=['403'],
            response_only=True
        )
    ]
)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_vpn_config(request):
    """
    Получение персонализированной SingBox VPN конфигурации для аутентифицированного пользователя.

    PURPOSE:
      - Предоставляет готовую к использованию SingBox VPN конфигурацию
      - Использует шаблонный подход на основе singbox_Config_example
      - Подставляет уникальный UUID пользователя в конфигурацию
      - Возвращает чистую JSON конфигурацию без обертки

    AAG (Actor -> Action -> Goal):
      - Пользователь -> Запрашивает VPN конфигурацию -> Получает персонализированную SingBox конфигурацию
      - Система -> Проверяет права доступа -> Возвращает готовую конфигурацию с UUID пользователя

    CONTRACT:
      PRECONDITIONS:
        - Пользователь аутентифицирован (JWT токен с hiddify_uuid)
        - Активная подписка пользователя
      POSTCONDITIONS:
        - Возвращается персонализированная SingBox конфигурация в JSON формате
        - UUID пользователя подставлен в поля password (Trojan) и uuid (VMess)
        - Обновляется статистика последнего запроса конфигурации
      INVARIANTS:
        - Доступ только для пользователей с активной подпиской
        - Конфигурация точно соответствует структуре singbox_Config_example
        - Все статические параметры захардкожены в шаблоне
    """

    # Шаг 1: Аутентификация и проверка статуса пользователя
    user_account = request.user

    # Проверяем, что пользователь активен
    if not user_account.is_active:
        logger.warning(f"Inactive user {user_account.id} attempted to access VPN config")
        return Response({
            'error': 'User account is disabled.'
        }, status=status.HTTP_403_FORBIDDEN)

    # Извлекаем hiddify_uuid из JWT токена
    hiddify_uuid = None
    if hasattr(request.auth, 'payload'):
        hiddify_uuid = request.auth.payload.get('hiddify_uuid')

    if not hiddify_uuid:
        logger.warning(f"No hiddify_uuid in JWT token for user {user_account.id}")
        return Response({
            'error': 'VPN access not configured. Please contact support.'
        }, status=status.HTTP_403_FORBIDDEN)

    # Шаг 2: Проверка статуса устройства
    device_model_id = None
    if hasattr(request.auth, 'payload'):
        device_model_id = request.auth.payload.get('deviceId')

    if not device_model_id:
        logger.warning(f"No deviceId in JWT token for user {user_account.id}")
        return Response({
            'error': 'Device information is missing from token.'
        }, status=status.HTTP_403_FORBIDDEN)

    try:
        # Пытаемся получить АКТИВНОЕ устройство
        device = UserDevice.objects.get(id=device_model_id, is_active=True)
    except UserDevice.DoesNotExist:
        logger.warning(f"Device {device_model_id} is deactivated or does not exist for user {user_account.id}")
        return Response({
            'error': 'This device has been deactivated or does not exist.'
        }, status=status.HTTP_403_FORBIDDEN)

    try:
        # Шаг 3: Проверка активной подписки пользователя
        active_subscription = ActiveSubscription.objects.select_related('plan').filter(
            user=user_account,
            is_active=True,
            start_date__lte=timezone.now(),
            end_date__gte=timezone.now()
        ).first()

        if not active_subscription:
            logger.warning(f"User {user_account.id} has no active subscription")
            return Response({
                'error': 'No active subscription. Please renew your subscription.'
            }, status=status.HTTP_403_FORBIDDEN)

        # Шаг 4: Создание персонализированной конфигурации на основе шаблона
        config_template = {
            "dns": {
                "servers": [
                    {
                        "tag": "cloudflare",
                        "address": "https://*******/dns-query",
                        "address_resolver": "local",
                        "detour": "proxy"
                    },
                    {
                        "tag": "cloudflare-tls",
                        "address": "tls://*******",
                        "address_resolver": "local",
                        "detour": "proxy"
                    },
                    {
                        "tag": "google",
                        "address": "tls://*******",
                        "address_resolver": "local",
                        "detour": "proxy"
                    },
                    {
                        "tag": "local",
                        "address": "*********",
                        "detour": "direct"
                    },
                    {
                        "tag": "block",
                        "address": "rcode://success"
                    }
                ],
                "rules": [
                    {
                        "outbound": "any",
                        "server": "local"
                    },
                    {
                        "geoip": ["private"],
                        "server": "local"
                    },
                    {
                        "geoip": ["cn"],
                        "server": "local"
                    },
                    {
                        "domain_suffix": [".cn", ".ru"],
                        "server": "local"
                    }
                ],
                "final": "cloudflare",
                "strategy": "ipv4_only",
                "disable_cache": False,
                "disable_expire": False
            },
            "inbounds": [
                {
                    "type": "tun",
                    "inet4_address": "**********/30",
                    "inet6_address": "fdfe:dcba:9876::1/126",
                    "auto_route": True,
                    "strict_route": False,
                    "sniff": True,
                    "sniff_override_destination": False,
                    "domain_strategy": "ipv4_only"
                }
            ],
            "outbounds": [
                {
                    "type": "selector",
                    "tag": "proxy",
                    "outbounds": [
                        "trojan-ws",
                        "vmess-ws",
                        "vmess-httpupgrade",
                        "trojan-grpc",
                        "vmess-grpc"
                    ]
                },
                {
                    "type": "trojan",
                    "tag": "trojan-ws",
                    "server": "***********",
                    "server_port": 443,
                    "password": hiddify_uuid,  # Персонализация
                    "tls": {
                        "enabled": True,
                        "server_name": "***********.sslip.io",
                        "alpn": "http/1.1",
                        "utls": {
                            "enabled": True,
                            "fingerprint": "chrome"
                        }
                    },
                    "transport": {
                        "type": "ws",
                        "path": "/Cgm6B1DqLOKIFrY19tjCyr3egnx",
                        "headers": {
                            "Host": "***********.sslip.io"
                        },
                        "early_data_header_name": "Sec-WebSocket-Protocol"
                    }
                },
                {
                    "type": "vmess",
                    "tag": "vmess-ws",
                    "server": "***********",
                    "server_port": 443,
                    "uuid": hiddify_uuid,  # Персонализация
                    "security": "auto",
                    "tls": {
                        "enabled": True,
                        "server_name": "***********.sslip.io",
                        "alpn": "http/1.1",
                        "utls": {
                            "enabled": True,
                            "fingerprint": "chrome"
                        }
                    },
                    "packet_encoding": "xudp",
                    "transport": {
                        "type": "ws",
                        "path": "/39m0pgSOrY19tjCyr3egnx",
                        "headers": {
                            "Host": "***********.sslip.io"
                        },
                        "early_data_header_name": "Sec-WebSocket-Protocol"
                    }
                },
                {
                    "type": "vmess",
                    "tag": "vmess-httpupgrade",
                    "server": "***********",
                    "server_port": 443,
                    "uuid": hiddify_uuid,  # Персонализация
                    "security": "auto",
                    "tls": {
                        "enabled": True,
                        "server_name": "***********.sslip.io",
                        "alpn": "http/1.1",
                        "utls": {
                            "enabled": True,
                            "fingerprint": "chrome"
                        }
                    },
                    "packet_encoding": "xudp",
                    "transport": {
                        "type": "httpupgrade",
                        "path": "/39m0pgSOdKbicJLIaR",
                        "headers": {
                            "Host": "***********.sslip.io"
                        }
                    }
                },
                {
                    "type": "trojan",
                    "tag": "trojan-grpc",
                    "server": "***********",
                    "server_port": 443,
                    "password": hiddify_uuid,  # Персонализация
                    "tls": {
                        "enabled": True,
                        "server_name": "***********.sslip.io",
                        "alpn": "h2",
                        "utls": {
                            "enabled": True,
                            "fingerprint": "chrome"
                        }
                    },
                    "transport": {
                        "type": "grpc",
                        "service_name": "Cgm6B1DqLOKIFOh7gdS9",
                        "idle_timeout": "1m55s",
                        "ping_timeout": "15s"
                    }
                },
                {
                    "type": "vmess",
                    "tag": "vmess-grpc",
                    "server": "***********",
                    "server_port": 443,
                    "uuid": hiddify_uuid,  # Персонализация
                    "security": "auto",
                    "tls": {
                        "enabled": True,
                        "server_name": "***********.sslip.io",
                        "alpn": "h2",
                        "utls": {
                            "enabled": True,
                            "fingerprint": "chrome"
                        }
                    },
                    "packet_encoding": "xudp",
                    "transport": {
                        "type": "grpc",
                        "service_name": "39m0pgSOOh7gdS9",
                        "idle_timeout": "1m55s",
                        "ping_timeout": "15s"
                    }
                },
                {
                    "type": "direct",
                    "tag": "direct"
                },
                {
                    "type": "block",
                    "tag": "block"
                },
                {
                    "type": "dns",
                    "tag": "dns-out"
                }
            ],
            "route": {
                "rules": [
                    {
                        "protocol": "dns",
                        "outbound": "dns-out"
                    },
                    {
                        "geoip": ["private"],
                        "outbound": "direct"
                    },
                    {
                        "geoip": ["cn"],
                        "outbound": "direct"
                    },
                    {
                        "domain_suffix": [".cn", ".ru"],
                        "outbound": "direct"
                    }
                ],
                "final": "proxy",
                "auto_detect_interface": True
            }
        }

        # Шаг 5: Обновление информации о последнем запросе конфигурации
        try:
            hiddify_link = HiddifyLink.objects.get(
                user=user_account,
                is_active_in_hiddify=True
            )
            hiddify_link.last_config_request = timezone.now()
            hiddify_link.save(update_fields=['last_config_request'])
        except HiddifyLink.DoesNotExist:
            # Если нет HiddifyLink, это не критично для возврата конфигурации
            logger.info(f"No HiddifyLink found for user {user_account.id}, but config generated")

        # Обновляем время последнего обращения устройства
        device.last_seen = timezone.now()
        device.save(update_fields=['last_seen'])

        logger.info(f"Generated personalized SingBox config for user {user_account.id} with UUID {hiddify_uuid}")

        # Шаг 6: Возврат персонализированной конфигурации (чистый JSON без обертки)
        return Response(config_template, status=status.HTTP_200_OK, headers={
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Content-Type': 'application/json'
        })

    except Exception as e:
        logger.error(f"SingBox config request failed for user {user_account.id}: {str(e)}")
        return Response({
            'error': 'Failed to process SingBox VPN configuration request.'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@extend_schema(
    tags=['VPN'],
    summary='Get traffic statistics',
    description='Get traffic usage statistics',
    responses={
        200: TrafficStatsExampleSerializer,
        404: VpnErrorResponseSerializer
    }
)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_traffic_stats(request):
    """
    Получение статистики использования трафика.
    
    PURPOSE:
      - Предоставляет актуальную статистику трафика
      - Синхронизирует данные с Hiddify Manager
      - Показывает лимиты и использование
    """
    user_account = request.user
    
    try:
        hiddify_link = HiddifyLink.objects.get(
            user=user_account,
            is_active_in_hiddify=True
        )
        
        active_subscription = ActiveSubscription.objects.filter(
            user=user_account,
            is_active=True,
            start_date__lte=timezone.now(),
            end_date__gte=timezone.now()
        ).first()
        
        if not active_subscription:
            return Response({
                'error': 'No active subscription found'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Получаем актуальную статистику из Hiddify через SingBox endpoint (Stage 1 Focus)
        hiddify_service = HiddifyApiService()
        success, config_response = hiddify_service.get_singbox_config_for_user(
            str(hiddify_link.hiddify_user_uuid)
        )

        # TODO (Этап 3): Восстановить использование get_subscription_link_for_user для получения статистики
        # success, config_response = hiddify_service.get_subscription_link_for_user(
        #     str(hiddify_link.hiddify_user_uuid)
        # )
        
        traffic_info = {}
        if success and 'traffic_info' in config_response:
            traffic_info = config_response['traffic_info']
            
            # Обновляем локальную статистику
            hiddify_link.traffic_used_bytes = traffic_info.get('download_bytes', 0) + traffic_info.get('upload_bytes', 0)
            hiddify_link.last_traffic_sync = timezone.now()
            hiddify_link.save(update_fields=['traffic_used_bytes', 'last_traffic_sync'])
        
        return Response({
            'success': True,
            'traffic_stats': {
                'upload_bytes': traffic_info.get('upload_bytes', 0),
                'download_bytes': traffic_info.get('download_bytes', 0),
                'total_used_bytes': traffic_info.get('upload_bytes', 0) + traffic_info.get('download_bytes', 0),
                'total_used_gb': round((traffic_info.get('upload_bytes', 0) + traffic_info.get('download_bytes', 0)) / (1024**3), 2),
                'limit_gb': active_subscription.plan.traffic_limit_gb,
                'limit_bytes': active_subscription.plan.traffic_limit_gb * 1024 * 1024 * 1024,
                'last_sync': hiddify_link.last_traffic_sync.isoformat() if hiddify_link.last_traffic_sync else None
            },
            'subscription_info': {
                'plan_name': active_subscription.plan.name,
                'end_date': active_subscription.end_date.isoformat(),
                'days_remaining': active_subscription.days_remaining
            }
        })
        
    except HiddifyLink.DoesNotExist:
        return Response({
            'error': 'VPN access not configured'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.error(f"Traffic stats request failed for user {user_account.id}: {str(e)}")
        return Response({
            'error': 'Failed to retrieve traffic statistics'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@extend_schema(
    tags=['VPN'],
    summary='Get available VPN locations',
    description='Get list of VPN locations available for user\'s subscription plan',
    responses={
        200: LocationListResponseSerializer,
        403: VpnErrorResponseSerializer,
        404: VpnErrorResponseSerializer
    },
    examples=[
        OpenApiExample(
            'Available Locations',
            description='List of VPN locations available for user\'s subscription plan',
            value={
                "success": True,
                "count": 3,
                "locations": [
                    {
                        "id": "550e8400-e29b-41d4-a716-************",
                        "name": "Netherlands - Amsterdam",
                        "country_code": "NL",
                        "city": "Amsterdam",
                        "flag_emoji": "🇳🇱"
                    },
                    {
                        "id": "550e8400-e29b-41d4-a716-446655440001",
                        "name": "Germany - Frankfurt",
                        "country_code": "DE",
                        "city": "Frankfurt",
                        "flag_emoji": "🇩🇪"
                    },
                    {
                        "id": "550e8400-e29b-41d4-a716-446655440002",
                        "name": "United States - New York",
                        "country_code": "US",
                        "city": "New York",
                        "flag_emoji": "🇺🇸"
                    }
                ]
            }
        )
    ]
)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_available_locations(request):
    """
    Получение списка доступных VPN локаций для текущего пользователя.

    PURPOSE:
      - Предоставляет пользователю список VPN серверов, доступных по его тарифному плану
      - Фильтрует локации на основе активной подписки пользователя
      - Обеспечивает дифференциацию сервиса по уровням подписки
      - Оптимизирует запросы к базе данных с помощью select_related/prefetch_related

    AAG (Actor -> Action -> Goal):
      - Пользователь -> Запрашивает доступные локации -> Получает список для выбора сервера
      - Система -> Проверяет тарифный план -> Возвращает соответствующие локации
      - Клиент -> Отображает локации -> Позволяет пользователю выбрать оптимальный сервер

    CONTRACT:
      PRECONDITIONS:
        - Пользователь аутентифицирован (JWT токен)
        - Активная подписка пользователя существует
      POSTCONDITIONS:
        - Возвращается список локаций, доступных для тарифного плана пользователя
        - Локации отсортированы по стране и городу
        - Технические параметры (hiddify_params) скрыты от пользователя
      INVARIANTS:
        - Возвращаются только активные локации
        - Локации соответствуют тарифному плану пользователя
        - Структура ответа консистентна

    NOTES:
      - Если у пользователя нет активной подписки, возвращается пустой список
      - Локации кэшируются на уровне базы данных через select_related
      - Эндпоинт оптимизирован для частых запросов от мобильных клиентов
    """
    user_account = request.user

    try:
        # Шаг 1: Проверка активной подписки пользователя
        active_subscription = ActiveSubscription.objects.select_related('plan').filter(
            user=user_account,
            is_active=True,
            start_date__lte=timezone.now(),
            end_date__gte=timezone.now()
        ).first()

        if not active_subscription:
            logger.info(f"User {user_account.id} has no active subscription for locations")
            return Response({
                'success': True,
                'count': 0,
                'locations': []
            }, status=status.HTTP_200_OK)

        # Шаг 2: Получение доступных локаций для тарифного плана с оптимизацией запросов
        available_locations = Location.objects.filter(
            location_plans__plan=active_subscription.plan,
            is_active=True
        ).select_related().order_by('country_code', 'city', 'name')

        # Шаг 3: Сериализация данных локаций (скрываем технические детали)
        locations_data = []
        for location in available_locations:
            locations_data.append({
                'id': str(location.id),
                'name': location.name,
                'country_code': location.country_code,
                'city': location.city,
                'flag_emoji': location.flag_emoji
            })

        logger.info(f"Returned {len(locations_data)} locations for user {user_account.id} with plan {active_subscription.plan.name}")

        return Response({
            'success': True,
            'count': len(locations_data),
            'locations': locations_data
        }, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Failed to get locations for user {user_account.id}: {str(e)}")
        return Response({
            'error': 'Failed to retrieve available locations'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@extend_schema(
    tags=['VPN'],
    summary='Get Trojan VPN configuration',
    description='Get Trojan VPN configuration with TUN or Mixed mode for authenticated user',
    parameters=[
        OpenApiParameter(
            name='mode',
            type=OpenApiTypes.STR,
            location=OpenApiParameter.QUERY,
            description='Connection mode: "tun" for full VPN or "mixed" for proxy mode',
            required=False,
            default='tun',
            enum=['tun', 'mixed']
        ),
        OpenApiParameter(
            name='listen_port',
            type=OpenApiTypes.INT,
            location=OpenApiParameter.QUERY,
            description='Local listen port for mixed mode (default: 2080)',
            required=False
        )
    ],
    responses={
        200: VPNConfigExampleSerializer,
        400: VpnErrorResponseSerializer,
        403: VpnErrorResponseSerializer,
        404: VpnErrorResponseSerializer
    },
    examples=[
        OpenApiExample(
            'Trojan TUN Config Request',
            description='Request Trojan configuration with TUN interface (full VPN)',
            value={
                "mode": "tun"
            }
        ),
        OpenApiExample(
            'Trojan Mixed Config Request',
            description='Request Trojan configuration with Mixed interface (proxy mode)',
            value={
                "mode": "mixed",
                "listen_port": 2080
            }
        )
    ]
)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_trojan_config(request):
    """
    Получение Trojan VPN конфигурации для аутентифицированного пользователя.

    PURPOSE:
      - Предоставляет Trojan VPN конфигурацию в формате SingBox
      - Поддерживает TUN режим (полный VPN) и Mixed режим (прокси)
      - Использует реальные параметры нашего Hiddify Manager сервера
      - Совместим с конфигурацией из файла 06_undef_uk_trojan.json

    AAG (Actor -> Action -> Goal):
      - Пользователь -> Запрашивает Trojan конфигурацию -> Получает SingBox конфигурацию
      - Система -> Проверяет права доступа -> Возвращает Trojan конфигурацию

    CONTRACT:
      PRECONDITIONS:
        - Пользователь аутентифицирован (JWT токен)
        - Активная подписка пользователя
      POSTCONDITIONS:
        - Возвращается Trojan VPN конфигурация в JSON формате
        - config_type равен 'trojan'
        - Обновляется статистика последнего запроса
      INVARIANTS:
        - Доступ только для пользователей с активной подпиской
        - Конфигурация использует реальные параметры сервера ductuspro.ru
    """
    user_account = request.user
    mode = request.GET.get('mode', 'tun').lower()
    listen_port = request.GET.get('listen_port', 2080)

    # Валидация параметров
    if mode not in ['tun', 'mixed']:
        return Response({
            'error': 'Invalid mode. Use "tun" or "mixed"'
        }, status=status.HTTP_400_BAD_REQUEST)

    try:
        listen_port = int(listen_port)
        if listen_port < 1024 or listen_port > 65535:
            raise ValueError("Port out of range")
    except (ValueError, TypeError):
        return Response({
            'error': 'Invalid listen_port. Must be integer between 1024-65535'
        }, status=status.HTTP_400_BAD_REQUEST)

    try:
        # Шаг 1: Проверка активной подписки
        active_subscription = ActiveSubscription.objects.filter(
            user=user_account,
            is_active=True,
            start_date__lte=timezone.now(),
            end_date__gte=timezone.now()
        ).first()

        if not active_subscription:
            logger.warning(f"User {user_account.id} has no active subscription for Trojan config")
            return Response({
                'error': 'No active subscription found'
            }, status=status.HTTP_403_FORBIDDEN)

        # Шаг 2: Проверка Hiddify Link
        try:
            hiddify_link = HiddifyLink.objects.get(
                user=user_account,
                is_active_in_hiddify=True
            )
        except HiddifyLink.DoesNotExist:
            logger.warning(f"User {user_account.id} has no active Hiddify link for Trojan config")
            return Response({
                'error': 'VPN access not configured'
            }, status=status.HTTP_404_NOT_FOUND)

        # Шаг 3: Генерация Trojan конфигурации
        if mode == 'tun':
            trojan_config = TrojanConfigService.generate_trojan_tun_config(
                password=str(hiddify_link.hiddify_user_uuid),
                user_uuid=str(user_account.id)
            )
        else:  # mixed mode
            trojan_config = TrojanConfigService.generate_trojan_mixed_config(
                password=str(hiddify_link.hiddify_user_uuid),
                listen_port=listen_port,
                user_uuid=str(user_account.id)
            )

        # Шаг 4: Получение информации о трафике (опционально)
        hiddify_service = HiddifyApiService()
        success, hiddify_response = hiddify_service.get_singbox_config_for_user(
            str(hiddify_link.hiddify_user_uuid)
        )

        traffic_info = {}
        if success and 'traffic_info' in hiddify_response:
            traffic_info = hiddify_response['traffic_info']

        logger.info(f"Generated Trojan {mode} config for user {user_account.id}")

        # Шаг 5: Формирование ответа
        response_data = {
            'success': True,
            'config_type': 'trojan',
            'mode': mode,
            'config': trojan_config,
            'connection_info': TrojanConfigService.get_trojan_connection_info(),
            'subscription_info': {
                'plan_name': active_subscription.plan.name,
                'end_date': active_subscription.end_date.isoformat(),
                'traffic_limit_gb': active_subscription.plan.traffic_limit_gb,
                'traffic_used_gb': round(hiddify_link.traffic_used_bytes / (1024**3), 2) if hiddify_link.traffic_used_bytes else 0
            }
        }

        if mode == 'mixed':
            response_data['listen_port'] = listen_port

        if traffic_info:
            response_data['traffic_info'] = traffic_info

        return Response(response_data, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Trojan config request failed for user {user_account.id}: {str(e)}")
        return Response({
            'error': 'Failed to process Trojan VPN configuration request.'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
