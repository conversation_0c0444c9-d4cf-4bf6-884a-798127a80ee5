{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block title %}{{ title }} | {{ site_title|default:_('Django site admin') }}{% endblock %}

{% block extrahead %}
{{ block.super }}
<style>
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.stat-card {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
}

.stat-card h3 {
    margin: 0 0 10px 0;
    color: #495057;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-number {
    font-size: 32px;
    font-weight: bold;
    color: #007bff;
    margin: 10px 0;
}

.stat-subtitle {
    font-size: 12px;
    color: #6c757d;
    margin: 5px 0;
}

.recent-activity {
    margin: 30px 0;
}

.activity-section {
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin: 20px 0;
    overflow: hidden;
}

.activity-header {
    background: #f8f9fa;
    padding: 15px 20px;
    border-bottom: 1px solid #dee2e6;
    font-weight: bold;
}

.activity-list {
    padding: 0;
    margin: 0;
    list-style: none;
}

.activity-item {
    padding: 15px 20px;
    border-bottom: 1px solid #f1f3f4;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-info {
    flex: 1;
}

.activity-time {
    color: #6c757d;
    font-size: 12px;
}

.warning {
    color: #dc3545;
}

.success {
    color: #28a745;
}

.info {
    color: #17a2b8;
}

.app-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin: 30px 0;
}

.module {
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
}

.module h2 {
    background: #f8f9fa;
    margin: 0;
    padding: 15px 20px;
    border-bottom: 1px solid #dee2e6;
    font-size: 16px;
}

.module ul {
    margin: 0;
    padding: 0;
    list-style: none;
}

.module li {
    border-bottom: 1px solid #f1f3f4;
}

.module li:last-child {
    border-bottom: none;
}

.module a {
    display: block;
    padding: 12px 20px;
    text-decoration: none;
    color: #007bff;
}

.module a:hover {
    background: #f8f9fa;
}
</style>
{% endblock %}

{% block breadcrumbs %}{% endblock %}

{% block content %}
<div id="content-main">
    <h1>{{ title }}</h1>
    
    <!-- Основная статистика -->
    <div class="dashboard-stats">
        <!-- Пользователи -->
        <div class="stat-card">
            <h3>Total Users</h3>
            <div class="stat-number">{{ total_users|default:0 }}</div>
            <div class="stat-subtitle">{{ registered_users|default:0 }} registered, {{ anonymous_users|default:0 }} anonymous</div>
            <div class="stat-subtitle success">{{ active_users|default:0 }} active</div>
        </div>
        
        <!-- Новые пользователи -->
        <div class="stat-card">
            <h3>New Users</h3>
            <div class="stat-number">{{ new_users_7d|default:0 }}</div>
            <div class="stat-subtitle">Last 7 days</div>
            <div class="stat-subtitle">{{ new_users_30d|default:0 }} in last 30 days</div>
        </div>
        
        <!-- Устройства -->
        <div class="stat-card">
            <h3>Devices</h3>
            <div class="stat-number">{{ active_devices|default:0 }}</div>
            <div class="stat-subtitle">{{ total_devices|default:0 }} total devices</div>
            <div class="stat-subtitle info">{{ devices_last_7d|default:0 }} active in 7 days</div>
        </div>
        
        <!-- Подписки -->
        <div class="stat-card">
            <h3>Active Subscriptions</h3>
            <div class="stat-number">{{ active_subscriptions|default:0 }}</div>
            <div class="stat-subtitle">{{ total_subscriptions|default:0 }} total subscriptions</div>
            {% if expiring_soon > 0 %}
            <div class="stat-subtitle warning">{{ expiring_soon }} expiring soon</div>
            {% endif %}
        </div>
        
        <!-- Промокоды -->
        <div class="stat-card">
            <h3>Promo Codes</h3>
            <div class="stat-number">{{ activated_promo_codes|default:0 }}</div>
            <div class="stat-subtitle">{{ total_promo_codes|default:0 }} total codes</div>
            <div class="stat-subtitle">{{ promo_codes_30d|default:0 }} activated in 30 days</div>
            <div class="stat-subtitle info">{{ unused_promo_codes|default:0 }} unused</div>
        </div>
        
        <!-- Локации -->
        <div class="stat-card">
            <h3>VPN Locations</h3>
            <div class="stat-number">{{ active_locations|default:0 }}</div>
            <div class="stat-subtitle">{{ total_locations|default:0 }} total locations</div>
        </div>
    </div>
    
    <!-- Приложения админки -->
    {% if app_list %}
    <div class="app-list">
        {% for app in app_list %}
        <div class="module">
            <h2><a href="{{ app.app_url }}">{{ app.name }}</a></h2>
            {% if app.models %}
            <ul>
                {% for model in app.models %}
                <li>
                    {% if model.admin_url %}
                    <a href="{{ model.admin_url }}">{{ model.name }}</a>
                    {% else %}
                    {{ model.name }}
                    {% endif %}
                </li>
                {% endfor %}
            </ul>
            {% endif %}
        </div>
        {% endfor %}
    </div>
    {% endif %}
    
    <!-- Топ планы подписок -->
    {% if subscription_plans_stats %}
    <div class="activity-section">
        <div class="activity-header">Top Subscription Plans</div>
        <ul class="activity-list">
            {% for plan in subscription_plans_stats %}
            <li class="activity-item">
                <div class="activity-info">
                    <strong>{{ plan.plan__name }}</strong>
                </div>
                <div class="activity-time">{{ plan.count }} active</div>
            </li>
            {% endfor %}
        </ul>
    </div>
    {% endif %}
    
    <!-- Недавняя активность -->
    <div class="recent-activity">
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
            
            <!-- Недавние регистрации -->
            {% if recent_registrations %}
            <div class="activity-section">
                <div class="activity-header">Recent Registrations</div>
                <ul class="activity-list">
                    {% for user in recent_registrations %}
                    <li class="activity-item">
                        <div class="activity-info">
                            {% if user.is_anonymous %}
                                <strong>Anonymous {{ user.id|slice:":8" }}</strong>
                            {% else %}
                                <strong>{{ user.email|default:"No email" }}</strong>
                            {% endif %}
                        </div>
                        <div class="activity-time">{{ user.date_joined|timesince }} ago</div>
                    </li>
                    {% endfor %}
                </ul>
            </div>
            {% endif %}
            
            <!-- Недавние активации промокодов -->
            {% if recent_activations %}
            <div class="activity-section">
                <div class="activity-header">Recent Promo Activations</div>
                <ul class="activity-list">
                    {% for activation in recent_activations %}
                    <li class="activity-item">
                        <div class="activity-info">
                            <strong>{{ activation.code }}</strong> - {{ activation.plan.name }}
                            <br>
                            <small>
                                {% if activation.activated_by.is_anonymous %}
                                    by Anonymous {{ activation.activated_by.id|slice:":8" }}
                                {% else %}
                                    by {{ activation.activated_by.email|default:"No email" }}
                                {% endif %}
                            </small>
                        </div>
                        <div class="activity-time">{{ activation.activated_at|timesince }} ago</div>
                    </li>
                    {% endfor %}
                </ul>
            </div>
            {% endif %}
            
        </div>
    </div>
    
    <!-- Быстрые ссылки -->
    <div class="activity-section">
        <div class="activity-header">Quick Actions</div>
        <div style="padding: 20px;">
            <a href="{% url 'admin:accounts_useraccount_changelist' %}" class="button">Manage Users</a>
            <a href="{% url 'admin:subscriptions_activesubscription_changelist' %}" class="button">Manage Subscriptions</a>
            <a href="{% url 'admin:promo_promocode_changelist' %}" class="button">Manage Promo Codes</a>
            <a href="{% url 'admin:promo_promocode_generate' %}" class="button">Generate Promo Codes</a>
            <a href="{% url 'admin:vpn_location_changelist' %}" class="button">Manage Locations</a>
        </div>
    </div>
</div>
{% endblock %}
