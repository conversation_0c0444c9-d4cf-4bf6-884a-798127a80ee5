{% extends "admin/base_site.html" %}
{% load i18n admin_urls static admin_modify %}

{% block title %}Generate Promo Codes{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
    <a href="{% url 'admin:index' %}">Home</a>
    &rsaquo; <a href="{% url 'admin:app_list' app_label='promo' %}">Promo</a>
    &rsaquo; <a href="{% url 'admin:promo_promocode_changelist' %}">Promo codes</a>
    &rsaquo; Generate Codes
</div>
{% endblock %}

{% block content %}
<div id="content-main">
    <div class="module aligned">
        <h1>Generate Promo Codes</h1>
        
        <form method="post" id="generate-codes-form">
            {% csrf_token %}
            
            <div class="form-row">
                <div>
                    <label for="id_plan" class="required">Subscription Plan:</label>
                    <select name="plan" id="id_plan" required>
                        <option value="">---------</option>
                        {% for plan in plans %}
                            <option value="{{ plan.id }}">{{ plan.name }} - {{ plan.price }} {{ plan.currency }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            
            <div class="form-row">
                <div>
                    <label for="id_quantity" class="required">Quantity:</label>
                    <input type="number" name="quantity" id="id_quantity" min="1" max="1000" value="10" required>
                    <p class="help">Number of promo codes to generate (1-1000)</p>
                </div>
            </div>
            
            <div class="form-row">
                <div>
                    <label for="id_expires_at">Expires At:</label>
                    <input type="date" name="expires_at" id="id_expires_at">
                    <p class="help">Optional expiration date. Leave empty for permanent codes.</p>
                </div>
            </div>
            
            <div class="submit-row">
                <input type="submit" value="Generate Codes" class="default" name="_generate">
                <p class="deletelink-box">
                    <a href="{% url 'admin:promo_promocode_changelist' %}" class="deletelink">Cancel</a>
                </p>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('generate-codes-form');
    const submitBtn = form.querySelector('input[type="submit"]');
    
    form.addEventListener('submit', function(e) {
        const quantity = parseInt(document.getElementById('id_quantity').value);
        
        if (quantity > 100) {
            if (!confirm(`You are about to generate ${quantity} promo codes. This may take a while. Continue?`)) {
                e.preventDefault();
                return;
            }
        }
        
        // Disable submit button to prevent double submission
        submitBtn.disabled = true;
        submitBtn.value = 'Generating...';
    });
});
</script>
{% endblock %}
