# 📋 План сквозного тестирования (E2E) VPN-сервиса

## 🎯 Цель
Провести полное ручное тестирование ключевых пользовательских сценариев через API-клиент (Postman/curl) для подтверждения готовности системы.

---

## 🔧 Подготовка к тестированию

### Настройка окружения
1. **Запуск сервера**: `python manage.py runserver 0.0.0.0:8090`
2. **Base URL**: `http://ductuspro.ru:8090/api/`
3. **Swagger UI**: `http://ductuspro.ru:8090/api/docs/`

### Проверка готовности
```bash
# Проверка здоровья системы
curl -X GET "http://ductuspro.ru:8090/health/?include_external=true"
```

---

## 📱 Сценарий 1: "Анонимный разведчик → Осторожный пользователь"

### Шаг 1: Регистрация анонимного устройства
```http
POST /api/devices/register
Content-Type: application/json

{
    "device_id": "test-device-anonymous-001",
    "device_name": "iPhone Test",
    "device_type": "mobile"
}
```

**Ожидаемый результат:**
- Статус: `201 Created`
- Поля ответа: `success`, `tokens` (access_token, refresh_token), `user`, `subscription`
- JWT токен содержит: `userId`, `deviceId`, `tokenType: "anonymous"`

### Шаг 2: Получение VPN конфигурации (анонимный)
```http
GET /api/vpn/config
Authorization: Bearer {access_token_from_step1}
```

**Ожидаемый результат:**
- Статус: `200 OK`
- Конфигурация SingBox с персонализированными параметрами
- Размер ответа: ~3KB JSON
- Headers: `Cache-Control: no-cache, no-store, must-revalidate`

### Шаг 3: Регистрация полноценного пользователя
```http
POST /api/auth/register
Authorization: Bearer {access_token_from_step1}
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "SecurePass123!",
    "password_confirm": "SecurePass123!"
}
```

**Ожидаемый результат:**
- Статус: `201 Created`
- Конвертация анонимного пользователя в зарегистрированного
- Новые JWT токены с `tokenType: "registered"`
- Сохранение существующей подписки

### Шаг 4: Получение VPN конфигурации (зарегистрированный)
```http
GET /api/vpn/config
Authorization: Bearer {new_access_token_from_step3}
```

**Ожидаемый результат:**
- Статус: `200 OK`
- Та же конфигурация, но с обновленным hiddify_uuid
- Подтверждение сохранения доступа

---

## 👨‍👩‍👧‍👦 Сценарий 2: "Семейный доступ"

### Шаг 1: Генерация кода активации
```http
GET /api/auth/activation-code
Authorization: Bearer {registered_user_token}
```

**Ожидаемый результат:**
- Статус: `200 OK`
- Поля: `activation_code`, `expires_at`, `expires_in_minutes`
- Код: 8-символьный буквенно-цифровой

### Шаг 2: Активация на новом устройстве
```http
POST /api/auth/activate
Content-Type: application/json

{
    "activation_code": "{code_from_step1}",
    "device_id": "test-device-family-002",
    "device_name": "iPad Family",
    "device_type": "tablet"
}
```

**Ожидаемый результат:**
- Статус: `200 OK`
- Новые JWT токены для второго устройства
- Доступ к той же подписке
- Проверка лимита устройств

---

## 💳 Сценарий 3: "Покупка подписки"

### Шаг 1: Активация промокода
```http
POST /api/promo/activate
Authorization: Bearer {user_token}
Content-Type: application/json

{
    "promo_code": "TESTCODE123"
}
```

**Подготовка:** Создать промокод через Django Admin
```python
# В Django shell
from promo.models import PromoCode
from subscriptions.models import SubscriptionPlan

plan = SubscriptionPlan.objects.filter(name__icontains="premium").first()
PromoCode.objects.create(code="TESTCODE123", plan=plan)
```

**Ожидаемый результат:**
- Статус: `200 OK`
- Создание новой подписки
- Обновление информации о пользователе

---

## 🔍 Проверки безопасности

### Тест 1: Неавторизованный доступ
```http
GET /api/vpn/config
# Без Authorization header
```
**Ожидаемый результат:** `401 Unauthorized`

### Тест 2: Недействительный токен
```http
GET /api/vpn/config
Authorization: Bearer invalid_token_here
```
**Ожидаемый результат:** `401 Unauthorized`

### Тест 3: Конфликт устройств
```http
POST /api/auth/activate
Content-Type: application/json

{
    "activation_code": "{valid_code}",
    "device_id": "already-used-device-id",
    "device_name": "Conflict Test",
    "device_type": "mobile"
}
```
**Ожидаемый результат:** `409 Conflict`

---

## 📊 Критерии успеха

### ✅ Функциональность
- [ ] Все эндпоинты возвращают корректные статус-коды
- [ ] JWT токены содержат правильные claims
- [ ] VPN конфигурации персонализированы
- [ ] Подписки корректно связываются с устройствами

### ✅ Безопасность
- [ ] Неавторизованные запросы блокируются
- [ ] Конфликты устройств обнаруживаются
- [ ] Коды активации имеют срок действия

### ✅ Производительность
- [ ] Время ответа < 2 секунд для всех эндпоинтов
- [ ] Отсутствие N+1 запросов к БД
- [ ] Корректная обработка ошибок

---

## 🐛 Отладка

### Логи для анализа
```bash
# Django логи
tail -f logs/django.log

# Hiddify API логи
tail -f logs/hiddify_api.log
```

### Полезные команды
```bash
# Проверка пользователей
python manage.py shell -c "from accounts.models import UserAccount; print(f'Users: {UserAccount.objects.count()}')"

# Проверка подписок
python manage.py shell -c "from subscriptions.models import ActiveSubscription; print(f'Subscriptions: {ActiveSubscription.objects.count()}')"
```

---

## 📝 Отчет о тестировании

После выполнения всех сценариев заполните:

- **Дата тестирования:** ___________
- **Версия системы:** ___________
- **Пройденные сценарии:** ___/3
- **Обнаруженные проблемы:** ___________
- **Статус готовности:** ✅ Готов / ❌ Требует доработки
