"""
URL configuration for vpn_service project.
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from drf_spectacular.views import SpectacularAPIView, SpectacularSwaggerView, SpectacularRedocView
from .health import health_check, health_detailed, readiness_check, liveness_check

urlpatterns = [
    path(getattr(settings, 'ADMIN_URL', 'admin/'), admin.site.urls),

    path('api/auth/', include('accounts.urls')),
    path('api/vpn/', include('vpn.urls')),
    path('api/subscriptions/', include('subscriptions.urls')),
    path('api/promo/', include('promo.urls')),

    # DEPRECATED: MVP Device API (v1) - Stage 1 unification
    # Use Legacy API (POST /api/auth/register/ + GET /api/vpn/config/) instead
    # path('api/v1/', include('accounts.mvp_urls')),

    # Health checks
    path('health/', health_check, name='health'),
    path('health/detailed/', health_detailed, name='health-detailed'),
    path('health/ready/', readiness_check, name='readiness'),
    path('health/live/', liveness_check, name='liveness'),

    # API Documentation
    path('api/schema/', SpectacularAPIView.as_view(), name='schema'),
    path('api/docs/', SpectacularSwaggerView.as_view(url_name='schema'), name='swagger-ui'),
    path('api/redoc/', SpectacularRedocView.as_view(url_name='schema'), name='redoc'),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

# Admin site customization
admin.site.site_header = "VPN Service Administration"
admin.site.site_title = "VPN Service Admin"
admin.site.index_title = "VPN Service Dashboard"
