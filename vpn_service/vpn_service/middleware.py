"""
Custom middleware for VPN Service.
"""

from django.http import HttpResponsePermanentRedirect
from django.conf import settings
from django.utils.deprecation import MiddlewareMixin


class HTTPSToHTTPRedirectMiddleware(MiddlewareMixin):
    """
    Middleware для переадресации HTTPS запросов на HTTP в development режиме.
    
    PURPOSE:
      - Решает проблему доступа к Django development server через HTTPS
      - Переадресовывает HTTPS запросы на HTTP для Swagger UI
      - Работает только в DEBUG режиме для безопасности
    
    AAG (Actor -> Action -> Goal):
      - Браузер -> Запрашивает HTTPS URL -> Получает переадресацию на HTTP
      - Django dev server -> Обрабатывает HTTP запросы -> Swagger UI доступен
    
    CONTRACT:
      PRECONDITIONS:
        - DEBUG = True (работает только в development режиме)
        - Запрос приходит с HTTPS схемой
      POSTCONDITIONS:
        - HTTPS запросы переадресовываются на HTTP
        - HTTP запросы проходят без изменений
        - В production режиме middleware не активен
    
    NOTES:
      - Используется только для Stage 1 development
      - В production должен быть отключен (DEBUG=False)
      - Решает проблему "You're accessing the development server over HTTPS"
    """
    
    def process_request(self, request):
        """
        Обрабатывает входящие запросы и переадресовывает HTTPS на HTTP.
        
        Args:
            request: Django HttpRequest объект
            
        Returns:
            HttpResponsePermanentRedirect: Если нужна переадресация
            None: Если запрос должен продолжить обработку
        """
        # Работаем только в DEBUG режиме
        if not settings.DEBUG:
            return None
            
        # Проверяем, пришел ли запрос через HTTPS
        is_https = (
            request.is_secure() or 
            request.META.get('HTTP_X_FORWARDED_PROTO') == 'https' or
            request.META.get('HTTP_X_FORWARDED_SSL') == 'on'
        )
        
        if is_https:
            # Строим HTTP URL для переадресации
            http_url = request.build_absolute_uri().replace('https://', 'http://')
            
            # Для порта 443 (стандартный HTTPS) переадресовываем на 8000
            if ':443/' in http_url or http_url.endswith(':443'):
                http_url = http_url.replace(':443', ':8000')
            
            return HttpResponsePermanentRedirect(http_url)
        
        return None


class CORSMiddleware(MiddlewareMixin):
    """
    Дополнительный CORS middleware для публичного доступа к Swagger UI.
    
    PURPOSE:
      - Добавляет CORS заголовки для публичного доступа к API
      - Разрешает доступ к Swagger UI с любых доменов в development
      - Дополняет django-cors-headers для специфических случаев
    
    CONTRACT:
      PRECONDITIONS:
        - DEBUG = True (работает только в development режиме)
      POSTCONDITIONS:
        - Добавляет необходимые CORS заголовки
        - Разрешает OPTIONS запросы
        - Swagger UI доступен публично
    """
    
    def process_response(self, request, response):
        """
        Добавляет CORS заголовки к ответу.
        
        Args:
            request: Django HttpRequest объект
            response: Django HttpResponse объект
            
        Returns:
            HttpResponse: Ответ с добавленными CORS заголовками
        """
        # Работаем только в DEBUG режиме
        if not settings.DEBUG:
            return response
            
        # Добавляем CORS заголовки для публичного доступа
        response['Access-Control-Allow-Origin'] = '*'
        response['Access-Control-Allow-Methods'] = 'GET, POST, PUT, PATCH, DELETE, OPTIONS'
        response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With'
        response['Access-Control-Max-Age'] = '86400'
        
        return response
    
    def process_request(self, request):
        """
        Обрабатывает OPTIONS запросы для CORS preflight.
        
        Args:
            request: Django HttpRequest объект
            
        Returns:
            HttpResponse: Для OPTIONS запросов
            None: Для остальных запросов
        """
        if request.method == 'OPTIONS' and settings.DEBUG:
            from django.http import HttpResponse
            response = HttpResponse()
            response['Access-Control-Allow-Origin'] = '*'
            response['Access-Control-Allow-Methods'] = 'GET, POST, PUT, PATCH, DELETE, OPTIONS'
            response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With'
            response['Access-Control-Max-Age'] = '86400'
            return response
        
        return None


class SSLProxyMiddleware(MiddlewareMixin):
    """
    Middleware для корректной работы Django за SSL терминатором (HAProxy/nginx).

    PURPOSE:
      - Обеспечивает корректное определение HTTPS за прокси
      - Устанавливает правильные заголовки для Django
      - Поддерживает работу с HAProxy SSL терминацией

    AAG (Actor -> Action -> Goal):
      - HAProxy -> Терминирует SSL и передает заголовки -> Django корректно определяет HTTPS
      - Django -> Обрабатывает запросы как HTTPS -> Генерирует правильные URL и редиректы

    CONTRACT:
      PRECONDITIONS:
        - Прокси (HAProxy/nginx) передает заголовки X-Forwarded-Proto
        - USE_TLS = True в настройках
      POSTCONDITIONS:
        - request.is_secure() возвращает True для HTTPS запросов
        - Django генерирует HTTPS URL
        - Безопасные cookie работают корректно
    """

    def process_request(self, request):
        """
        Обрабатывает заголовки от SSL терминатора.

        Args:
            request: Django HttpRequest объект

        Returns:
            None: Всегда позволяет запросу продолжить обработку
        """
        # Проверяем заголовки от прокси
        forwarded_proto = request.META.get('HTTP_X_FORWARDED_PROTO')
        forwarded_ssl = request.META.get('HTTP_X_FORWARDED_SSL')

        # Устанавливаем HTTPS если прокси передал соответствующие заголовки
        if forwarded_proto == 'https' or forwarded_ssl == 'on':
            request.META['wsgi.url_scheme'] = 'https'

        # Обрабатываем X-Forwarded-For для получения реального IP
        forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if forwarded_for:
            # Берем первый IP из списка (реальный клиент)
            real_ip = forwarded_for.split(',')[0].strip()
            request.META['REMOTE_ADDR'] = real_ip

        # Обрабатываем X-Real-IP
        real_ip = request.META.get('HTTP_X_REAL_IP')
        if real_ip:
            request.META['REMOTE_ADDR'] = real_ip

        return None
