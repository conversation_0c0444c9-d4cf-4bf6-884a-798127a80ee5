"""
Celery configuration for vpn_service project.
"""
import os
from celery import Celery
from django.conf import settings

# Set the default Django settings module for the 'celery' program.
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'vpn_service.settings')

app = Celery('vpn_service')

# Using a string here means the worker doesn't have to serialize
# the configuration object to child processes.
app.config_from_object('django.conf:settings', namespace='CELERY')

# Load task modules from all registered Django apps.
app.autodiscover_tasks()

# Celery Beat Schedule
app.conf.beat_schedule = {
    'sync-traffic-statistics': {
        'task': 'vpn.tasks.sync_traffic_statistics',
        'schedule': 300.0,  # Every 5 minutes
    },
    'cleanup-expired-subscriptions': {
        'task': 'subscriptions.tasks.cleanup_expired_subscriptions',
        'schedule': 3600.0,  # Every hour
    },
    'cleanup-inactive-devices': {
        'task': 'accounts.tasks.cleanup_inactive_devices',
        'schedule': 86400.0,  # Every day
    },
}

app.conf.timezone = 'UTC'


@app.task(bind=True)
def debug_task(self):
    print(f'Request: {self.request!r}')
