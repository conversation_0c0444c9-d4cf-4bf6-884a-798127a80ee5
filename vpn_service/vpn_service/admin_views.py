"""
Кастомные представления для админ-панели VPN-сервиса.
"""
from django.shortcuts import render
from django.contrib.admin.views.decorators import staff_member_required
from django.db.models import Count, Q
from django.utils import timezone
from datetime import timedelta


@staff_member_required
def admin_dashboard(request):
    """
    Главный дашборд админ-панели с основной статистикой.
    
    PURPOSE:
      - Предоставляет обзор ключевых метрик VPN-сервиса
      - Показывает статистику пользователей, подписок и промокодов
      - Обеспечивает быстрый доступ к важной информации
    
    AAG (Actor -> Action -> Goal):
      - Администратор -> Просматривает дашборд -> Получает обзор состояния сервиса
      - Менеджер -> Анализирует метрики -> Принимает бизнес-решения
    
    CONTRACT:
      PRECONDITIONS:
        - request: HTTP запрос от авторизованного администратора
      POSTCONDITIONS:
        - Возвращается HTML страница с дашбордом и статистикой
      INVARIANTS:
        - Все метрики рассчитываются в реальном времени
        - Доступ только для staff пользователей
    
    ARGS:
      - request: HTTP запрос
    
    RETURNS:
      - HttpResponse: HTML страница с дашбордом
    """
    # Импорты моделей
    from accounts.models import UserAccount, UserDevice
    from subscriptions.models import ActiveSubscription
    from promo.models import PromoCode
    from vpn.models import Location
    
    # Временные рамки для статистики
    now = timezone.now()
    last_30_days = now - timedelta(days=30)
    last_7_days = now - timedelta(days=7)
    
    # Статистика пользователей
    total_users = UserAccount.objects.count()
    anonymous_users = UserAccount.objects.filter(is_anonymous=True).count()
    registered_users = total_users - anonymous_users
    active_users = UserAccount.objects.filter(is_active=True).count()
    new_users_30d = UserAccount.objects.filter(date_joined__gte=last_30_days).count()
    new_users_7d = UserAccount.objects.filter(date_joined__gte=last_7_days).count()
    
    # Статистика устройств
    total_devices = UserDevice.objects.count()
    active_devices = UserDevice.objects.filter(is_active=True).count()
    devices_last_7d = UserDevice.objects.filter(last_seen__gte=last_7_days).count()
    
    # Статистика подписок
    total_subscriptions = ActiveSubscription.objects.count()
    active_subscriptions = ActiveSubscription.objects.filter(is_active=True).count()
    expiring_soon = ActiveSubscription.objects.filter(
        is_active=True,
        end_date__lte=now + timedelta(days=7)
    ).count()
    
    # Статистика по планам
    subscription_plans_stats = ActiveSubscription.objects.filter(
        is_active=True
    ).values('plan__name').annotate(
        count=Count('id')
    ).order_by('-count')[:5]
    
    # Статистика промокодов
    total_promo_codes = PromoCode.objects.count()
    activated_promo_codes = PromoCode.objects.filter(is_activated=True).count()
    promo_codes_30d = PromoCode.objects.filter(
        activated_at__gte=last_30_days,
        is_activated=True
    ).count()
    unused_promo_codes = PromoCode.objects.filter(is_activated=False).count()
    
    # Статистика локаций
    total_locations = Location.objects.count()
    active_locations = Location.objects.filter(is_active=True).count()
    
    # Топ локации по странам
    location_stats = Location.objects.filter(
        is_active=True
    ).values('country_code').annotate(
        count=Count('id')
    ).order_by('-count')[:10]
    
    # Недавняя активность
    recent_registrations = UserAccount.objects.filter(
        date_joined__gte=last_7_days
    ).order_by('-date_joined')[:5]
    
    recent_activations = PromoCode.objects.filter(
        activated_at__gte=last_7_days,
        is_activated=True
    ).select_related('activated_by', 'plan').order_by('-activated_at')[:5]
    
    context = {
        'title': 'VPN Service Dashboard',
        
        # Статистика пользователей
        'total_users': total_users,
        'anonymous_users': anonymous_users,
        'registered_users': registered_users,
        'active_users': active_users,
        'new_users_30d': new_users_30d,
        'new_users_7d': new_users_7d,
        
        # Статистика устройств
        'total_devices': total_devices,
        'active_devices': active_devices,
        'devices_last_7d': devices_last_7d,
        
        # Статистика подписок
        'total_subscriptions': total_subscriptions,
        'active_subscriptions': active_subscriptions,
        'expiring_soon': expiring_soon,
        'subscription_plans_stats': subscription_plans_stats,
        
        # Статистика промокодов
        'total_promo_codes': total_promo_codes,
        'activated_promo_codes': activated_promo_codes,
        'promo_codes_30d': promo_codes_30d,
        'unused_promo_codes': unused_promo_codes,
        
        # Статистика локаций
        'total_locations': total_locations,
        'active_locations': active_locations,
        'location_stats': location_stats,
        
        # Недавняя активность
        'recent_registrations': recent_registrations,
        'recent_activations': recent_activations,
    }
    
    return render(request, 'admin/dashboard.html', context)
