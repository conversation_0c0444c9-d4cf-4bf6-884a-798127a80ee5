"""
Health check views for production monitoring.

PURPOSE:
  - Предоставляет endpoints для мониторинга состояния системы
  - Проверяет доступность критических компонентов
  - Обеспечивает быструю диагностику проблем

AAG (Actor -> Action -> Goal):
  - Мониторинг системы -> Проверяет health endpoints -> Получает статус системы
  - Load balancer -> Проверяет health -> Направляет трафик на здоровые инстансы
"""

import json
import time
from django.http import JsonResponse, HttpResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.cache import never_cache
from django.core.cache import cache
from django.db import connection
from django.conf import settings
import logging

logger = logging.getLogger(__name__)


@require_http_methods(["GET"])
@never_cache
def health_check(request):
    """
    Базовая проверка здоровья системы.
    
    PURPOSE:
      - Быстрая проверка доступности API
      - Используется load balancer'ами и мониторингом
      - Минимальная нагрузка на систему
    
    CONTRACT:
      POSTCONDITIONS:
        - Возвращает HTTP 200 если система работает
        - Возвращает HTTP 503 если есть критические проблемы
    """
    try:
        # Проверка базы данных
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            cursor.fetchone()
        
        return JsonResponse({
            'status': 'healthy',
            'timestamp': time.time(),
            'version': getattr(settings, 'API_VERSION', '1.0.0')
        })
    
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return JsonResponse({
            'status': 'unhealthy',
            'error': 'Database connection failed',
            'timestamp': time.time()
        }, status=503)


@require_http_methods(["GET"])
@never_cache
def health_detailed(request):
    """
    Детальная проверка здоровья системы.
    
    PURPOSE:
      - Подробная диагностика всех компонентов
      - Используется для глубокого мониторинга
      - Проверяет все критические зависимости
    """
    health_data = {
        'status': 'healthy',
        'timestamp': time.time(),
        'version': getattr(settings, 'API_VERSION', '1.0.0'),
        'checks': {}
    }
    
    overall_healthy = True
    
    # Проверка базы данных
    try:
        start_time = time.time()
        with connection.cursor() as cursor:
            cursor.execute("SELECT COUNT(*) FROM django_migrations")
            migrations_count = cursor.fetchone()[0]
        
        db_time = (time.time() - start_time) * 1000
        health_data['checks']['database'] = {
            'status': 'healthy',
            'response_time_ms': round(db_time, 2),
            'migrations_count': migrations_count
        }
    except Exception as e:
        overall_healthy = False
        health_data['checks']['database'] = {
            'status': 'unhealthy',
            'error': str(e)
        }
    
    # Проверка Redis/Cache
    try:
        start_time = time.time()
        cache_key = 'health_check_test'
        cache.set(cache_key, 'test_value', 10)
        cached_value = cache.get(cache_key)
        cache.delete(cache_key)
        
        cache_time = (time.time() - start_time) * 1000
        
        if cached_value == 'test_value':
            health_data['checks']['cache'] = {
                'status': 'healthy',
                'response_time_ms': round(cache_time, 2)
            }
        else:
            overall_healthy = False
            health_data['checks']['cache'] = {
                'status': 'unhealthy',
                'error': 'Cache read/write test failed'
            }
    except Exception as e:
        overall_healthy = False
        health_data['checks']['cache'] = {
            'status': 'unhealthy',
            'error': str(e)
        }
    
    # Проверка Hiddify API (опционально, может быть медленной)
    if request.GET.get('include_external') == 'true':
        try:
            from vpn.services import HiddifyApiService
            start_time = time.time()
            
            hiddify_service = HiddifyApiService()
            # Простая проверка доступности без создания пользователей
            success = hiddify_service._make_request('GET', '/admin/user/', {})
            
            hiddify_time = (time.time() - start_time) * 1000
            
            if success:
                health_data['checks']['hiddify_api'] = {
                    'status': 'healthy',
                    'response_time_ms': round(hiddify_time, 2)
                }
            else:
                health_data['checks']['hiddify_api'] = {
                    'status': 'degraded',
                    'warning': 'Hiddify API not responding'
                }
        except Exception as e:
            health_data['checks']['hiddify_api'] = {
                'status': 'unhealthy',
                'error': str(e)
            }
    
    # Общий статус
    if not overall_healthy:
        health_data['status'] = 'unhealthy'
        return JsonResponse(health_data, status=503)
    
    return JsonResponse(health_data)


@require_http_methods(["GET"])
@never_cache
def readiness_check(request):
    """
    Проверка готовности к обслуживанию запросов.
    
    PURPOSE:
      - Проверяет готовность системы принимать трафик
      - Используется Kubernetes и другими оркестраторами
      - Более строгая проверка чем liveness
    """
    try:
        # Проверка критических компонентов
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            cursor.fetchone()
        
        # Проверка кэша
        cache.set('readiness_test', 'ok', 5)
        if cache.get('readiness_test') != 'ok':
            raise Exception("Cache not working")
        cache.delete('readiness_test')
        
        return HttpResponse("OK", content_type="text/plain")
    
    except Exception as e:
        logger.error(f"Readiness check failed: {str(e)}")
        return HttpResponse("NOT READY", status=503, content_type="text/plain")


@require_http_methods(["GET"])
@never_cache
def liveness_check(request):
    """
    Проверка жизнеспособности процесса.
    
    PURPOSE:
      - Проверяет что процесс Django жив
      - Используется для автоматического перезапуска
      - Минимальная проверка без внешних зависимостей
    """
    return HttpResponse("ALIVE", content_type="text/plain")
