#!/bin/bash

# VPN Service Quick Start Script
# This script helps you quickly set up and run the VPN service

set -e

echo "🚀 VPN Service Quick Start"
echo "=========================="

# Check if .env file exists
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "⚠️  Please edit .env file with your actual configuration before proceeding!"
    echo "   Especially set your HIDDIFY_ADMIN_API_KEY"
    read -p "Press Enter when you've configured .env file..."
fi

# Check if we're in Docker environment
if [ "$1" = "docker" ]; then
    echo "🐳 Starting with Docker Compose..."
    
    # Build and start services
    docker-compose up -d --build
    
    echo "⏳ Waiting for services to start..."
    sleep 10
    
    # Run migrations
    echo "📊 Running database migrations..."
    docker-compose exec web python manage.py migrate
    
    # Create superuser
    echo "👤 Creating superuser..."
    docker-compose exec web python manage.py createsuperuser --noinput --email <EMAIL> || true
    
    # Setup sample data
    echo "📋 Setting up sample data..."
    docker-compose exec web python setup.py
    
    echo "✅ Docker setup complete!"
    echo "🌐 API available at: http://localhost:8000"
    echo "🔧 Admin panel at: http://localhost:8000/admin"
    echo "📊 View logs: docker-compose logs -f"
    
else
    echo "💻 Starting local development setup..."
    
    # Check if virtual environment exists
    if [ ! -d "venv" ]; then
        echo "🐍 Creating virtual environment..."
        python -m venv venv
    fi
    
    # Activate virtual environment
    echo "🔄 Activating virtual environment..."
    source venv/bin/activate
    
    # Install dependencies
    echo "📦 Installing dependencies..."
    pip install -r requirements.txt
    
    # Run setup
    echo "⚙️  Running setup script..."
    python setup.py
    
    echo "✅ Local setup complete!"
    echo ""
    echo "🚀 To start the development server:"
    echo "   1. Start Redis: redis-server"
    echo "   2. Start PostgreSQL service"
    echo "   3. Run Django: python manage.py runserver"
    echo "   4. Run Celery worker: celery -A vpn_service worker -l info"
    echo "   5. Run Celery beat: celery -A vpn_service beat -l info"
    echo ""
    echo "🌐 API will be available at: http://localhost:8000"
    echo "🔧 Admin panel at: http://localhost:8000/admin"
fi

echo ""
echo "📚 API Endpoints:"
echo "   POST /api/auth/register/     - Register new user"
echo "   POST /api/auth/login/        - Login user"
echo "   GET  /api/vpn/config/        - Get VPN config"
echo "   GET  /api/vpn/stats/         - Get traffic stats"
echo "   GET  /api/subscriptions/plans/ - Get subscription plans"
echo ""
echo "🧪 Test the API:"
echo "   curl -X POST http://localhost:8000/api/auth/register/ \\"
echo "     -H 'Content-Type: application/json' \\"
echo "     -d '{\"email\":\"<EMAIL>\",\"password\":\"test123456\",\"password_confirm\":\"test123456\",\"device_id\":\"test-device\"}'"
echo ""
echo "🎉 VPN Service is ready to use!"
