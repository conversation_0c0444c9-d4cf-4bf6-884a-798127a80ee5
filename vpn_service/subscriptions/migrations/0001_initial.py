# Generated by Django 4.2.7 on 2025-06-01 11:02

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SubscriptionPlan',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True)),
                ('price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('currency', models.CharField(default='USD', max_length=3)),
                ('duration_days', models.PositiveIntegerField()),
                ('traffic_limit_gb', models.PositiveIntegerField()),
                ('max_devices', models.PositiveIntegerField(default=3)),
                ('is_active', models.BooleanField(default=True)),
                ('is_trial', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'db_table': 'subscription_plans',
                'ordering': ['price'],
            },
        ),
        migrations.CreateModel(
            name='ActiveSubscription',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('start_date', models.DateTimeField()),
                ('end_date', models.DateTimeField()),
                ('is_active', models.BooleanField(default=True)),
                ('auto_renew', models.BooleanField(default=False)),
                ('payment_method', models.CharField(blank=True, max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('plan', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='subscriptions.subscriptionplan')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='subscriptions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'active_subscriptions',
            },
        ),
        migrations.CreateModel(
            name='PaymentTransaction',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('currency', models.CharField(default='USD', max_length=3)),
                ('payment_method', models.CharField(max_length=50)),
                ('payment_provider', models.CharField(max_length=50)),
                ('external_transaction_id', models.CharField(blank=True, max_length=255)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('completed', 'Completed'), ('failed', 'Failed'), ('refunded', 'Refunded')], default='pending', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('plan', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='subscriptions.subscriptionplan')),
                ('subscription', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='transactions', to='subscriptions.activesubscription')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transactions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'payment_transactions',
                'indexes': [models.Index(fields=['user', 'status'], name='payment_tra_user_id_131e27_idx'), models.Index(fields=['external_transaction_id'], name='payment_tra_externa_a7becb_idx')],
            },
        ),
        migrations.AddIndex(
            model_name='activesubscription',
            index=models.Index(fields=['user', 'is_active'], name='active_subs_user_id_af3d00_idx'),
        ),
        migrations.AddIndex(
            model_name='activesubscription',
            index=models.Index(fields=['end_date'], name='active_subs_end_dat_323c21_idx'),
        ),
    ]
