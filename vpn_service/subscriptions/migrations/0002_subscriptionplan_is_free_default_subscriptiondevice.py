# Generated by Django 4.2.7 on 2025-06-19 11:15

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0006_userdevice_device_secret_usersession'),
        ('subscriptions', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='subscriptionplan',
            name='is_free_default',
            field=models.BooleanField(default=False, help_text='Тариф по умолчанию для анонимных пользователей'),
        ),
        migrations.CreateModel(
            name='SubscriptionDevice',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('device', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='subscriptions', to='accounts.userdevice')),
                ('subscription', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='devices', to='subscriptions.activesubscription')),
            ],
            options={
                'db_table': 'subscription_devices',
                'indexes': [models.Index(fields=['subscription'], name='subscriptio_subscri_19779f_idx'), models.Index(fields=['device'], name='subscriptio_device__d65c00_idx')],
                'unique_together': {('subscription', 'device')},
            },
        ),
    ]
