"""
Subscription models for VPN service.
"""
from django.db import models
import uuid


class SubscriptionPlan(models.Model):
    """
    Модель тарифных планов VPN-сервиса.
    
    PURPOSE:
      - Определяет доступные тарифы с их характеристиками
      - Контролирует лимиты трафика и устройств
      - Управляет ценообразованием
    
    AAG (Actor -> Action -> Goal):
      - Администратор -> Создает тарифы -> Предлагает варианты пользователям
      - Пользователь -> Выбирает тариф -> Получает соответствующие лимиты
    
    CONTRACT:
      PRECONDITIONS:
        - name (str): Уникальное название тарифа
        - price (Decimal): Цена в основной валюте
        - duration_days (int): Длительность в днях
      POSTCONDITIONS:
        - Создается тарифный план для использования
        - Устанавливаются лимиты для подписок
      INVARIANTS:
        - price >= 0
        - duration_days > 0
        - max_devices > 0
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    price = models.DecimalField(max_digits=10, decimal_places=2)
    currency = models.CharField(max_length=3, default='USD')
    duration_days = models.PositiveIntegerField()
    traffic_limit_gb = models.PositiveIntegerField()  # Лимит трафика в ГБ
    max_devices = models.PositiveIntegerField(default=3)
    is_active = models.BooleanField(default=True)
    is_trial = models.BooleanField(default=False)
    is_free_default = models.BooleanField(default=False, help_text="Тариф по умолчанию для анонимных пользователей")
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'subscription_plans'
        ordering = ['price']

    def __str__(self):
        return f"{self.name} - {self.price} {self.currency}"


class ActiveSubscription(models.Model):
    """
    Модель активных подписок пользователей.
    
    PURPOSE:
      - Отслеживает текущие активные подписки пользователей
      - Контролирует сроки действия и автопродление
      - Связывает пользователей с их тарифными планами
    
    AAG (Actor -> Action -> Goal):
      - Пользователь -> Оплачивает подписку -> Получает VPN-доступ на период
      - Система -> Проверяет подписки -> Контролирует доступ к сервису
    
    CONTRACT:
      PRECONDITIONS:
        - user (UserAccount): Существующий пользователь
        - plan (SubscriptionPlan): Активный тарифный план
        - start_date (datetime): Дата начала подписки
      POSTCONDITIONS:
        - Создается активная подписка с определенным сроком
        - Пользователь получает доступ согласно тарифу
      INVARIANTS:
        - end_date > start_date
        - Только одна активная подписка на пользователя
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey('accounts.UserAccount', on_delete=models.CASCADE, related_name='subscriptions')
    plan = models.ForeignKey(SubscriptionPlan, on_delete=models.PROTECT)
    start_date = models.DateTimeField()
    end_date = models.DateTimeField()
    is_active = models.BooleanField(default=True)
    auto_renew = models.BooleanField(default=False)
    payment_method = models.CharField(max_length=50, blank=True)  # "card", "crypto", etc.
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'active_subscriptions'
        indexes = [
            models.Index(fields=['user', 'is_active']),
            models.Index(fields=['end_date']),
        ]

    def __str__(self):
        return f"{self.user.email} - {self.plan.name}"

    @property
    def is_expired(self):
        """Проверяет, истекла ли подписка."""
        from django.utils import timezone
        return timezone.now() > self.end_date

    @property
    def days_remaining(self):
        """Возвращает количество дней до истечения подписки."""
        from django.utils import timezone
        if self.is_expired:
            return 0
        delta = self.end_date - timezone.now()
        return delta.days


class PaymentTransaction(models.Model):
    """
    Модель транзакций оплаты.
    
    PURPOSE:
      - Отслеживает все платежные операции
      - Обеспечивает аудит финансовых операций
      - Связывает платежи с подписками
    """
    PAYMENT_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('refunded', 'Refunded'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey('accounts.UserAccount', on_delete=models.CASCADE, related_name='transactions')
    subscription = models.ForeignKey(ActiveSubscription, on_delete=models.CASCADE, related_name='transactions', null=True, blank=True)
    plan = models.ForeignKey(SubscriptionPlan, on_delete=models.PROTECT)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    currency = models.CharField(max_length=3, default='USD')
    payment_method = models.CharField(max_length=50)
    payment_provider = models.CharField(max_length=50)  # "stripe", "paypal", etc.
    external_transaction_id = models.CharField(max_length=255, blank=True)
    status = models.CharField(max_length=20, choices=PAYMENT_STATUS_CHOICES, default='pending')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'payment_transactions'
        indexes = [
            models.Index(fields=['user', 'status']),
            models.Index(fields=['external_transaction_id']),
        ]

    def __str__(self):
        return f"{self.user.email} - {self.amount} {self.currency} - {self.status}"


class SubscriptionDevice(models.Model):
    """
    Промежуточная модель для связи подписок с устройствами (M2M).

    PURPOSE:
      - Связывает активные подписки с конкретными устройствами
      - Позволяет отслеживать, какие устройства используют подписку
      - Обеспечивает контроль лимитов устройств

    AAG (Actor -> Action -> Goal):
      - Система -> Связывает подписку с устройством -> Обеспечивает VPN-доступ
      - Администратор -> Просматривает связи -> Контролирует использование

    CONTRACT:
      PRECONDITIONS:
        - subscription (ActiveSubscription): Активная подписка
        - device (UserDevice): Устройство пользователя
      POSTCONDITIONS:
        - Создается связь между подпиской и устройством
        - Устройство получает доступ к VPN согласно подписке
      INVARIANTS:
        - Комбинация subscription + device уникальна
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    subscription = models.ForeignKey(ActiveSubscription, on_delete=models.CASCADE, related_name='devices')
    device = models.ForeignKey('accounts.UserDevice', on_delete=models.CASCADE, related_name='subscriptions')
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'subscription_devices'
        unique_together = ['subscription', 'device']
        indexes = [
            models.Index(fields=['subscription']),
            models.Index(fields=['device']),
        ]

    def __str__(self):
        return f"{self.subscription.plan.name} -> {self.device.device_name or self.device.device_id}"
