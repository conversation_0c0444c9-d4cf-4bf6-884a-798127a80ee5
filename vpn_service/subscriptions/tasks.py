"""
Celery tasks for subscriptions app.
"""
from celery import shared_task
from django.utils import timezone
import logging

logger = logging.getLogger(__name__)


@shared_task
def cleanup_expired_subscriptions():
    """
    Деактивирует истекшие подписки и соответствующие Hiddify аккаунты.
    
    PURPOSE:
      - Автоматически деактивирует истекшие подписки
      - Приостанавливает VPN-доступ для неоплаченных аккаунтов
      - Поддерживает синхронизацию между Django и Hiddify
    """
    from .models import ActiveSubscription
    from accounts.models import HiddifyLink
    from vpn.services import HiddifyApiService
    
    expired_subscriptions = ActiveSubscription.objects.filter(
        is_active=True,
        end_date__lt=timezone.now()
    )
    
    hiddify_service = HiddifyApiService()
    deactivated_count = 0
    
    for subscription in expired_subscriptions:
        try:
            # Деактивируем подписку
            subscription.is_active = False
            subscription.save()
            
            # Деактивируем соответствующий Hiddify аккаунт
            try:
                hiddify_link = subscription.user.hiddify_link
                
                # Обновляем статус в Hiddify (приостанавливаем доступ)
                success, response = hiddify_service.update_hiddify_user(
                    str(hiddify_link.hiddify_user_uuid),
                    {'is_active': False}
                )
                
                if success:
                    hiddify_link.is_active_in_hiddify = False
                    hiddify_link.save()
                    deactivated_count += 1
                    logger.info(f"Deactivated subscription and Hiddify user for {subscription.user.email}")
                else:
                    logger.error(f"Failed to deactivate Hiddify user for subscription {subscription.id}: {response}")
                
            except HiddifyLink.DoesNotExist:
                logger.warning(f"No Hiddify link found for user {subscription.user.email}")
                deactivated_count += 1
                
        except Exception as e:
            logger.error(f"Failed to deactivate subscription {subscription.id}: {e}")
    
    logger.info(f"Processed {expired_subscriptions.count()} expired subscriptions, deactivated {deactivated_count}")
    return f"Processed {expired_subscriptions.count()} expired subscriptions, deactivated {deactivated_count}"


@shared_task
def send_expiration_warnings():
    """
    Отправляет предупреждения о скором истечении подписок.
    
    PURPOSE:
      - Уведомляет пользователей о необходимости продления
      - Снижает отток пользователей
      - Улучшает пользовательский опыт
    """
    from .models import ActiveSubscription
    from datetime import timedelta
    from django.core.mail import send_mail
    from django.conf import settings
    
    # Подписки, истекающие через 3 дня
    warning_date = timezone.now() + timedelta(days=3)
    expiring_subscriptions = ActiveSubscription.objects.filter(
        is_active=True,
        end_date__lte=warning_date,
        end_date__gt=timezone.now()
    )
    
    sent_count = 0
    
    for subscription in expiring_subscriptions:
        try:
            subject = "Your VPN subscription is expiring soon"
            message = f"""
            Dear {subscription.user.email},
            
            Your VPN subscription ({subscription.plan.name}) will expire on {subscription.end_date.strftime('%Y-%m-%d %H:%M')}.
            
            Please renew your subscription to continue enjoying our VPN service.
            
            Best regards,
            VPN Service Team
            """
            
            send_mail(
                subject,
                message,
                settings.DEFAULT_FROM_EMAIL,
                [subscription.user.email],
                fail_silently=False,
            )
            
            sent_count += 1
            logger.info(f"Expiration warning sent to {subscription.user.email}")
            
        except Exception as e:
            logger.error(f"Failed to send expiration warning to {subscription.user.email}: {e}")
    
    logger.info(f"Sent {sent_count} expiration warnings")
    return f"Sent {sent_count} expiration warnings"


@shared_task
def process_payment_webhook(transaction_id, status, external_id=None):
    """
    Обрабатывает webhook от платежной системы.
    
    PURPOSE:
      - Обновляет статус платежных транзакций
      - Активирует подписки при успешной оплате
      - Обеспечивает синхронизацию с платежными провайдерами
    """
    from .models import PaymentTransaction, ActiveSubscription
    from accounts.models import HiddifyLink
    from vpn.services import HiddifyApiService
    from datetime import timedelta
    
    try:
        transaction = PaymentTransaction.objects.get(id=transaction_id)
        old_status = transaction.status
        
        # Обновляем статус транзакции
        transaction.status = status
        if external_id:
            transaction.external_transaction_id = external_id
        transaction.save()
        
        # Если платеж успешен и статус изменился
        if status == 'completed' and old_status != 'completed':
            # Активируем или продлеваем подписку
            if transaction.subscription:
                subscription = transaction.subscription
                subscription.is_active = True
                
                # Если подписка истекла, продлеваем от текущей даты
                if subscription.is_expired:
                    subscription.start_date = timezone.now()
                    subscription.end_date = timezone.now() + timedelta(days=subscription.plan.duration_days)
                else:
                    # Продлеваем от даты окончания
                    subscription.end_date += timedelta(days=subscription.plan.duration_days)
                
                subscription.save()
                
                # Активируем Hiddify пользователя
                try:
                    hiddify_link = subscription.user.hiddify_link
                    hiddify_service = HiddifyApiService()
                    
                    success, response = hiddify_service.update_hiddify_user(
                        str(hiddify_link.hiddify_user_uuid),
                        {
                            'is_active': True,
                            'usage_limit_GB': subscription.plan.traffic_limit_gb,
                            'package_days': subscription.plan.duration_days
                        }
                    )
                    
                    if success:
                        hiddify_link.is_active_in_hiddify = True
                        hiddify_link.traffic_limit_bytes = subscription.plan.traffic_limit_gb * 1024 * 1024 * 1024
                        hiddify_link.save()
                        
                        logger.info(f"Activated subscription and Hiddify user for {subscription.user.email}")
                    else:
                        logger.error(f"Failed to activate Hiddify user for transaction {transaction_id}: {response}")
                        
                except HiddifyLink.DoesNotExist:
                    logger.error(f"No Hiddify link found for user {subscription.user.email}")
        
        logger.info(f"Processed payment webhook for transaction {transaction_id}: {status}")
        return f"Processed payment webhook for transaction {transaction_id}: {status}"
        
    except PaymentTransaction.DoesNotExist:
        logger.error(f"Transaction {transaction_id} not found")
        return f"Transaction {transaction_id} not found"
    except Exception as e:
        logger.error(f"Failed to process payment webhook for transaction {transaction_id}: {e}")
        return f"Failed to process payment webhook: {e}"
