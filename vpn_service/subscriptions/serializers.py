"""
Serializers for subscriptions app.
"""
from rest_framework import serializers
from drf_spectacular.utils import extend_schema_serializer, OpenApiExample
from .models import SubscriptionPlan, ActiveSubscription, PaymentTransaction


class SubscriptionPlanSerializer(serializers.ModelSerializer):
    """
    Сериализатор для тарифных планов.

    PURPOSE:
      - Отображает информацию о доступных тарифах
      - Скрывает внутренние поля от публичного API
      - Форматирует данные для клиентских приложений
    """

    class Meta:
        model = SubscriptionPlan
        fields = (
            'id', 'name', 'description', 'price', 'currency',
            'duration_days', 'traffic_limit_gb', 'max_devices', 'is_trial'
        )


@extend_schema_serializer(
    examples=[
        OpenApiExample(
            'Promo Code Activation',
            value={
                'promo_code': 'PREMIUM2024'
            }
        )
    ]
)
class SubscriptionActivationSerializer(serializers.Serializer):
    """
    Сериализатор для активации подписки через промокод.

    PURPOSE:
      - Валидирует данные для активации промокода
      - Обеспечивает правильный формат входных данных
      - Используется в эндпоинте POST /api/subscriptions/activate/
    """
    promo_code = serializers.CharField(
        max_length=16,
        help_text="Промокод для активации подписки"
    )

    def validate_promo_code(self, value):
        """Валидация промокода."""
        if not value or len(value.strip()) == 0:
            raise serializers.ValidationError("Промокод не может быть пустым")

        # Нормализуем код (приводим к верхнему регистру и убираем пробелы)
        return value.strip().upper()


class SubscriptionInfoSerializer(serializers.Serializer):
    """
    Сериализатор для информации о подписке в ответе активации.

    PURPOSE:
      - Предоставляет информацию о активированной подписке
      - Включает данные о тарифном плане и сроках действия
      - Используется в ответе успешной активации промокода
    """
    plan_name = serializers.CharField(read_only=True)
    end_date = serializers.DateTimeField(read_only=True)
    traffic_limit_gb = serializers.IntegerField(read_only=True)
    duration_days = serializers.IntegerField(read_only=True)
    is_active = serializers.BooleanField(read_only=True)


@extend_schema_serializer(
    examples=[
        OpenApiExample(
            'Successful Activation',
            value={
                'success': True,
                'message': 'Subscription activated successfully!',
                'subscription': {
                    'plan_name': 'Premium',
                    'end_date': '2024-07-01T12:00:00Z',
                    'traffic_limit_gb': 100,
                    'duration_days': 30,
                    'is_active': True
                }
            }
        )
    ]
)
class SubscriptionActivationResponseSerializer(serializers.Serializer):
    """
    Сериализатор для ответа активации подписки.

    PURPOSE:
      - Стандартизирует формат ответа при активации промокода
      - Включает информацию о подписке и статусе операции
      - Обеспечивает консистентность API ответов
    """
    success = serializers.BooleanField(read_only=True)
    message = serializers.CharField(read_only=True)
    subscription = SubscriptionInfoSerializer(read_only=True)


class SubscriptionErrorResponseSerializer(serializers.Serializer):
    """
    Сериализатор для ошибок активации подписки.

    PURPOSE:
      - Стандартизирует формат ошибок при активации промокодов
      - Предоставляет детальную информацию об ошибках
      - Включает коды ошибок для программной обработки
    """
    error = serializers.CharField(read_only=True)
    code = serializers.CharField(read_only=True)
    details = serializers.DictField(read_only=True, required=False)


class ActiveSubscriptionSerializer(serializers.ModelSerializer):
    """
    Сериализатор для активных подписок.
    
    PURPOSE:
      - Отображает информацию о текущей подписке пользователя
      - Включает данные о тарифном плане
      - Показывает статус и сроки действия
    """
    plan = SubscriptionPlanSerializer(read_only=True)
    days_remaining = serializers.ReadOnlyField()
    is_expired = serializers.ReadOnlyField()
    
    class Meta:
        model = ActiveSubscription
        fields = (
            'id', 'plan', 'start_date', 'end_date', 'is_active', 
            'auto_renew', 'days_remaining', 'is_expired', 'created_at'
        )


class PaymentTransactionSerializer(serializers.ModelSerializer):
    """
    Сериализатор для платежных транзакций.
    
    PURPOSE:
      - Отображает информацию о платежах пользователя
      - Обеспечивает аудит финансовых операций
      - Скрывает чувствительную информацию
    """
    plan = SubscriptionPlanSerializer(read_only=True)
    
    class Meta:
        model = PaymentTransaction
        fields = (
            'id', 'plan', 'amount', 'currency', 'payment_method', 
            'status', 'created_at', 'updated_at'
        )
        # Скрываем чувствительные поля
        read_only_fields = ('external_transaction_id', 'payment_provider')


class PurchaseRequestSerializer(serializers.Serializer):
    """
    Сериализатор для запроса покупки подписки.

    PURPOSE:
      - Валидирует данные запроса на покупку
      - Обеспечивает типизацию для OpenAPI схемы
      - Проверяет корректность входных параметров
    """
    plan_id = serializers.UUIDField(help_text="ID тарифного плана")
    payment_method = serializers.ChoiceField(
        choices=['card', 'crypto', 'paypal'],
        default='card',
        help_text="Метод оплаты"
    )


class PurchaseResponseSerializer(serializers.Serializer):
    """
    Сериализатор для ответа на запрос покупки.

    PURPOSE:
      - Стандартизирует ответ API покупки
      - Предоставляет информацию о транзакции
      - Включает ссылки для завершения платежа
    """
    success = serializers.BooleanField()
    transaction_id = serializers.UUIDField()
    payment_url = serializers.URLField(allow_null=True)
    message = serializers.CharField()


class CurrentSubscriptionResponseSerializer(serializers.Serializer):
    """
    Сериализатор для ответа с информацией о текущей подписке.

    PURPOSE:
      - Стандартизирует ответ API текущей подписки
      - Включает полную информацию о подписке и статистике
      - Обеспечивает типизацию для OpenAPI схемы
    """
    success = serializers.BooleanField()
    subscription = ActiveSubscriptionSerializer()
    usage_stats = serializers.DictField(allow_null=True)
