#!/usr/bin/env python
"""
Скрипт для аудита готовности VPN-сервиса.
"""
import os
import sys
import django

# Настройка Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'vpn_service.settings')
django.setup()

def check_free_default_plan():
    """Проверяет наличие бесплатного тарифа по умолчанию."""
    from subscriptions.models import SubscriptionPlan
    
    print("=== 1.2. Проверка тарифа по умолчанию ===")
    
    free_plan = SubscriptionPlan.objects.filter(is_free_default=True).first()
    if free_plan:
        print(f'✅ Найден бесплатный тариф по умолчанию: {free_plan.name}')
        print(f'   - ID: {free_plan.id}')
        print(f'   - Цена: {free_plan.price} {free_plan.currency}')
        print(f'   - Длительность: {free_plan.duration_days} дней')
        print(f'   - Лимит трафика: {free_plan.traffic_limit_gb} GB')
        print(f'   - Максимум устройств: {free_plan.max_devices}')
        print(f'   - Активен: {free_plan.is_active}')
        return True
    else:
        print('❌ КРИТИЧЕСКАЯ ПРОБЛЕМА: Не найден тариф с is_free_default=True')
        print('   Это блокирует работу эндпоинта register_device')
        return False
    
def check_hiddify_config():
    """Проверяет конфигурацию HiddifyApiService."""
    from django.conf import settings
    
    print("\n=== 1.1. Проверка конфигурации HiddifyApiService ===")
    
    # Проверяем настройки
    required_settings = [
        'HIDDIFY_ADMIN_API_KEY',
        'HIDDIFY_ADMIN_BASE_URL', 
        'HIDDIFY_USER_BASE_URL'
    ]
    
    issues = []
    for setting_name in required_settings:
        value = getattr(settings, setting_name, None)
        if not value:
            issues.append(f"❌ {setting_name} не настроен")
        elif 'your-' in str(value).lower():
            issues.append(f"⚠️  {setting_name} содержит placeholder значение: {value}")
        else:
            print(f"✅ {setting_name}: настроен")
    
    if issues:
        print("\nПроблемы с конфигурацией:")
        for issue in issues:
            print(f"   {issue}")
        return False
    
    return True

def check_admin_registrations():
    """Проверяет регистрацию моделей в Django Admin."""
    from django.contrib import admin
    
    print("\n=== 4. Аудит Django Admin ===")
    
    # Модели, которые должны быть зарегистрированы
    expected_models = {
        'accounts': ['UserAccount', 'UserDevice', 'HiddifyLink', 'ActivationCode', 'UserSession'],
        'subscriptions': ['SubscriptionPlan', 'ActiveSubscription', 'PaymentTransaction', 'SubscriptionDevice'],
        'promo': ['PromoCode'],
        'vpn': ['VPNServer', 'ConnectionLog', 'Location', 'SubscriptionPlanLocation']
    }
    
    registered_models = []
    unregistered_models = []
    
    for app_name, model_names in expected_models.items():
        for model_name in model_names:
            try:
                # Импортируем модель
                module = __import__(f'{app_name}.models', fromlist=[model_name])
                model_class = getattr(module, model_name)
                
                # Проверяем регистрацию в админке
                if model_class in admin.site._registry:
                    registered_models.append(f"{app_name}.{model_name}")
                else:
                    unregistered_models.append(f"{app_name}.{model_name}")
                    
            except (ImportError, AttributeError) as e:
                unregistered_models.append(f"{app_name}.{model_name} (ошибка импорта: {e})")
    
    print(f"✅ Зарегистрированные модели ({len(registered_models)}):")
    for model in registered_models:
        print(f"   - {model}")
    
    if unregistered_models:
        print(f"\n❌ НЕ зарегистрированные модели ({len(unregistered_models)}):")
        for model in unregistered_models:
            print(f"   - {model}")
        return False
    else:
        print("\n✅ Все ожидаемые модели зарегистрированы в админке")
        return True

def main():
    """Основная функция аудита."""
    print("🔍 АУДИТ ГОТОВНОСТИ VPN-СЕРВИСА")
    print("=" * 50)
    
    results = []
    
    # 1.1. Конфигурация HiddifyApiService
    results.append(check_hiddify_config())
    
    # 1.2. Наличие тарифа по умолчанию
    results.append(check_free_default_plan())
    
    # 4. Аудит Django Admin
    results.append(check_admin_registrations())
    
    # Итоговый результат
    print("\n" + "=" * 50)
    print("📊 ИТОГОВЫЙ РЕЗУЛЬТАТ АУДИТА")
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"✅ Все проверки пройдены ({passed}/{total})")
        print("🚀 Проект готов к тестированию!")
    else:
        print(f"❌ Проверки не пройдены: {total - passed}/{total}")
        print("⚠️  Необходимо исправить проблемы перед продолжением")
    
    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
