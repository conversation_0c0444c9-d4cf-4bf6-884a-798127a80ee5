"""
Django admin configuration for promo app.
"""
from django.contrib import admin
from django.utils.html import format_html
from django.urls import path, reverse
from django.shortcuts import render, redirect
from django.contrib import messages
from .models import PromoCode


@admin.register(PromoCode)
class PromoCodeAdmin(admin.ModelAdmin):
    """
    Админка для управления промокодами.

    PURPOSE:
      - Предоставляет удобный интерфейс для создания и управления промокодами
      - Отображает статус промокодов и информацию об активации
      - Позволяет фильтровать и искать промокоды

    AAG (Actor -> Action -> Goal):
      - Администратор -> Создает промокоды -> Управляет распространением подписок
      - Администратор -> Просматривает статистику -> Контролирует использование промокодов
    """
    list_display = [
        'code',
        'plan',
        'status_display',
        'activated_by_email',
        'activated_at',
        'expires_at',
        'created_at'
    ]
    list_filter = [
        'is_activated',
        'plan',
        'created_at',
        'activated_at',
        'expires_at'
    ]
    search_fields = [
        'code',
        'activated_by__email'
    ]
    readonly_fields = [
        'id',
        'is_activated',
        'activated_at',
        'activated_by',
        'created_at'
    ]
    ordering = ['-created_at']

    fieldsets = (
        ('Основная информация', {
            'fields': ('code', 'plan', 'expires_at')
        }),
        ('Статус активации', {
            'fields': ('is_activated', 'activated_at', 'activated_by'),
            'classes': ('collapse',)
        }),
        ('Системная информация', {
            'fields': ('id', 'created_at'),
            'classes': ('collapse',)
        }),
    )

    def status_display(self, obj):
        """
        Отображает статус промокода с цветовой индикацией.

        PURPOSE:
          - Визуально показывает статус промокода в списке
          - Упрощает идентификацию активных/использованных/истекших кодов

        ARGS:
          - obj (PromoCode): Объект промокода

        RETURNS:
          - str: HTML с цветовой индикацией статуса
        """
        if obj.is_activated:
            return format_html(
                '<span style="color: #28a745; font-weight: bold;">✓ Активирован</span>'
            )
        elif obj.is_expired():
            return format_html(
                '<span style="color: #dc3545; font-weight: bold;">⏰ Истек</span>'
            )
        else:
            return format_html(
                '<span style="color: #007bff; font-weight: bold;">🔄 Активен</span>'
            )

    status_display.short_description = 'Статус'
    status_display.admin_order_field = 'is_activated'

    def activated_by_email(self, obj):
        """
        Отображает email пользователя, активировавшего промокод.

        PURPOSE:
          - Показывает, кто активировал промокод
          - Упрощает идентификацию пользователей
          - Помогает в аудите использования промокодов

        ARGS:
          - obj (PromoCode): Объект промокода

        RETURNS:
          - str: Email пользователя или "Not activated"
        """
        if obj.activated_by:
            if obj.activated_by.is_anonymous:
                return f"Anonymous {str(obj.activated_by.id)[:8]}"
            return obj.activated_by.email or f"User {str(obj.activated_by.id)[:8]}"
        return "Not activated"

    activated_by_email.short_description = 'Activated By'
    activated_by_email.admin_order_field = 'activated_by__email'

    def get_queryset(self, request):
        """
        Оптимизирует запросы для админки.

        PURPOSE:
          - Предзагружает связанные объекты для оптимизации производительности
          - Уменьшает количество SQL запросов при отображении списка

        ARGS:
          - request: HTTP запрос

        RETURNS:
          - QuerySet: Оптимизированный QuerySet с предзагруженными данными
        """
        return super().get_queryset(request).select_related(
            'plan', 'activated_by'
        )

    def has_delete_permission(self, request, obj=None):
        """
        Ограничивает удаление активированных промокодов.

        PURPOSE:
          - Предотвращает случайное удаление использованных промокодов
          - Сохраняет историю активаций для аудита

        ARGS:
          - request: HTTP запрос
          - obj (PromoCode): Объект промокода

        RETURNS:
          - bool: True если можно удалить, False если нельзя
        """
        if obj and obj.is_activated:
            return False
        return super().has_delete_permission(request, obj)

    def save_model(self, request, obj, form, change):
        """
        Дополнительная обработка при сохранении промокода.

        PURPOSE:
          - Нормализует код промокода (приводит к верхнему регистру)
          - Обеспечивает консистентность данных

        ARGS:
          - request: HTTP запрос
          - obj (PromoCode): Объект промокода
          - form: Форма админки
          - change (bool): Флаг изменения существующего объекта
        """
        # Нормализуем код промокода
        if obj.code:
            obj.code = obj.code.strip().upper()

        super().save_model(request, obj, form, change)

    def get_urls(self):
        """
        Добавляет кастомные URL'ы для админки промокодов.

        PURPOSE:
          - Добавляет URL для генератора промокодов
          - Интегрирует кастомные представления в админку

        RETURNS:
          - list: Список URL паттернов
        """
        urls = super().get_urls()
        custom_urls = [
            path(
                'generate-codes/',
                self.admin_site.admin_view(self.generate_codes_view),
                name='promo_promocode_generate'
            ),
        ]
        return custom_urls + urls

    def generate_codes_view(self, request):
        """
        Представление для массовой генерации промокодов.

        PURPOSE:
          - Обеспечивает интерфейс для создания множества промокодов
          - Упрощает массовое создание промокодов для кампаний
          - Интегрируется в админку Django

        ARGS:
          - request: HTTP запрос

        RETURNS:
          - HttpResponse: Ответ с формой или редирект после создания
        """
        if request.method == 'POST':
            return self._handle_generate_codes_post(request)

        # GET запрос - показываем форму
        from subscriptions.models import SubscriptionPlan
        context = {
            'title': 'Generate Promo Codes',
            'plans': SubscriptionPlan.objects.filter(is_active=True),
            'opts': self.model._meta,
            'has_change_permission': self.has_change_permission(request),
        }
        return render(request, 'admin/promo/generate_codes.html', context)

    def _handle_generate_codes_post(self, request):
        """
        Обрабатывает POST запрос для генерации промокодов.

        PURPOSE:
          - Создает указанное количество промокодов
          - Валидирует входные данные
          - Обеспечивает обратную связь пользователю

        ARGS:
          - request: HTTP запрос с данными формы

        RETURNS:
          - HttpResponseRedirect: Редирект на список промокодов
        """
        try:
            from subscriptions.models import SubscriptionPlan
            import string
            import random
            from datetime import datetime

            plan_id = request.POST.get('plan')
            quantity = int(request.POST.get('quantity', 1))
            expires_at_str = request.POST.get('expires_at')

            # Валидация
            if quantity < 1 or quantity > 1000:
                messages.error(request, 'Quantity must be between 1 and 1000')
                return redirect('admin:promo_promocode_generate')

            plan = SubscriptionPlan.objects.get(id=plan_id)
            expires_at = None
            if expires_at_str:
                expires_at = datetime.strptime(expires_at_str, '%Y-%m-%d')

            # Генерация промокодов
            created_codes = []
            for _ in range(quantity):
                code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=8))
                # Проверяем уникальность
                while PromoCode.objects.filter(code=code).exists():
                    code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=8))

                PromoCode.objects.create(
                    code=code,
                    plan=plan,
                    expires_at=expires_at
                )
                created_codes.append(code)

            messages.success(
                request,
                f'Successfully generated {len(created_codes)} promo codes for plan "{plan.name}"'
            )

        except Exception as e:
            messages.error(request, f'Error generating codes: {str(e)}')

        return redirect('admin:promo_promocode_changelist')

    def changelist_view(self, request, extra_context=None):
        """
        Добавляет кнопку генерации промокодов в список.

        PURPOSE:
          - Интегрирует кнопку генерации в интерфейс списка
          - Обеспечивает удобный доступ к функции генерации

        ARGS:
          - request: HTTP запрос
          - extra_context: Дополнительный контекст

        RETURNS:
          - HttpResponse: Ответ со списком промокодов
        """
        extra_context = extra_context or {}
        extra_context['generate_codes_url'] = reverse('admin:promo_promocode_generate')
        return super().changelist_view(request, extra_context)
