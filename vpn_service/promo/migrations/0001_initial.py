# Generated by Django 4.2.7 on 2025-06-07 10:46

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('subscriptions', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PromoCode',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Уникальный идентификатор промокода', primary_key=True, serialize=False)),
                ('code', models.CharField(db_index=True, help_text='Уникальный буквенно-цифровой код для активации', max_length=16, unique=True)),
                ('is_activated', models.BooleanField(default=False, help_text='Флаг активации промокода')),
                ('activated_at', models.DateTimeField(blank=True, help_text='Время активации промокода', null=True)),
                ('expires_at', models.DateTimeField(blank=True, help_text='Срок годности промокода до активации (None = бессрочный)', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Время создания промокода')),
                ('activated_by', models.ForeignKey(blank=True, help_text='Пользователь, который активировал промокод', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='activated_promo_codes', to=settings.AUTH_USER_MODEL)),
                ('plan', models.ForeignKey(help_text='Тарифный план, который активируется этим промокодом', on_delete=django.db.models.deletion.CASCADE, to='subscriptions.subscriptionplan')),
            ],
            options={
                'verbose_name': 'Promo Code',
                'verbose_name_plural': 'Promo Codes',
                'db_table': 'promo_codes',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['code'], name='promo_codes_code_5b1d05_idx'), models.Index(fields=['is_activated'], name='promo_codes_is_acti_193de1_idx'), models.Index(fields=['expires_at'], name='promo_codes_expires_e12c60_idx'), models.Index(fields=['plan', 'is_activated'], name='promo_codes_plan_id_b1a28d_idx')],
            },
        ),
    ]
