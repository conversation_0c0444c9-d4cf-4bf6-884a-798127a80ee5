"""
Promo code models for VPN service subscription activation.
"""
from django.db import models
from django.utils import timezone
import uuid


class PromoCode(models.Model):
    """
    Модель промокодов для активации подписок VPN-сервиса.

    PURPOSE:
      - Обеспечивает механизм активации платных подписок через уникальные коды
      - Связывает промокоды с конкретными тарифными планами
      - Отслеживает использование и срок действия промокодов
      - Поддерживает монетизацию сервиса через распространение кодов

    AAG (Actor -> Action -> Goal):
      - Пользователь -> Вводит промокод -> Активирует платную подписку
      - Система -> Валидирует промокод -> Предоставляет VPN-доступ согласно тарифу
      - Администратор -> Создает промокоды -> Управляет распространением подписок

    CONTRACT:
      PRECONDITIONS:
        - code (str): Уникальный буквенно-цифровой код длиной до 16 символов
        - plan (SubscriptionPlan): Существующий активный тарифный план
        - expires_at (datetime): Срок годности промокода (может быть None для бессрочных)
      POSTCONDITIONS:
        - Создается промокод, готовый к активации
        - Промокод может быть использован для создания подписки
        - После активации промокод помечается как использованный
      INVARIANTS:
        - code всегда уникален в системе
        - is_activated определяет возможность использования промокода
        - activated_by устанавливается только при активации
        - activated_at устанавливается только при активации
    """
    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
        help_text="Уникальный идентификатор промокода"
    )
    code = models.CharField(
        max_length=16,
        unique=True,
        db_index=True,
        help_text="Уникальный буквенно-цифровой код для активации"
    )
    plan = models.ForeignKey(
        'subscriptions.SubscriptionPlan',
        on_delete=models.CASCADE,
        help_text="Тарифный план, который активируется этим промокодом"
    )
    is_activated = models.BooleanField(
        default=False,
        help_text="Флаг активации промокода"
    )
    activated_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Время активации промокода"
    )
    activated_by = models.ForeignKey(
        'accounts.UserAccount',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='activated_promo_codes',
        help_text="Пользователь, который активировал промокод"
    )
    expires_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Срок годности промокода до активации (None = бессрочный)"
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        help_text="Время создания промокода"
    )

    class Meta:
        db_table = 'promo_codes'
        verbose_name = 'Promo Code'
        verbose_name_plural = 'Promo Codes'
        indexes = [
            models.Index(fields=['code']),
            models.Index(fields=['is_activated']),
            models.Index(fields=['expires_at']),
            models.Index(fields=['plan', 'is_activated']),
        ]
        ordering = ['-created_at']

    def __str__(self):
        status = "activated" if self.is_activated else "active"
        if self.is_expired():
            status = "expired"
        return f"Code {self.code} ({self.plan.name}) - {status}"

    def is_expired(self):
        """
        Проверяет, истек ли срок действия промокода.

        PURPOSE:
          - Определяет валидность промокода по времени
          - Используется при проверке возможности активации

        RETURNS:
          - bool: True если промокод истек, False если еще действителен или бессрочный
        """
        if self.expires_at is None:
            return False  # Бессрочный промокод
        return timezone.now() > self.expires_at

    def is_valid(self):
        """
        Проверяет, валиден ли промокод для использования.

        PURPOSE:
          - Комплексная проверка возможности использования промокода
          - Учитывает активацию, срок действия и связанный тарифный план

        RETURNS:
          - bool: True если промокод можно использовать, False если нельзя
        """
        return (
            not self.is_activated and
            not self.is_expired() and
            self.plan.is_active
        )

    def activate(self, user):
        """
        Активирует промокод для указанного пользователя.

        PURPOSE:
          - Помечает промокод как использованный
          - Сохраняет информацию о пользователе и времени активации
          - Предотвращает повторное использование

        ARGS:
          - user (UserAccount): Пользователь, активирующий промокод

        RAISES:
          - ValueError: Если промокод уже активирован или недействителен
        """
        if not self.is_valid():
            raise ValueError("Промокод недействителен для активации")

        self.is_activated = True
        self.activated_at = timezone.now()
        self.activated_by = user
        self.save()
