"""
Tests for promo code activation system.
"""
import json
from datetime import timed<PERSON><PERSON>
from django.test import TestCase
from django.utils import timezone
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from unittest.mock import patch, Mock

from accounts.models import UserAccount, UserDevice, HiddifyLink
from subscriptions.models import SubscriptionPlan, ActiveSubscription
from .models import PromoCode
from .services import PromoActivationService


class PromoCodeModelTest(TestCase):
    """
    Тесты для модели PromoCode.

    PURPOSE:
      - Проверяет корректность работы модели промокодов
      - Тестирует валидацию и методы модели
      - Обеспечивает правильность бизнес-логики
    """

    def setUp(self):
        """Настройка тестовых данных."""
        self.plan = SubscriptionPlan.objects.create(
            name='Test Plan',
            description='Test plan for promo codes',
            price=10.00,
            currency='USD',
            duration_days=30,
            traffic_limit_gb=100,
            max_devices=5,
            is_active=True,
            is_trial=False
        )

    def test_promo_code_creation(self):
        """Тест создания промокода."""
        promo_code = PromoCode.objects.create(
            code='TEST2024',
            plan=self.plan,
            expires_at=timezone.now() + timedelta(days=30)
        )

        self.assertEqual(promo_code.code, 'TEST2024')
        self.assertEqual(promo_code.plan, self.plan)
        self.assertFalse(promo_code.is_activated)
        self.assertIsNone(promo_code.activated_by)
        self.assertIsNone(promo_code.activated_at)

    def test_promo_code_is_valid(self):
        """Тест валидности промокода."""
        # Валидный промокод
        valid_promo = PromoCode.objects.create(
            code='VALID2024',
            plan=self.plan,
            expires_at=timezone.now() + timedelta(days=30)
        )
        self.assertTrue(valid_promo.is_valid())

        # Истекший промокод
        expired_promo = PromoCode.objects.create(
            code='EXPIRED2024',
            plan=self.plan,
            expires_at=timezone.now() - timedelta(days=1)
        )
        self.assertFalse(expired_promo.is_valid())

        # Активированный промокод
        activated_promo = PromoCode.objects.create(
            code='ACTIVATED2024',
            plan=self.plan,
            is_activated=True,
            expires_at=timezone.now() + timedelta(days=30)
        )
        self.assertFalse(activated_promo.is_valid())

    def test_promo_code_activation(self):
        """Тест активации промокода."""
        user = UserAccount.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

        promo_code = PromoCode.objects.create(
            code='ACTIVATE2024',
            plan=self.plan,
            expires_at=timezone.now() + timedelta(days=30)
        )

        # Активируем промокод
        promo_code.activate(user)

        self.assertTrue(promo_code.is_activated)
        self.assertEqual(promo_code.activated_by, user)
        self.assertIsNotNone(promo_code.activated_at)
        self.assertFalse(promo_code.is_valid())

    def test_promo_code_activate_invalid(self):
        """Тест активации недействительного промокода."""
        user = UserAccount.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

        # Истекший промокод
        expired_promo = PromoCode.objects.create(
            code='EXPIRED2024',
            plan=self.plan,
            expires_at=timezone.now() - timedelta(days=1)
        )

        with self.assertRaises(ValueError):
            expired_promo.activate(user)


class PromoActivationServiceTest(TestCase):
    """
    Тесты для сервиса активации промокодов.

    PURPOSE:
      - Проверяет корректность работы сервиса активации
      - Тестирует интеграцию с Hiddify API
      - Обеспечивает правильность создания пользователей и подписок
    """

    def setUp(self):
        """Настройка тестовых данных."""
        self.plan = SubscriptionPlan.objects.create(
            name='Test Plan',
            description='Test plan for promo codes',
            price=10.00,
            currency='USD',
            duration_days=30,
            traffic_limit_gb=100,
            max_devices=5,
            is_active=True,
            is_trial=False
        )

        self.trial_plan = SubscriptionPlan.objects.create(
            name='Trial Plan',
            description='Trial plan for new users',
            price=0.00,
            currency='USD',
            duration_days=7,
            traffic_limit_gb=10,
            max_devices=1,
            is_active=True,
            is_trial=True
        )

        self.promo_code = PromoCode.objects.create(
            code='TEST2024',
            plan=self.plan,
            expires_at=timezone.now() + timedelta(days=30)
        )

        self.service = PromoActivationService()

    def test_validate_promo_code_valid(self):
        """Тест валидации действительного промокода."""
        result = self.service._validate_promo_code('TEST2024')
        self.assertIsNotNone(result)
        self.assertEqual(result.code, 'TEST2024')

    def test_validate_promo_code_invalid(self):
        """Тест валидации недействительного промокода."""
        result = self.service._validate_promo_code('INVALID2024')
        self.assertIsNone(result)

    @patch('promo.services.HiddifyApiService.create_hiddify_user')
    def test_create_anonymous_user(self, mock_create_hiddify):
        """Тест создания анонимного пользователя."""
        # Мокаем ответ Hiddify API
        mock_create_hiddify.return_value = (True, {'uuid': 'test-uuid-123'})

        device_id = 'test_device_123'
        user, is_new = self.service._create_anonymous_user(device_id)

        self.assertIsNotNone(user)
        self.assertTrue(is_new)
        self.assertTrue(user.is_anonymous)

        # Проверяем, что создано устройство
        device = UserDevice.objects.get(device_id=device_id)
        self.assertEqual(device.user, user)

        # Проверяем, что создана связь с Hiddify
        hiddify_link = HiddifyLink.objects.get(user=user)
        self.assertEqual(str(hiddify_link.hiddify_user_uuid), 'test-uuid-123')


class PromoActivationAPITest(APITestCase):
    """
    Тесты для API активации промокодов.

    PURPOSE:
      - Проверяет корректность работы API эндпоинта
      - Тестирует различные сценарии активации
      - Обеспечивает правильность ответов API
    """

    def setUp(self):
        """Настройка тестовых данных."""
        self.plan = SubscriptionPlan.objects.create(
            name='Test Plan',
            description='Test plan for promo codes',
            price=10.00,
            currency='USD',
            duration_days=30,
            traffic_limit_gb=100,
            max_devices=5,
            is_active=True,
            is_trial=False
        )

        self.trial_plan = SubscriptionPlan.objects.create(
            name='Trial Plan',
            description='Trial plan for new users',
            price=0.00,
            currency='USD',
            duration_days=7,
            traffic_limit_gb=10,
            max_devices=1,
            is_active=True,
            is_trial=True
        )

        self.valid_promo = PromoCode.objects.create(
            code='VALID2024',
            plan=self.plan,
            expires_at=timezone.now() + timedelta(days=30)
        )

        self.expired_promo = PromoCode.objects.create(
            code='EXPIRED2024',
            plan=self.plan,
            expires_at=timezone.now() - timedelta(days=1)
        )

        self.url = reverse('promo:activate_promo_code')

    def test_activate_promo_validation_error(self):
        """Тест ошибки валидации данных."""
        # Пустые данные
        response = self.client.post(self.url, {})
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.data)

        # Неполные данные
        response = self.client.post(self.url, {'promo_code': 'TEST2024'})
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_activate_invalid_promo_code(self):
        """Тест активации несуществующего промокода."""
        data = {
            'promo_code': 'NONEXISTENT2024',
            'device_id': 'test_device_123'
        }

        response = self.client.post(self.url, data)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertIn('error', response.data)
        self.assertEqual(response.data['code'], 'INVALID_PROMO_CODE')

    def test_activate_expired_promo_code(self):
        """Тест активации истекшего промокода."""
        data = {
            'promo_code': 'EXPIRED2024',
            'device_id': 'test_device_123'
        }

        response = self.client.post(self.url, data)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertIn('error', response.data)

    @patch('promo.services.HiddifyApiService.create_hiddify_user')
    @patch('promo.services.HiddifyApiService.update_hiddify_user')
    def test_activate_promo_anonymous_user(self, mock_update_hiddify, mock_create_hiddify):
        """Тест активации промокода для нового анонимного пользователя."""
        # Мокаем ответы Hiddify API
        mock_create_hiddify.return_value = (True, {'uuid': 'test-uuid-123'})
        mock_update_hiddify.return_value = (True, {'status': 'updated'})

        data = {
            'promo_code': 'VALID2024',
            'device_id': 'new_device_123'
        }

        response = self.client.post(self.url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Проверяем структуру ответа
        self.assertTrue(response.data['success'])
        self.assertIn('subscription', response.data)
        self.assertIn('tokens', response.data)  # Для нового анонимного пользователя

        # Проверяем, что промокод активирован
        self.valid_promo.refresh_from_db()
        self.assertTrue(self.valid_promo.is_activated)

        # Проверяем, что создан пользователь и подписка
        user = UserAccount.objects.get(is_anonymous=True)
        subscription = ActiveSubscription.objects.get(user=user)
        self.assertEqual(subscription.plan, self.plan)
        self.assertTrue(subscription.is_active)

    @patch('promo.services.HiddifyApiService.update_hiddify_user')
    def test_activate_promo_authenticated_user(self, mock_update_hiddify):
        """Тест активации промокода для аутентифицированного пользователя."""
        # Создаем пользователя и устройство
        user = UserAccount.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

        device = UserDevice.objects.create(
            user=user,
            device_id='existing_device_123',
            device_name='Test Device',
            is_active=True
        )

        # Создаем Hiddify связь
        HiddifyLink.objects.create(
            user=user,
            device=device,
            hiddify_user_uuid='existing-uuid-123',
            hiddify_comment={},
            traffic_limit_bytes=10 * 1024 * 1024 * 1024,
            is_active_in_hiddify=True,
            hiddify_created_at=timezone.now()
        )

        # Мокаем ответ Hiddify API
        mock_update_hiddify.return_value = (True, {'status': 'updated'})

        # Получаем JWT токен
        refresh = RefreshToken.for_user(user)
        access_token = refresh.access_token

        data = {
            'promo_code': 'VALID2024',
            'device_id': 'existing_device_123'
        }

        # Отправляем запрос с авторизацией
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
        response = self.client.post(self.url, data)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Проверяем структуру ответа
        self.assertTrue(response.data['success'])
        self.assertIn('subscription', response.data)
        self.assertNotIn('tokens', response.data)  # Для существующего пользователя токены не нужны

        # Проверяем, что промокод активирован
        self.valid_promo.refresh_from_db()
        self.assertTrue(self.valid_promo.is_activated)
        self.assertEqual(self.valid_promo.activated_by, user)

    @patch('promo.services.HiddifyApiService.create_hiddify_user')
    def test_activate_promo_hiddify_error(self, mock_create_hiddify):
        """Тест обработки ошибки Hiddify API."""
        # Мокаем ошибку Hiddify API
        mock_create_hiddify.return_value = (False, {'error': 'Hiddify API error'})

        data = {
            'promo_code': 'VALID2024',
            'device_id': 'error_device_123'
        }

        response = self.client.post(self.url, data)
        self.assertEqual(response.status_code, status.HTTP_409_CONFLICT)
        self.assertIn('error', response.data)

        # Проверяем, что промокод НЕ активирован при ошибке
        self.valid_promo.refresh_from_db()
        self.assertFalse(self.valid_promo.is_activated)
