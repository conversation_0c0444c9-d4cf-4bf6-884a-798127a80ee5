"""
Views for promo code activation API.
"""
import logging
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
from drf_spectacular.utils import extend_schema, OpenApiExample
from .serializers import (
    PromoActivationRequestSerializer,
    PromoActivationResponseSerializer,
    PromoErrorResponseSerializer
)
from .services import PromoActivationService

logger = logging.getLogger(__name__)


@extend_schema(
    operation_id='activate_promo_code',
    summary='Активация промокода',
    description="""
    Активирует промокод и создает подписку для пользователя.

    Эндпоинт поддерживает как аутентифицированных, так и анонимных пользователей:
    - Если передан Authorization заголовок с валидным JWT - используется аутентифицированный пользователь
    - Если Authorization отсутствует - создается новый анонимный пользователь или используется существующий по device_id

    Для новых анонимных пользователей в ответе возвращаются JWT токены.
    """,
    request=PromoActivationRequestSerializer,
    responses={
        200: PromoActivationResponseSerializer,
        400: PromoErrorResponseSerializer,
        404: PromoErrorResponseSerializer,
        409: PromoErrorResponseSerializer,
        500: PromoErrorResponseSerializer,
    },
    examples=[
        OpenApiExample(
            'Успешная активация',
            value={
                'promo_code': 'PREMIUM2024',
                'device_id': 'device_12345'
            },
            request_only=True
        ),
        OpenApiExample(
            'Успешный ответ для нового пользователя',
            value={
                'success': True,
                'message': 'Subscription activated successfully!',
                'subscription': {
                    'plan_name': 'Premium',
                    'end_date': '2024-07-01T12:00:00Z',
                    'traffic_limit_gb': 100,
                    'duration_days': 30,
                    'is_active': True
                },
                'tokens': {
                    'access': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...',
                    'refresh': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...'
                }
            },
            response_only=True
        ),
        OpenApiExample(
            'Ошибка - промокод не найден',
            value={
                'error': 'Промокод не найден или недействителен',
                'code': 'INVALID_PROMO_CODE'
            },
            response_only=True,
            status_codes=['404']
        )
    ]
)
@api_view(['POST'])
@permission_classes([AllowAny])  # Открытый эндпоинт
def activate_promo_code(request):
    """
    Активирует промокод и создает подписку.

    PURPOSE:
      - Обрабатывает запросы на активацию промокодов
      - Поддерживает как аутентифицированных, так и анонимных пользователей
      - Возвращает информацию о созданной подписке и токены при необходимости

    AAG (Actor -> Action -> Goal):
      - Пользователь -> Отправляет промокод -> Активирует платную подписку
      - Мобильное приложение -> Использует deeplink -> Получает VPN доступ
      - Система -> Обрабатывает активацию -> Предоставляет VPN сервис

    CONTRACT:
      PRECONDITIONS:
        - request.data содержит promo_code и device_id
        - promo_code существует и действителен
        - device_id является валидным идентификатором
      POSTCONDITIONS:
        - Промокод активирован и помечен как использованный
        - Создана подписка для пользователя
        - Обновлены лимиты в Hiddify Manager
        - Возвращен ответ с информацией о подписке
      INVARIANTS:
        - Все операции выполняются атомарно
        - При ошибке состояние системы не изменяется
    """
    # Валидация входных данных
    serializer = PromoActivationRequestSerializer(data=request.data)
    if not serializer.is_valid():
        return Response({
            'error': 'Некорректные данные запроса',
            'code': 'VALIDATION_ERROR',
            'details': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    promo_code = serializer.validated_data['promo_code']
    device_id = serializer.validated_data['device_id']

    # Попытка получить аутентифицированного пользователя
    authenticated_user = None
    auth_header = request.META.get('HTTP_AUTHORIZATION')

    if auth_header and auth_header.startswith('Bearer '):
        try:
            jwt_auth = JWTAuthentication()
            validated_token = jwt_auth.get_validated_token(auth_header.split(' ')[1])
            authenticated_user = jwt_auth.get_user(validated_token)
            logger.info(f"Authenticated user {authenticated_user.id} activating promo code {promo_code}")
        except (InvalidToken, TokenError) as e:
            logger.warning(f"Invalid JWT token in promo activation: {str(e)}")
            # Продолжаем как анонимный пользователь
        except Exception as e:
            logger.error(f"Error processing JWT token: {str(e)}")
            # Продолжаем как анонимный пользователь

    # Активация промокода
    activation_service = PromoActivationService()
    success, result = activation_service.activate_promo_code(
        promo_code=promo_code,
        device_id=device_id,
        user=authenticated_user
    )

    if success:
        logger.info(f"Successfully activated promo code {promo_code} for device {device_id}")
        return Response(result, status=status.HTTP_200_OK)
    else:
        # Определяем статус код на основе типа ошибки
        error_code = result.get('code', 'UNKNOWN_ERROR')

        if error_code == 'INVALID_PROMO_CODE':
            response_status = status.HTTP_404_NOT_FOUND
        elif error_code == 'VALIDATION_ERROR':
            response_status = status.HTTP_400_BAD_REQUEST
        elif error_code in ['HIDDIFY_UPDATE_FAILED', 'USER_IDENTIFICATION_FAILED']:
            response_status = status.HTTP_409_CONFLICT
        else:
            response_status = status.HTTP_500_INTERNAL_SERVER_ERROR

        logger.warning(f"Failed to activate promo code {promo_code}: {result.get('error')}")
        return Response(result, status=response_status)
