"""
Service layer for promo code activation logic.
"""
import logging
import json
from datetime import datetime, timed<PERSON>ta
from typing import Tu<PERSON>, Dict, Optional
from django.db import transaction
from django.utils import timezone
from rest_framework_simplejwt.tokens import RefreshToken

from accounts.models import UserAccount, UserDevice, HiddifyLink
from subscriptions.models import ActiveSubscription, SubscriptionPlan
from vpn.services import HiddifyApiService
from .models import PromoCode

logger = logging.getLogger(__name__)


class MockHiddifyApiService:
    """
    Mock версия HiddifyApiService для тестирования без реального API.

    PURPOSE:
      - Предоставляет заглушку для Hiddify API во время разработки и тестирования
      - Возвращает успешные ответы без реальных вызовов API
      - Позволяет тестировать логику промокодов без зависимости от внешнего сервиса
    """

    def create_hiddify_user(self, name: str, usage_limit_gb: int,
                           package_days: int, comment_json_string: str) -> Tuple[bool, Dict]:
        """Mock создания пользователя Hiddify."""
        import uuid
        return True, {'uuid': str(uuid.uuid4())}

    def update_hiddify_user(self, hiddify_user_uuid: str, data_to_update: Dict) -> Tuple[bool, Dict]:
        """Mock обновления пользователя Hiddify."""
        return True, {'status': 'updated', 'uuid': hiddify_user_uuid}


class PromoActivationService:
    """
    Сервис для активации промокодов и создания подписок.

    PURPOSE:
      - Инкапсулирует всю логику активации промокодов
      - Обеспечивает атомарность операций через транзакции
      - Управляет созданием анонимных пользователей и обновлением Hiddify
      - Предоставляет единообразную обработку ошибок

    AAG (Actor -> Action -> Goal):
      - API View -> Вызывает сервис -> Активирует подписку через промокод
      - Сервис -> Координирует операции -> Обеспечивает консистентность данных
      - Система -> Обновляет Hiddify -> Синхронизирует VPN доступ

    CONTRACT:
      PRECONDITIONS:
        - promo_code (str): Валидный промокод
        - device_id (str): Уникальный идентификатор устройства
        - user (UserAccount, optional): Аутентифицированный пользователь
      POSTCONDITIONS:
        - Промокод активирован и помечен как использованный
        - Создана или обновлена подписка пользователя
        - Обновлены лимиты в Hiddify Manager
        - Возвращены данные о подписке и токенах (если нужно)
      INVARIANTS:
        - Все операции выполняются в рамках транзакции
        - При ошибке все изменения откатываются
    """

    def __init__(self):
        # Временно используем только mock для диагностики проблем
        self.hiddify_service = MockHiddifyApiService()
        logger.info("Using MockHiddifyApiService for testing")

    def activate_promo_code(self, promo_code: str, device_id: str, 
                          user: Optional[UserAccount] = None) -> Tuple[bool, Dict]:
        """
        Активирует промокод и создает подписку.

        PURPOSE:
          - Основной метод для активации промокодов
          - Координирует все этапы процесса активации
          - Обеспечивает атомарность операций

        ARGS:
          - promo_code (str): Код для активации
          - device_id (str): Идентификатор устройства
          - user (UserAccount, optional): Аутентифицированный пользователь

        RETURNS:
          - Tuple[bool, Dict]: (success, response_data_or_error)
        """
        try:
            with transaction.atomic():
                # Шаг 1: Валидация промокода
                promo_obj = self._validate_promo_code(promo_code)
                if not promo_obj:
                    return False, {
                        'error': 'Промокод не найден или недействителен',
                        'code': 'INVALID_PROMO_CODE'
                    }

                # Шаг 2: Получение или создание пользователя
                user_account, is_new_user = self._get_or_create_user(device_id, user)
                if not user_account:
                    return False, {
                        'error': 'Не удалось идентифицировать пользователя',
                        'code': 'USER_IDENTIFICATION_FAILED'
                    }

                # Шаг 3: Деактивация старых подписок
                self._deactivate_existing_subscriptions(user_account)

                # Шаг 4: Создание новой подписки
                subscription = self._create_subscription(user_account, promo_obj.plan)

                # Шаг 5: Обновление Hiddify
                success = self._update_hiddify_limits(user_account, promo_obj.plan)
                if not success:
                    return False, {
                        'error': 'Не удалось обновить VPN доступ',
                        'code': 'HIDDIFY_UPDATE_FAILED'
                    }

                # Шаг 6: Активация промокода
                promo_obj.activate(user_account)

                # Шаг 7: Формирование ответа
                response_data = self._build_response(subscription, user_account, is_new_user)
                
                logger.info(f"Successfully activated promo code {promo_code} for user {user_account.id}")
                return True, response_data

        except Exception as e:
            logger.error(f"Error activating promo code {promo_code}: {str(e)}")
            return False, {
                'error': 'Внутренняя ошибка сервера',
                'code': 'INTERNAL_ERROR',
                'details': str(e) if logger.level <= logging.DEBUG else None
            }

    def _validate_promo_code(self, promo_code: str) -> Optional[PromoCode]:
        """
        Валидирует промокод и возвращает объект, если он действителен.

        PURPOSE:
          - Проверяет существование и валидность промокода
          - Возвращает объект для дальнейшего использования

        ARGS:
          - promo_code (str): Код для проверки

        RETURNS:
          - PromoCode или None: Объект промокода или None если недействителен
        """
        try:
            promo_obj = PromoCode.objects.select_related('plan').get(code=promo_code)
            if promo_obj.is_valid():
                return promo_obj
            else:
                logger.warning(f"Invalid promo code attempted: {promo_code}")
                return None
        except PromoCode.DoesNotExist:
            logger.warning(f"Non-existent promo code attempted: {promo_code}")
            return None

    def _get_or_create_user(self, device_id: str, 
                           authenticated_user: Optional[UserAccount]) -> Tuple[Optional[UserAccount], bool]:
        """
        Получает существующего пользователя или создает нового анонимного.

        PURPOSE:
          - Идентифицирует пользователя по JWT токену или device_id
          - Создает анонимного пользователя для новых устройств
          - Обеспечивает связь устройства с пользователем

        ARGS:
          - device_id (str): Идентификатор устройства
          - authenticated_user (UserAccount, optional): Аутентифицированный пользователь

        RETURNS:
          - Tuple[UserAccount, bool]: (пользователь, флаг_нового_пользователя)
        """
        # Если есть аутентифицированный пользователь, используем его
        if authenticated_user:
            # Проверяем или создаем устройство для этого пользователя
            user_device, _ = UserDevice.objects.get_or_create(
                user=authenticated_user,
                device_id=device_id,
                defaults={
                    'device_name': f'Device {device_id[:8]}',
                    'is_active': True
                }
            )
            return authenticated_user, False

        # Ищем существующее устройство
        try:
            user_device = UserDevice.objects.select_related('user').get(device_id=device_id)
            return user_device.user, False
        except UserDevice.DoesNotExist:
            pass

        # Создаем нового анонимного пользователя
        return self._create_anonymous_user(device_id)

    def _create_anonymous_user(self, device_id: str) -> Tuple[Optional[UserAccount], bool]:
        """
        Создает нового анонимного пользователя с устройством и Hiddify связью.

        PURPOSE:
          - Создает анонимного пользователя для нового устройства
          - Инициализирует связь с Hiddify Manager
          - Обеспечивает базовую настройку для VPN доступа

        ARGS:
          - device_id (str): Идентификатор устройства

        RETURNS:
          - Tuple[UserAccount, bool]: (новый_пользователь, True)
        """
        try:
            # Создаем анонимного пользователя
            user_account = UserAccount.objects.create_user(
                is_anonymous=True
            )

            # Создаем устройство
            user_device = UserDevice.objects.create(
                user=user_account,
                device_id=device_id,
                device_name=f'Device {device_id[:8]}',
                is_active=True
            )

            # Получаем пробный тариф для создания Hiddify пользователя
            trial_plan = SubscriptionPlan.objects.filter(
                is_trial=True, is_active=True
            ).first()
            if not trial_plan:
                trial_plan = SubscriptionPlan.objects.filter(is_active=True).order_by('price').first()

            if not trial_plan:
                logger.error("No active subscription plans available for anonymous user creation")
                return None, False

            # Создаем пользователя в Hiddify
            comment_data = {
                'user_id': str(user_account.id),
                'device_id': device_id,
                'plan_id': str(trial_plan.id),
                'is_anonymous': True,
                'created_at': datetime.now().isoformat()
            }

            success, hiddify_response = self.hiddify_service.create_hiddify_user(
                name=f"anon_{str(user_account.id)[:8]}",
                usage_limit_gb=trial_plan.traffic_limit_gb,
                package_days=trial_plan.duration_days,
                comment_json_string=json.dumps(comment_data)
            )

            if not success:
                logger.error(f"Failed to create Hiddify user for anonymous user: {hiddify_response}")
                return None, False

            # Создаем связь с Hiddify
            HiddifyLink.objects.create(
                user=user_account,
                device=user_device,
                hiddify_user_uuid=hiddify_response['uuid'],
                hiddify_comment=comment_data,
                traffic_limit_bytes=trial_plan.traffic_limit_gb * 1024 * 1024 * 1024,
                is_active_in_hiddify=True,
                hiddify_created_at=timezone.now()
            )

            logger.info(f"Created anonymous user {user_account.id} with device {device_id}")
            return user_account, True

        except Exception as e:
            logger.error(f"Error creating anonymous user for device {device_id}: {str(e)}")
            return None, False

    def _deactivate_existing_subscriptions(self, user: UserAccount):
        """
        Деактивирует существующие активные подписки пользователя.

        PURPOSE:
          - Обеспечивает замену старой подписки новой
          - Предотвращает конфликты между подписками
          - Упрощает логику управления подписками

        ARGS:
          - user (UserAccount): Пользователь для деактивации подписок
        """
        active_subscriptions = ActiveSubscription.objects.filter(
            user=user,
            is_active=True
        )

        for subscription in active_subscriptions:
            subscription.is_active = False
            subscription.save()
            logger.info(f"Deactivated subscription {subscription.id} for user {user.id}")

    def _create_subscription(self, user: UserAccount, plan: SubscriptionPlan) -> ActiveSubscription:
        """
        Создает новую активную подписку для пользователя.

        PURPOSE:
          - Создает подписку на основе тарифного плана из промокода
          - Устанавливает корректные даты начала и окончания
          - Активирует подписку для немедленного использования

        ARGS:
          - user (UserAccount): Пользователь для создания подписки
          - plan (SubscriptionPlan): Тарифный план из промокода

        RETURNS:
          - ActiveSubscription: Созданная подписка
        """
        start_date = timezone.now()
        end_date = start_date + timedelta(days=plan.duration_days)

        subscription = ActiveSubscription.objects.create(
            user=user,
            plan=plan,
            start_date=start_date,
            end_date=end_date,
            is_active=True,
            payment_method='promo_code'
        )

        logger.info(f"Created subscription {subscription.id} for user {user.id} with plan {plan.name}")
        return subscription

    def _update_hiddify_limits(self, user: UserAccount, plan: SubscriptionPlan) -> bool:
        """
        Обновляет лимиты пользователя в Hiddify Manager.

        PURPOSE:
          - Синхронизирует лимиты трафика и времени с новым тарифом
          - Активирует пользователя в Hiddify если он был деактивирован
          - Обеспечивает соответствие VPN доступа подписке

        ARGS:
          - user (UserAccount): Пользователь для обновления
          - plan (SubscriptionPlan): Новый тарифный план

        RETURNS:
          - bool: True если обновление успешно, False при ошибке
        """
        try:
            hiddify_link = user.hiddify_link

            # Обновляем пользователя в Hiddify
            update_data = {
                'is_active': True,
                'usage_limit_GB': plan.traffic_limit_gb,
                'package_days': plan.duration_days
            }

            success, response = self.hiddify_service.update_hiddify_user(
                str(hiddify_link.hiddify_user_uuid),
                update_data
            )

            if success:
                # Обновляем локальные данные
                hiddify_link.is_active_in_hiddify = True
                hiddify_link.traffic_limit_bytes = plan.traffic_limit_gb * 1024 * 1024 * 1024
                hiddify_link.save()

                logger.info(f"Updated Hiddify limits for user {user.id}")
                return True
            else:
                logger.error(f"Failed to update Hiddify user {hiddify_link.hiddify_user_uuid}: {response}")
                return False

        except HiddifyLink.DoesNotExist:
            logger.error(f"No Hiddify link found for user {user.id}")
            return False
        except Exception as e:
            logger.error(f"Error updating Hiddify limits for user {user.id}: {str(e)}")
            return False

    def _build_response(self, subscription: ActiveSubscription,
                       user: UserAccount, is_new_user: bool) -> Dict:
        """
        Формирует ответ для успешной активации промокода.

        PURPOSE:
          - Создает стандартизированный ответ API
          - Включает JWT токены для новых анонимных пользователей
          - Предоставляет информацию о созданной подписке

        ARGS:
          - subscription (ActiveSubscription): Созданная подписка
          - user (UserAccount): Пользователь
          - is_new_user (bool): Флаг нового пользователя

        RETURNS:
          - Dict: Данные ответа для API
        """
        response_data = {
            'success': True,
            'message': 'Subscription activated successfully!',
            'subscription': {
                'plan_name': subscription.plan.name,
                'end_date': subscription.end_date.isoformat(),
                'traffic_limit_gb': subscription.plan.traffic_limit_gb,
                'duration_days': subscription.plan.duration_days,
                'is_active': subscription.is_active
            }
        }

        # Добавляем токены только для новых анонимных пользователей
        if is_new_user and user.is_anonymous:
            refresh = RefreshToken.for_user(user)
            access_token = refresh.access_token

            # Добавляем дополнительные claims
            user_devices = user.devices.filter(is_active=True)
            if user_devices.exists():
                access_token['device_id'] = user_devices.first().device_id

            try:
                hiddify_link = user.hiddify_link
                access_token['hiddify_uuid'] = str(hiddify_link.hiddify_user_uuid)
            except HiddifyLink.DoesNotExist:
                pass

            response_data['tokens'] = {
                'access': str(access_token),
                'refresh': str(refresh)
            }

        return response_data
