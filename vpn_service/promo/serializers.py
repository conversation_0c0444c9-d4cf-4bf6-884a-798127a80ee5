"""
Serializers for promo code activation API.
"""
from rest_framework import serializers


class PromoActivationRequestSerializer(serializers.Serializer):
    """
    Сериализатор для валидации запроса активации промокода.

    PURPOSE:
      - Валидирует входные данные для эндпоинта активации промокода
      - Обеспечивает корректность формата promo_code и device_id
      - Предоставляет понятные сообщения об ошибках валидации

    AAG (Actor -> Action -> Goal):
      - API клиент -> Отправляет данные активации -> Получает валидированные данные
      - DRF -> Валидирует сериализатор -> Обеспечивает корректность входных данных

    CONTRACT:
      PRECONDITIONS:
        - promo_code (str): Непустая строка с промокодом
        - device_id (str): Непустая строка с идентификатором устройства
      POSTCONDITIONS:
        - Возвращает валидированные данные для обработки
        - Генерирует ошибки валидации при некорректных данных
    """
    promo_code = serializers.CharField(
        max_length=16,
        min_length=1,
        required=True,
        help_text="Промокод для активации подписки"
    )
    device_id = serializers.CharField(
        max_length=255,
        min_length=1,
        required=True,
        help_text="Уникальный идентификатор устройства"
    )

    def validate_promo_code(self, value):
        """
        Валидирует формат промокода.

        PURPOSE:
          - Проверяет базовый формат промокода
          - Удаляет лишние пробелы и приводит к верхнему регистру

        ARGS:
          - value (str): Промокод для валидации

        RETURNS:
          - str: Нормализованный промокод

        RAISES:
          - ValidationError: При некорректном формате
        """
        # Нормализуем промокод: убираем пробелы и приводим к верхнему регистру
        normalized_code = value.strip().upper()
        
        if not normalized_code:
            raise serializers.ValidationError("Промокод не может быть пустым")
        
        # Проверяем, что промокод содержит только буквы и цифры
        if not normalized_code.isalnum():
            raise serializers.ValidationError(
                "Промокод должен содержать только буквы и цифры"
            )
        
        return normalized_code

    def validate_device_id(self, value):
        """
        Валидирует идентификатор устройства.

        PURPOSE:
          - Проверяет базовый формат device_id
          - Удаляет лишние пробелы

        ARGS:
          - value (str): Device ID для валидации

        RETURNS:
          - str: Нормализованный device_id

        RAISES:
          - ValidationError: При некорректном формате
        """
        normalized_device_id = value.strip()
        
        if not normalized_device_id:
            raise serializers.ValidationError("Device ID не может быть пустым")
        
        return normalized_device_id


class SubscriptionInfoSerializer(serializers.Serializer):
    """
    Сериализатор для информации о подписке в ответе активации.

    PURPOSE:
      - Предоставляет информацию о активированной подписке
      - Включает данные о тарифном плане и сроках действия
      - Используется в ответе успешной активации промокода
    """
    plan_name = serializers.CharField(read_only=True)
    end_date = serializers.DateTimeField(read_only=True)
    traffic_limit_gb = serializers.IntegerField(read_only=True)
    duration_days = serializers.IntegerField(read_only=True)
    is_active = serializers.BooleanField(read_only=True)


class PromoActivationResponseSerializer(serializers.Serializer):
    """
    Сериализатор для ответа успешной активации промокода.

    PURPOSE:
      - Стандартизирует формат ответа при успешной активации
      - Включает информацию о подписке и токенах (если нужно)
      - Обеспечивает консистентность API ответов
    """
    success = serializers.BooleanField(default=True)
    message = serializers.CharField(default="Subscription activated successfully!")
    subscription = SubscriptionInfoSerializer()
    tokens = serializers.DictField(required=False, help_text="JWT токены для новых анонимных пользователей")


class PromoErrorResponseSerializer(serializers.Serializer):
    """
    Сериализатор для ответов с ошибками активации промокода.

    PURPOSE:
      - Стандартизирует формат ошибок API
      - Предоставляет понятные сообщения об ошибках
      - Обеспечивает консистентность обработки ошибок
    """
    error = serializers.CharField(help_text="Описание ошибки")
    code = serializers.CharField(required=False, help_text="Код ошибки для программной обработки")
    details = serializers.DictField(required=False, help_text="Дополнительные детали ошибки")
