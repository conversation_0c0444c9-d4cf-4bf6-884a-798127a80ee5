# Django Configuration
SECRET_KEY=your-super-secret-django-key-change-in-production
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Database Configuration
DB_NAME=vpn_service
DB_USER=postgres
DB_PASSWORD=postgres
DB_HOST=localhost
DB_PORT=5432

# Redis Configuration
REDIS_URL=redis://localhost:6379/1

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Hiddify Manager API Configuration
HIDDIFY_ADMIN_API_KEY=your-hiddify-admin-api-key
HIDDIFY_ADMIN_BASE_URL=https://ductuspro.ru/your-admin-uuid
HIDDIFY_USER_BASE_URL=https://ductuspro.ru/your-user-uuid
HIDDIFY_API_TIMEOUT=30
HIDDIFY_MAX_RETRY_ATTEMPTS=3
HIDDIFY_RATE_LIMIT_PER_MINUTE=60

# JWT Settings
JWT_ACCESS_TOKEN_LIFETIME=1  # hours
JWT_REFRESH_TOKEN_LIFETIME=7  # days

# CORS Settings
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# Email Configuration (for production)
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
DEFAULT_FROM_EMAIL=<EMAIL>

# Security Settings (for production)
SECURE_SSL_REDIRECT=False
SESSION_COOKIE_SECURE=False
CSRF_COOKIE_SECURE=False
