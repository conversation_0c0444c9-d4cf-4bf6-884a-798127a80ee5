

### Сценарий 1: "Анонимный разведчик"

*   **Пользовательская история:** "Как новый пользователь, я хочу попробовать VPN бесплатно и без регистрации, чтобы оценить его скорость и удобство."
*   **Приемочные критерии:**
    1.  При первом запуске приложение не требует email или пароль.
    2.  Система автоматически предоставляет мне доступ к бесплатному тарифу.
    3.  Я могу подключиться к ограниченному набору бесплатных локаций.
    4.  Если я удалю и снова установлю приложение, мой доступ к бесплатному тарифу должен восстановиться автоматически.
*   **Сквозной пример использования:**
    1.  Анна скачивает и открывает приложение на своем iPhone.
    2.  Приложение на клиенте генерирует `device_id` и сохраняет его в Keychain.
    3.  Клиент отправляет `POST /devices/register` с `device_id`.
    4.  **Бэкенд:**
        *   Создает новую запись `Device`.
        *   Создает `device_secret` и возвращает его клиенту для сохранения в Keychain.
        *   Находит `TariffPlan` с флагом `is_free_default=true`.
        *   Создает анонимную `Subscription` (`user_id = NULL`), связанную с этим тарифом.
        *   Создает запись в `SubscriptionDevice`, связывая новую подписку и устройство Анны.
        *   Возвращает "анонимный" JWT.
    5.  Анна нажимает "Подключиться" и успешно выходит в интернет через бесплатный сервер.
*   **Влияние на модели данных:**
    *   **`Device`**: Нужны поля `device_id` и `device_secret`.
    *   **`TariffPlan`**: Нужен флаг `is_free_default`.
    *   **`Subscription`**: `user_id` должен быть `NULLABLE`.
    *   **`SubscriptionDevice`**: Необходима для связи анонимных подписок с устройствами.

---

### Сценарий 2: "Анонимный покупатель"

*   **Пользовательская история:** "Как анонимный пользователь, которому понравился сервис, я хочу купить премиум-подписку без создания аккаунта и быть уверенным, что не потеряю ее, если сменю телефон."
*   **Приемочные критерии:**
    1.  Я могу выбрать платный тариф и оплатить его (через промокод с сайта).
    2.  После активации промокода мой тариф должен немедленно обновиться на премиальный.
    3.  Приложение должно настоятельно предложить мне сохранить код восстановления подписки.
    4.  Используя код восстановления, я должен иметь возможность перенести свою подписку на новый телефон.
*   **Сквозной пример использования:**
    1.  Анна (из прошлого сценария) решает купить тариф "Премиум" на 1 месяц.
    2.  Приложение перенаправляет ее на сайт, где она покупает промокод.
    3.  В приложении она вводит промокод в специальное поле.
    4.  Клиент отправляет `POST /subscriptions/activate` с `promo_code`.
    5.  **Бэкенд:**
        *   Находит `PromoCode`, проверяет его.
        *   Находит анонимную подписку Анны (по `device_id` из JWT).
        *   **Деактивирует** ее старую бесплатную подписку.
        *   Создает новую `Subscription` (с `user_id = NULL`) на основе тарифа из промокода.
        *   Генерирует `recovery_code` для этой подписки.
        *   Привязывает подписку к устройству Анны через `SubscriptionDevice`.
    6.  Бэкенд возвращает `200 OK` и `recovery_code`.
    7.  Приложение показывает Анне `recovery_code` и просит его сохранить.
*   **Влияние на модели данных:**
    *   **`PromoCode`**: Модель для хранения и управления промокодами.
    *   **`Subscription`**: Нужна связь с `TariffPlan`, `status` (active/expired) и поле для **`recovery_code`** (или лучше вынести в отдельную сущность, но для простоты можно и здесь).

---

### Сценарий 3: "Осторожный пользователь"

*   **Пользовательская история:** "Как анонимный пользователь с платной подпиской, я хочу создать полноценный аккаунт с email и паролем, чтобы управлять подпиской с нескольких устройств и не бояться потерять ее."
*   **Приемочные критерии:**
    1.  В приложении есть возможность зарегистрироваться, даже если я уже использую его анонимно.
    2.  После регистрации моя текущая активная подписка должна автоматически привязаться к моему новому аккаунту.
    3.  Мой "анонимный" статус должен безболезненно превратиться в "зарегистрированный".
*   **Сквозной пример использования:**
    1.  Анна, пользуясь премиум-подпиской, решает создать аккаунт.
    2.  Она вводит свой `email` и `password` и нажимает "Зарегистрироваться".
    3.  Клиент отправляет `POST /auth/register`, используя в заголовке свой "анонимный" JWT.
    4.  **Бэкенд:**
        *   По JWT определяет `device_id` Анны.
        *   Создает нового `User` с ее email и хэшем пароля.
        *   Находит ее `Device` по `device_id` и **заполняет** у него поле `user_id`.
        *   Находит ее активную `Subscription` (которая связана с ее `device_id` и имеет `user_id = NULL`).
        *   **Заполняет** у этой подписки поле `user_id` ID нового пользователя.
        *   Возвращает **новую пару** `access/refresh` токенов с `tokenType: "registered"`.
    5.  Приложение Анны сохраняет новые токены, и теперь она — зарегистрированный пользователь.
*   **Влияние на модели данных:**
    *   **`User`**: Стандартная модель пользователя.
    *   **`Device`**: Поле `user_id` (FK к `User`) должно быть `NULLABLE`.
    *   **`Subscription`**: Поле `user_id` (FK к `User`) должно быть `NULLABLE`.

---

### Сценарий 4: "Семейный доступ"

*   **Пользовательская история:** "Как пользователь с премиум-тарифом на 5 устройств, я хочу легко поделиться доступом со своим партнером, отправив ему код-приглашение."
*   **Приемочные критерии:**
    1.  В моем профиле я могу видеть, сколько устройств подключено и сколько слотов свободно.
    2.  Я могу сгенерировать временный код-приглашение.
    3.  Мой партнер может установить приложение и активировать доступ с помощью моего кода.
    4.  Устройство партнера должно появиться в моем списке подключенных устройств.
*   **Сквозной пример использования:**
    1.  Анна (зарегистрированный пользователь) хочет подключить телефон своего мужа, Бориса.
    2.  В приложении она нажимает "Пригласить устройство" -> "Сгенерировать код".
    3.  Клиент отправляет `GET /devices/activation-code`.
    4.  **Бэкенд:**
        *   Проверяет, что у подписки Анны есть свободные слоты (`count(devices) < tariff.max_devices`).
        *   Создает запись в таблице **`ActivationCode`**, связывая ее с подпиской Анны и устанавливая TTL 10 минут.
        *   Возвращает код.
    5.  Борис устанавливает приложение, выбирает "Активировать по коду" и вводит код от Анны.
    6.  Клиент Бориса (с его собственным `device_id`) отправляет `POST /devices/activate` с кодом.
    7.  **Бэкенд:**
        *   Находит `ActivationCode`, проверяет его.
        *   Находит подписку Анны, связанную с кодом.
        *   Добавляет новую запись в `SubscriptionDevice`, связывая подписку Анны и устройство Бориса.
        *   Помечает код как использованный.
    8.  Борис получает доступ. В профиле Анны теперь видно 2/5 подключенных устройств.
*   **Влияние на модели данных:**
    *   **`TariffPlan`**: Поле `max_devices`.
    *   **`ActivationCode`**: Отдельная, временная таблица для хранения кодов-приглашений.
    *   **`SubscriptionDevice`**: Подтверждает свою необходимость для связи "одна подписка - много устройств".