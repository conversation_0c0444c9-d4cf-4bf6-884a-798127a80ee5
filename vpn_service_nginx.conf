# Nginx конфигурация для VPN Service API (Production)
# Обеспечивает доступ к API endpoints через интернет

# Основной HTTP сервер для API (порт 8090 для внешнего доступа)
server {
    listen 8090;
    server_name api.ductuspro.ru ductuspro.ru;

    # Security headers
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # Общие настройки проксирования
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Forwarded-Host $host;
    proxy_set_header X-Forwarded-Port $server_port;
    
    # Таймауты
    proxy_connect_timeout 30s;
    proxy_send_timeout 30s;
    proxy_read_timeout 30s;
    
    # Буферизация
    proxy_buffering on;
    proxy_buffer_size 4k;
    proxy_buffers 8 4k;

    # Rate limiting настроен в основном nginx.conf

    # Основное проксирование к Gunicorn на порту 8002
    location / {
        proxy_pass http://127.0.0.1:8002;
        
        # CORS заголовки для публичного API
        add_header 'Access-Control-Allow-Origin' '*' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS, PATCH' always;
        add_header 'Access-Control-Allow-Headers' 'Accept, Authorization, Content-Type, X-Requested-With, X-CSRF-Token' always;
        add_header 'Access-Control-Allow-Credentials' 'true' always;
        
        # Обработка preflight запросов
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS, PATCH';
            add_header 'Access-Control-Allow-Headers' 'Accept, Authorization, Content-Type, X-Requested-With, X-CSRF-Token';
            add_header 'Access-Control-Max-Age' 1728000;
            add_header 'Content-Type' 'text/plain; charset=utf-8';
            add_header 'Content-Length' 0;
            return 204;
        }

        # Rate limiting отключен для упрощения
    }

    # API endpoints с дополнительной защитой
    location /api/ {
        proxy_pass http://127.0.0.1:8002/api/;
        
        # CORS заголовки
        add_header 'Access-Control-Allow-Origin' '*' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS, PATCH' always;
        add_header 'Access-Control-Allow-Headers' 'Accept, Authorization, Content-Type, X-Requested-With, X-CSRF-Token' always;
        add_header 'Access-Control-Allow-Credentials' 'true' always;
        
        # Rate limiting отключен для упрощения
    }

    # Аутентификация endpoints с более строгим rate limiting
    location /api/auth/ {
        proxy_pass http://127.0.0.1:8002/api/auth/;
        
        # CORS заголовки
        add_header 'Access-Control-Allow-Origin' '*' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS, PATCH' always;
        add_header 'Access-Control-Allow-Headers' 'Accept, Authorization, Content-Type, X-Requested-With, X-CSRF-Token' always;
        add_header 'Access-Control-Allow-Credentials' 'true' always;
        
        # Rate limiting отключен для упрощения
    }

    # Swagger UI и документация
    location /api/docs/ {
        proxy_pass http://127.0.0.1:8002/api/docs/;
        
        # Дополнительные заголовки для Swagger UI
        proxy_set_header Accept-Encoding "";
        
        # CORS заголовки
        add_header 'Access-Control-Allow-Origin' '*' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS, PATCH' always;
        add_header 'Access-Control-Allow-Headers' 'Accept, Authorization, Content-Type, X-Requested-With' always;
    }

    # Health check endpoint
    location /health/ {
        proxy_pass http://127.0.0.1:8002/health/;
        access_log off;
    }

    # Статические файлы
    location /static/ {
        proxy_pass http://127.0.0.1:8002/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    location /media/ {
        proxy_pass http://127.0.0.1:8002/media/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Логирование
    access_log /var/log/nginx/vpn_service_api_access.log;
    error_log /var/log/nginx/vpn_service_api_error.log;

    # Максимальный размер загружаемых файлов
    client_max_body_size 10M;

    # Сжатие ответов
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
}


