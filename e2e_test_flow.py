#!/usr/bin/env python
"""
Скрипт для сквозного тестирования (E2E) VPN-сервиса.

PURPOSE:
  - Имитирует действия мобильного приложения
  - Тестирует полный цикл: анонимный пользователь -> зарегистрированный пользователь
  - Проверяет работоспособность всех ключевых API endpoints
  - Обеспечивает быструю диагностику проблем системы

SCENARIO: "Анонимный разведчик -> Осторожный пользователь"
  1. Регистрация анонимного пользователя
  2. Получение VPN конфигурации анонимом
  3. Конвертация в зарегистрированного пользователя
  4. Получение VPN конфигурации зарегистрированным пользователем

USAGE:
  python e2e_test_flow.py

REQUIREMENTS:
  - Django сервер должен быть запущен
  - База данных должна содержать тариф по умолчанию
  - Библиотека requests должна быть установлена
"""

import requests
import json
import sys
from datetime import datetime

# Конфигурация
SERVER_URL = "http://localhost:8090"  # URL Django сервера
TIMEOUT = 10  # Таймаут для HTTP запросов в секундах

# Глобальные переменные для хранения данных между шагами
test_data = {
    'anonymous_tokens': {},
    'registered_tokens': {},
    'device_secret': None,
    'test_email': None,
    'test_password': 'TestPassword123!'
}

def print_step(step_num, description):
    """Выводит заголовок шага."""
    print(f"\n{'='*60}")
    print(f"Шаг {step_num}: {description}")
    print('='*60)

def print_success(message):
    """Выводит сообщение об успехе."""
    print(f"✅ {message}")

def print_error(message):
    """Выводит сообщение об ошибке."""
    print(f"❌ {message}")

def print_info(message):
    """Выводит информационное сообщение."""
    print(f"ℹ️  {message}")

def make_request(method, endpoint, headers=None, data=None, expected_status=None):
    """
    Выполняет HTTP запрос с обработкой ошибок.
    
    ARGS:
        method (str): HTTP метод (GET, POST, etc.)
        endpoint (str): API endpoint (например, '/api/auth/devices/register/')
        headers (dict): HTTP заголовки
        data (dict): Данные для отправки
        expected_status (int): Ожидаемый статус код
    
    RETURNS:
        tuple: (success: bool, response: requests.Response or None)
    """
    url = f"{SERVER_URL}{endpoint}"
    headers = headers or {}
    
    try:
        print_info(f"Отправка {method} запроса на {endpoint}")
        if data:
            print_info(f"Данные: {json.dumps(data, indent=2, ensure_ascii=False)}")
        
        if method.upper() == 'GET':
            response = requests.get(url, headers=headers, timeout=TIMEOUT)
        elif method.upper() == 'POST':
            headers['Content-Type'] = 'application/json'
            response = requests.post(url, headers=headers, json=data, timeout=TIMEOUT)
        else:
            print_error(f"Неподдерживаемый HTTP метод: {method}")
            return False, None
        
        print_info(f"Статус ответа: {response.status_code}")
        
        # Проверяем статус код
        if expected_status and response.status_code != expected_status:
            print_error(f"Неожиданный статус код. Ожидался: {expected_status}, получен: {response.status_code}")
            print_error(f"Тело ответа: {response.text}")
            return False, response
        
        # Пытаемся распарсить JSON
        try:
            response_data = response.json()
            print_info(f"Ответ: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
        except:
            print_info(f"Ответ (не JSON): {response.text}")
        
        return True, response
        
    except requests.exceptions.Timeout:
        print_error(f"Таймаут запроса к {url}")
        return False, None
    except requests.exceptions.ConnectionError:
        print_error(f"Ошибка подключения к {url}. Убедитесь, что Django сервер запущен.")
        return False, None
    except Exception as e:
        print_error(f"Неожиданная ошибка: {e}")
        return False, None

def step1_register_anonymous():
    """Шаг 1: Регистрация анонимного пользователя."""
    print_step(1, "Регистрация анонимного пользователя")

    # Генерируем уникальный device_id для тестирования
    import uuid
    device_id = str(uuid.uuid4())

    data = {
        'device_id': device_id,
        'device_name': 'E2E Test Device',
        'device_type': 'android'  # Используем валидное значение из choices
    }

    success, response = make_request(
        'POST',
        '/api/auth/device/init/',
        data=data,
        expected_status=201
    )
    
    if not success:
        print_error("Провал регистрации анонимного пользователя")
        return False
    
    try:
        data = response.json()
        # Токены находятся в объекте tokens
        tokens = data.get('tokens', {})
        test_data['anonymous_tokens'] = {
            'access_token': tokens.get('access'),
            'refresh_token': tokens.get('refresh')
        }
        test_data['device_secret'] = data.get('device_secret')

        print_success("Анонимный пользователь успешно зарегистрирован")
        print_info(f"Access token получен: {test_data['anonymous_tokens']['access_token'][:20]}...")
        print_info(f"Refresh token получен: {test_data['anonymous_tokens']['refresh_token'][:20]}...")
        if test_data['device_secret']:
            print_info(f"Device secret получен: {test_data['device_secret'][:20]}...")

        return True

    except Exception as e:
        print_error(f"Ошибка обработки ответа: {e}")
        return False

def step2_get_config_anonymous():
    """Шаг 2: Запрос VPN конфигурации анонимным пользователем."""
    print_step(2, "Запрос VPN конфигурации анонимом")
    
    headers = {
        'Authorization': f"Bearer {test_data['anonymous_tokens']['access_token']}"
    }
    
    success, response = make_request(
        'GET',
        '/api/vpn/config/',
        headers=headers,
        expected_status=200
    )
    
    if not success:
        print_error("Провал получения VPN конфигурации анонимом")
        return False
    
    try:
        config_data = response.json()
        print_success("VPN конфигурация успешно получена анонимом")
        print_info(f"Размер конфигурации: {len(json.dumps(config_data))} символов")
        
        # Проверяем основные поля SingBox конфигурации
        if 'outbounds' in config_data:
            print_info(f"Найдено outbounds: {len(config_data['outbounds'])}")
        if 'inbounds' in config_data:
            print_info(f"Найдено inbounds: {len(config_data['inbounds'])}")
            
        return True
        
    except Exception as e:
        print_error(f"Ошибка обработки конфигурации: {e}")
        return False

def step3_convert_to_registered():
    """Шаг 3: Конвертация анонимного пользователя в зарегистрированного."""
    print_step(3, "Конвертация в зарегистрированного пользователя")
    
    # Генерируем уникальный email для тестирования
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    test_data['test_email'] = f"test_user_{timestamp}@example.com"
    
    headers = {
        'Authorization': f"Bearer {test_data['anonymous_tokens']['access_token']}"
    }
    
    data = {
        'email': test_data['test_email'],
        'password': test_data['test_password'],
        'password_confirm': test_data['test_password']
    }
    
    success, response = make_request(
        'POST',
        '/api/auth/register/',
        headers=headers,
        data=data,
        expected_status=200
    )
    
    if not success:
        print_error("Провал конвертации в зарегистрированного пользователя")
        return False
    
    try:
        data = response.json()
        # Токены могут быть в объекте tokens или напрямую в data
        if 'tokens' in data:
            tokens = data['tokens']
            test_data['registered_tokens'] = {
                'access_token': tokens.get('access'),
                'refresh_token': tokens.get('refresh')
            }
        else:
            # Если токены напрямую в ответе
            test_data['registered_tokens'] = {
                'access_token': data.get('access_token') or data.get('access'),
                'refresh_token': data.get('refresh_token') or data.get('refresh')
            }

        print_success("Успешная конвертация в зарегистрированного пользователя")
        print_info(f"Email: {test_data['test_email']}")
        print_info(f"Новый access token: {test_data['registered_tokens']['access_token'][:20]}...")
        print_info(f"Новый refresh token: {test_data['registered_tokens']['refresh_token'][:20]}...")

        return True

    except Exception as e:
        print_error(f"Ошибка обработки ответа: {e}")
        return False

def step4_get_config_registered():
    """Шаг 4: Запрос VPN конфигурации зарегистрированным пользователем."""
    print_step(4, "Запрос VPN конфигурации зарегистрированным пользователем")
    
    headers = {
        'Authorization': f"Bearer {test_data['registered_tokens']['access_token']}"
    }
    
    success, response = make_request(
        'GET',
        '/api/vpn/config/',
        headers=headers,
        expected_status=200
    )
    
    if not success:
        print_error("Провал получения VPN конфигурации зарегистрированным пользователем")
        return False
    
    try:
        config_data = response.json()
        print_success("VPN конфигурация успешно получена зарегистрированным пользователем")
        print_info(f"Размер конфигурации: {len(json.dumps(config_data))} символов")
        
        # Проверяем основные поля SingBox конфигурации
        if 'outbounds' in config_data:
            print_info(f"Найдено outbounds: {len(config_data['outbounds'])}")
        if 'inbounds' in config_data:
            print_info(f"Найдено inbounds: {len(config_data['inbounds'])}")
            
        return True
        
    except Exception as e:
        print_error(f"Ошибка обработки конфигурации: {e}")
        return False

def main():
    """Основная функция тестирования."""
    print("🚀 Запуск сквозного тестирования VPN-сервиса")
    print(f"🌐 Сервер: {SERVER_URL}")
    print(f"⏱️  Таймаут: {TIMEOUT} секунд")
    
    # Выполняем все шаги последовательно
    steps = [
        step1_register_anonymous,
        step2_get_config_anonymous,
        step3_convert_to_registered,
        step4_get_config_registered
    ]
    
    for i, step_func in enumerate(steps, 1):
        if not step_func():
            print(f"\n❌ Тестирование прервано на шаге {i}")
            print("🔧 Проверьте:")
            print("   - Запущен ли Django сервер на порту 8090")
            print("   - Создан ли тариф по умолчанию (python setup_initial_data.py)")
            print("   - Корректны ли настройки базы данных")
            sys.exit(1)
    
    # Все шаги выполнены успешно
    print(f"\n{'='*60}")
    print("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ УСПЕШНО!")
    print('='*60)
    print("✅ Анонимная регистрация работает")
    print("✅ Получение VPN конфигурации анонимом работает")
    print("✅ Конвертация в зарегистрированного пользователя работает")
    print("✅ Получение VPN конфигурации зарегистрированным пользователем работает")
    
    print(f"\n📊 Данные тестирования:")
    print(f"   Email: {test_data['test_email']}")
    print(f"   Password: {test_data['test_password']}")
    
    print(f"\n🔧 Система готова к использованию!")

if __name__ == "__main__":
    main()
